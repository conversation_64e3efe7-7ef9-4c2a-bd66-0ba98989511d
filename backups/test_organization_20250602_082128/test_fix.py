"""
Test script to verify the fix for the visualization error.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import our custom models
import sys
import os

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tci_premium import TCIPremiumPredictor
from folder_data_manager import FolderDataManager

def test_visualization_fix():
    """
    Test the fix for the visualization error.
    """
    logger.info("Initializing data manager")
    data_manager = FolderDataManager(dataset_folder="dataset")
    
    # Load data from folder
    success, message = data_manager.load_data_from_folder()
    if not success:
        logger.error(f"Failed to load data from folder: {message}")
        return
    
    logger.info(f"Loaded data for {len(data_manager.customers)} customers")
    
    logger.info("Initializing TCI Premium predictor")
    tci_premium = TCIPremiumPredictor()
    
    # Use a specific customer and product
    customer = "ADVIK (IND)"
    product = "MECHANICAL SEAL ( BB-0030451 )"
    logger.info(f"Using customer: {customer}")
    logger.info(f"Using product: {product}")
    
    # Train the model
    logger.info(f"Training model for {customer} - {product}")
    try:
        # Load data
        data = data_manager.get_product_data(customer, product)
        
        # Check if data was loaded correctly
        if data is None or data.empty:
            logger.error(f"Failed to load data for {customer} - {product}")
            return
            
        logger.info(f"Loaded data with {len(data)} rows")
        
        # Convert to the format expected by TCI Premium
        data['ds'] = pd.to_datetime(data['date'])
        data['y'] = data['quantity']
        
        # Train the model
        success, message = tci_premium.train_model(
            data=data,
            customer=customer,
            product=product,
            optimize_hyperparams=True
        )
        
        if success:
            logger.info(f"Successfully trained model: {message}")
            
            # Generate forecast
            logger.info("Generating forecast")
            forecast = tci_premium.predict_future(customer, product, periods=36)  # 3 years (36 months)
            
            if forecast is not None and not forecast.empty:
                logger.info(f"Successfully generated forecast with {len(forecast)} periods")
                logger.info("Test completed successfully!")
            else:
                logger.error("Failed to generate forecast")
        else:
            logger.error(f"Failed to train model: {message}")
    except Exception as e:
        logger.error(f"Error testing visualization fix: {str(e)}")

if __name__ == "__main__":
    test_visualization_fix()
