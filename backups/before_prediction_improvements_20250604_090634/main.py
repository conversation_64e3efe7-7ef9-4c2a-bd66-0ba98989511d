"""
Historical Customer Order Prediction System

This is the main application that brings everything together - the user interface,
data management, and prediction models. It helps businesses understand their
customers' ordering patterns and predict future orders based on historical data.

The system is designed to be user-friendly with tabs for:
- Data Management: Load and view customer order data
- Data Analysis: Analyze ordering patterns and trends
- Prediction: Generate forecasts for future orders

Author: Harry
Version: 1.0
"""

# Standard library imports - basic Python functionality
import sys
import os
import csv
import json
import logging
import traceback
from datetime import datetime

# Data analysis libraries - the workhorses of our application
import pandas as pd      # For data manipulation and analysis
import numpy as np       # For numerical operations
import matplotlib.pyplot as plt  # For creating visualizations

# PyQt5 imports - for building our user interface
from PyQt5.QtWidgets import QApplication, QMessageBox, QFileDialog, QHeaderView, QTableWidgetItem, QTableWidget, QGroupBox, QHBoxLayout, QPushButton, QWidget, QVBoxLayout, QFormLayout, QComboBox, QFrame, QSizePolicy, QScrollArea
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QColor
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# Our custom modules - the building blocks of our application
from folder_data_manager import FolderDataManager       # Handles loading and processing data
from prophet_model import ProphetOrderPredictor         # Makes predictions using Prophet
from tci_model import TCIOrderPredictor                # Makes predictions using TCI
from gui_base import MainWindow, PandasTableModel       # Defines our user interface

# Use enhanced logging system if available
try:
    from enhanced_logging import get_logger
    logger = get_logger(__name__)
    logger.info("Using enhanced logging system")
    # Set the exception hook to log uncaught exceptions
    from enhanced_logging import log_exception
    import sys
    sys.excepthook = log_exception
except ImportError:
    # Fall back to basic logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("app.log"),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)
    logger.info("Using basic logging system")

class OrderPredictionApp(MainWindow):
    """Main application class for the Historical Customer Order Prediction System.

    This class brings together all components of our application:
    - The user interface (inherited from MainWindow)
    - The data manager (for loading and processing data)
    - The prediction models (Prophet and XGBoost)

    It handles all the business logic and connects user actions to the appropriate
    functionality in the underlying components.
    """

    def __init__(self):
        """Set up the application by initializing all components.

        This method is called when the application starts. It:
        1. Sets up the user interface (through the parent class)
        2. Creates the data manager to handle our dataset
        3. Initializes the prediction models
        4. Sets default values and connects signals to slots
        """
        # Create our data manager - this handles loading and processing data
        # We point it to the 'dataset' folder where our customer data is stored
        self.data_manager = FolderDataManager(dataset_folder="dataset")

        # Initialize the parent class (MainWindow) which sets up the UI
        # Pass our data_manager to the parent class
        super().__init__(data_manager=self.data_manager)

        # Initialize our prediction models
        self.prophet_model = ProphetOrderPredictor()  # Facebook Prophet model
        self.tci_model = TCIOrderPredictor()         # TCI model
        self.model = self.prophet_model  # Default to Prophet as our active model

        # Initialize variables to store selections for each tab
        self.data_tab_selections = {'customer': '', 'product': ''}
        self.analysis_tab_selections = {'customer': '', 'product': ''}
        self.prediction_tab_selections = {'customer': '', 'product': ''}

        # Connect signals to slots
        self.connect_signals()

        # Connect model selection combo box
        self.model_selection_combo.currentTextChanged.connect(self.on_model_selection_changed)

        # Automatically load data from folder structure on startup
        QTimer.singleShot(500, self.load_data_from_folder)

        logger.info("Application initialized")

    def connect_signals(self):
        """Connect UI elements to their corresponding functions.

        This is where we wire up the user interface - connecting buttons, dropdowns,
        and other UI elements to the functions that should run when the user interacts
        with them. This creates the interactive behavior of our application.
        """
        # Tab navigation - what happens when the user switches tabs
        self.tabs.currentChanged.connect(self.on_tab_changed)

        # Data refresh button - reload data from the dataset folder
        self.refresh_btn.clicked.connect(self.load_data_from_folder)

        # Process all data button - process raw data files
        self.process_all_btn.clicked.connect(self.process_all_data)

        # Customer selection synchronization across tabs
        # When a customer is selected in one tab, we want to remember that selection
        # This makes the UI more intuitive as selections persist between tab switches
        self.data_customer_combo.currentTextChanged.connect(
            lambda text: self.on_customer_changed(text, 'data'))
        self.analysis_customer_combo.currentTextChanged.connect(
            lambda text: self.on_customer_changed(text, 'analysis'))
        self.prediction_customer_combo.currentTextChanged.connect(
            lambda text: self.on_customer_changed(text, 'prediction'))
        # Metrics tab signals - hidden for now
        # self.metrics_customer_combo.currentTextChanged.connect(
        #     lambda text: self.on_customer_changed(text, 'metrics'))
        # self.metrics_product_combo.currentTextChanged.connect(
        #     lambda text: self.on_product_changed(text, 'metrics'))
        # self.calculate_metrics_btn.clicked.connect(self.calculate_metrics)
        # self.plot_metrics_btn.clicked.connect(self.plot_metrics)
        # self.export_metrics_btn.clicked.connect(self.export_metrics_data)

        # Connect product selection changes
        self.data_product_combo.currentTextChanged.connect(lambda text: self.on_product_changed(text, 'data'))
        self.analysis_product_combo.currentTextChanged.connect(lambda text: self.on_product_changed(text, 'analysis'))
        self.prediction_product_combo.currentTextChanged.connect(lambda text: self.on_product_changed(text, 'prediction'))

        # Prediction tab additional connections
        self.prediction_interval_check.stateChanged.connect(self.update_prediction_display)

    def on_model_selection_changed(self, model_name):
        """Handle model selection change."""
        logger.info(f"Model selection changed to: '{model_name}'")

        # Debug: Print exact representation of the model name
        logger.info(f"Model name (repr): {repr(model_name)}")

        if model_name == "TCI":
            self.model = self.tci_model
            logger.info("Switched to TCI model")
            # Disable global model checkbox for TCI
            self.use_global_model_checkbox.setEnabled(False)
            self.use_global_model_checkbox.setChecked(False)
        # Ensemble model removed
        elif model_name == "TCI Premium":
            # Use the model from model_integration
            self.model = self.model_integration.tci_premium_predictor
            logger.info("Switched to TCI Premium model")
            # Disable global model checkbox for TCI Premium
            self.use_global_model_checkbox.setEnabled(False)
            self.use_global_model_checkbox.setChecked(False)
            # Enable hyperparameter optimization checkbox for TCI Premium
            self.optimize_hyperparams_checkbox.setEnabled(True)
        elif model_name == "TCI-fix":
            # Use the TCI-fix model from model_integration
            if hasattr(self.model_integration, 'tci_fix_predictor'):
                self.model = self.model_integration.tci_fix_predictor
                logger.info("Switched to TCI-fix model")
                # Disable global model checkbox for TCI-fix
                self.use_global_model_checkbox.setEnabled(False)
                self.use_global_model_checkbox.setChecked(False)
                # Enable hyperparameter optimization checkbox for TCI-fix
                self.optimize_hyperparams_checkbox.setEnabled(True)
            else:
                logger.error("TCI-fix predictor not found in model_integration")
                QMessageBox.warning(self, "Model Not Available", "The TCI-fix model is not properly initialized. Please check the logs for details.")
        else:
            logger.warning(f"Unknown model name: '{model_name}'")
            # Disable global model checkbox for unknown models
            self.use_global_model_checkbox.setEnabled(False)
            self.use_global_model_checkbox.setChecked(False)

        # All models now use frequency-based predictions by default

        # Clear any existing forecasts
        if hasattr(self, 'prediction_canvas_combined'):
            self.prediction_canvas_combined.clear()

        if hasattr(self, 'prediction_canvas_separate'):
            self.prediction_canvas_separate.clear()

    def on_tab_changed(self, index):
        """Handle tab change events."""
        tab_name = self.tabs.tabText(index)
        logger.info(f"Switched to {tab_name} tab")

        # Store current selections before updating
        current_customer = None
        current_product = None

        # Get current selections from the active tab
        if self.tabs.currentIndex() == 0:  # Data Management tab
            current_customer = self.data_customer_combo.currentText()
            current_product = self.data_product_combo.currentText()
        elif self.tabs.currentIndex() == 1:  # Data Analysis tab
            current_customer = self.analysis_customer_combo.currentText()
            current_product = self.analysis_product_combo.currentText()
        elif self.tabs.currentIndex() == 2:  # Prediction tab
            current_customer = self.prediction_customer_combo.currentText()
            current_product = self.prediction_product_combo.currentText()
        # Metrics tab hidden for now
        # elif self.tabs.currentIndex() == 3:  # Metrics tab
        #     current_customer = self.metrics_customer_combo.currentText()
        #     current_product = self.metrics_product_combo.currentText()

        # Update combos when switching to tabs
        if tab_name == "Data Management":
            self.update_data_customers(current_customer, current_product)
        elif tab_name == "Data Analysis":
            self.update_analysis_combos(current_customer, current_product)
        elif tab_name == "Prediction":
            self.update_prediction_combos(current_customer, current_product)
        # Metrics tab hidden for now
        # elif tab_name == "Metrics":
        #     self.update_metrics_combos(current_customer, current_product)

    def load_data_from_folder(self):
        """Load data from folder structure."""
        # Store current selections before loading new data
        current_tab = self.tabs.currentIndex()

        # Initialize variables with default values
        data_customer = None
        data_product = None
        analysis_customer = None
        analysis_product = None
        prediction_customer = None
        prediction_product = None
        # Metrics tab hidden for now
        # metrics_customer = None
        # metrics_product = None

        # Get current selections from all tabs if the combos exist and are initialized
        if hasattr(self, 'data_customer_combo') and self.data_customer_combo is not None:
            data_customer = self.data_customer_combo.currentText()
            data_product = self.data_product_combo.currentText()

        if hasattr(self, 'analysis_customer_combo') and self.analysis_customer_combo is not None:
            analysis_customer = self.analysis_customer_combo.currentText()
            analysis_product = self.analysis_product_combo.currentText()

        if hasattr(self, 'prediction_customer_combo') and self.prediction_customer_combo is not None:
            prediction_customer = self.prediction_customer_combo.currentText()
            prediction_product = self.prediction_product_combo.currentText()

        # Metrics tab hidden for now
        # if hasattr(self, 'metrics_customer_combo') and self.metrics_customer_combo is not None:
        #     metrics_customer = self.metrics_customer_combo.currentText()
        #     metrics_product = self.metrics_product_combo.currentText()

        # Show progress
        self.status_label.setText("Loading data from folders...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(10)
        QApplication.processEvents()

        # Load data
        success, message = self.data_manager.load_data_from_folder()

        if not success:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "Error", message)
            self.status_label.setText("Data loading failed")
            return

        # Update progress
        self.progress_bar.setValue(50)
        QApplication.processEvents()

        # Update data info
        self.update_data_info()

        # Update combos while preserving selections
        self.update_data_customers(data_customer, data_product)
        self.update_analysis_combos(analysis_customer, analysis_product)
        self.update_prediction_combos(prediction_customer, prediction_product)
        # Metrics tab hidden for now
        # self.update_metrics_combos(metrics_customer, metrics_product)

        # Hide progress
        self.progress_bar.setValue(100)
        self.status_label.setText(message)

        # Hide progress bar after a delay
        QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        logger.info(f"Data loaded: {message}")

    def load_data(self):
        """Load data from selected file."""
        file_path = self.file_path_label.text()

        if file_path == "No file selected":
            QMessageBox.warning(self, "Warning", "Please select a data file first")
            return

        # Show progress
        self.status_label.setText("Loading data...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(10)
        QApplication.processEvents()

        # Load data
        success, message = self.data_manager.load_data(file_path)

        if not success:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "Error", message)
            self.status_label.setText("Data loading failed")
            return

        # Update progress
        self.progress_bar.setValue(50)
        QApplication.processEvents()

        # Update data info
        self.update_data_info()

        # Update combos
        self.update_data_customers()
        self.update_analysis_combos()
        self.update_prediction_combos()

        # Hide progress
        self.progress_bar.setValue(100)
        self.status_label.setText(message)

        # Hide progress bar after a delay.
        QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        logger.info(f"Data loaded: {message}")

    def update_data_info(self):
        """Update data information display."""
        if self.data_manager.stats:
            stats = self.data_manager.stats

            self.records_label.setText(str(stats['record_count']))
            self.customers_label.setText(str(stats['customer_count']))
            self.products_label.setText(str(stats['product_count']))

            date_range = (f"{stats['date_range']['start'].strftime('%Y-%m-%d')} to "
                         f"{stats['date_range']['end'].strftime('%Y-%m-%d')} "
                         f"({stats['date_range']['days']} days)")
            self.date_range_label.setText(date_range)

    def update_data_customers(self, selected_customer=None, selected_product=None):
        """Update customer list in data tab."""
        # Get customers
        customers = self.data_manager.get_customers()

        # Update customer combo
        self.data_customer_combo.clear()
        if customers:
            self.data_customer_combo.addItems(customers)

            # Set the previously selected customer if it exists
            if selected_customer and selected_customer in customers:
                index = self.data_customer_combo.findText(selected_customer)
                if index >= 0:
                    self.data_customer_combo.setCurrentIndex(index)

                    # Also update products and select the previously selected product
                    if selected_product:
                        products = self.data_manager.get_products(selected_customer)
                        if selected_product in products:
                            self.update_data_products()
                            index = self.data_product_combo.findText(selected_product)
                            if index >= 0:
                                self.data_product_combo.setCurrentIndex(index)
                                # Update the preview with the selected customer and product
                                self.update_data_preview()

    def on_customer_changed(self, customer, source_tab):
        """Handle customer selection change and synchronize across tabs."""
        if not customer:
            return

        # Skip if we're in the middle of programmatically updating combos
        if hasattr(self, '_updating_combos') and self._updating_combos:
            return

        # Set flag to prevent recursive updates
        self._updating_combos = True

        try:
            # Update the customer selection in all tabs except the source
            if source_tab != 'data' and customer != self.data_customer_combo.currentText():
                index = self.data_customer_combo.findText(customer)
                if index >= 0:
                    self.data_customer_combo.setCurrentIndex(index)

            if source_tab != 'analysis' and customer != self.analysis_customer_combo.currentText():
                index = self.analysis_customer_combo.findText(customer)
                if index >= 0:
                    self.analysis_customer_combo.setCurrentIndex(index)

            if source_tab != 'prediction' and customer != self.prediction_customer_combo.currentText():
                index = self.prediction_customer_combo.findText(customer)
                if index >= 0:
                    self.prediction_customer_combo.setCurrentIndex(index)

            # Metrics tab hidden for now
            # if source_tab != 'metrics' and customer != self.metrics_customer_combo.currentText():
            #     index = self.metrics_customer_combo.findText(customer)
            #     if index >= 0:
            #         self.metrics_customer_combo.setCurrentIndex(index)

            # Update product lists in all tabs
            if source_tab == 'data':
                self.update_data_products()
            elif source_tab == 'analysis':
                self.update_analysis_products()
            elif source_tab == 'prediction':
                self.update_prediction_products()
            elif source_tab == 'metrics':
                self.update_metrics_products()
        finally:
            # Clear the flag
            self._updating_combos = False

    def on_product_changed(self, product, source_tab):
        """Handle product selection change and synchronize across tabs."""
        if not product:
            return

        # Skip if we're in the middle of programmatically updating combos
        if hasattr(self, '_updating_combos') and self._updating_combos:
            return

        # Set flag to prevent recursive updates
        self._updating_combos = True

        try:
            # Get the current customer
            customer = None
            if source_tab == 'data':
                customer = self.data_customer_combo.currentText()
            elif source_tab == 'analysis':
                customer = self.analysis_customer_combo.currentText()
            elif source_tab == 'prediction':
                customer = self.prediction_customer_combo.currentText()
            elif source_tab == 'metrics':
                customer = self.metrics_customer_combo.currentText()

            if not customer:
                return

            # Get products for this customer
            products = self.data_manager.get_products(customer)
            if product not in products:
                return

            # Update the product selection in all tabs except the source
            if source_tab != 'data':
                index = self.data_product_combo.findText(product)
                if index >= 0:
                    self.data_product_combo.setCurrentIndex(index)
                    # Update the preview
                    if self.tabs.currentIndex() == 0:  # If on Data Management tab
                        self.update_data_preview()

            if source_tab != 'analysis':
                index = self.analysis_product_combo.findText(product)
                if index >= 0:
                    self.analysis_product_combo.setCurrentIndex(index)
                    # Update the analysis
                    if self.tabs.currentIndex() == 1:  # If on Data Analysis tab
                        self.update_analysis()

            if source_tab != 'prediction':
                index = self.prediction_product_combo.findText(product)
                if index >= 0:
                    self.prediction_product_combo.setCurrentIndex(index)

            # Metrics tab hidden for now
            # if source_tab != 'metrics':
            #     index = self.metrics_product_combo.findText(product)
            #     if index >= 0:
            #         self.metrics_product_combo.setCurrentIndex(index)
        finally:
            # Clear the flag
            self._updating_combos = False

    def update_data_products(self):
        """Update product list based on selected customer in data tab."""
        customer = self.data_customer_combo.currentText()

        if not customer:
            return

        # Get products for selected customer
        products = self.data_manager.get_products(customer)

        # Update product combo
        self.data_product_combo.clear()
        if products:
            self.data_product_combo.addItems(products)

    def update_data_preview(self):
        """Update data preview based on selected customer and product."""
        customer = self.data_customer_combo.currentText()
        product = self.data_product_combo.currentText()

        if not customer or not product:
            return

        # Get data for selected customer and product
        data = self.data_manager.get_product_data(customer, product)

        if data is None or len(data) == 0:
            QMessageBox.warning(self, "Warning", f"No data available for {customer} - {product}")
            return

        # Display data in table
        model = PandasTableModel(data)
        self.data_table.setModel(model)

        # Auto-resize columns to content
        self.data_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)

        # Plot data
        self.data_canvas.clear()
        ax = self.data_canvas.axes

        # Sort data by date
        data_sorted = data.sort_values('date')

        # Plot quantity over time
        ax.plot(data_sorted['date'], data_sorted['quantity'], 'o-', color='#1f77b4')
        ax.set_title(f"Order History: {customer} - {product}")
        ax.set_xlabel("Date")
        ax.set_ylabel("Quantity")
        ax.grid(True, linestyle='--', alpha=0.7)

        # Format x-axis dates
        date_formatter = plt.matplotlib.dates.DateFormatter('%b %Y')
        ax.xaxis.set_major_formatter(date_formatter)
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        # Draw the plot
        self.data_canvas.draw()

        # Update status
        self.status_label.setText(f"Displaying {len(data)} records for {customer} - {product}")

    def update_analysis_combos(self, selected_customer=None, selected_product=None):
        """Update customer and product combos in analysis tab."""
        # Get customers
        customers = self.data_manager.get_customers()

        # Update customer combo
        self.analysis_customer_combo.clear()
        if customers:
            self.analysis_customer_combo.addItems(customers)

            # Set the previously selected customer if it exists
            if selected_customer and selected_customer in customers:
                index = self.analysis_customer_combo.findText(selected_customer)
                if index >= 0:
                    self.analysis_customer_combo.setCurrentIndex(index)

                    # Also update products and select the previously selected product
                    if selected_product:
                        products = self.data_manager.get_products(selected_customer)
                        if selected_product in products:
                            self.update_analysis_products()
                            index = self.analysis_product_combo.findText(selected_product)
                            if index >= 0:
                                self.analysis_product_combo.setCurrentIndex(index)
                                # Update the analysis with the selected customer and product
                                self.update_analysis()

    def update_analysis_products(self):
        """Update product combo based on selected customer in analysis tab."""
        customer = self.analysis_customer_combo.currentText()

        if not customer:
            return

        # Get products for selected customer
        products = self.data_manager.get_products(customer)

        # Update product combo
        self.analysis_product_combo.clear()
        if products:
            self.analysis_product_combo.addItems(products)

    def update_analysis(self):
        """Update analysis display based on selected customer and product."""
        customer = self.analysis_customer_combo.currentText()
        product = self.analysis_product_combo.currentText()

        if not customer or not product:
            return

        # Get data quality report
        report = self.data_manager.get_data_quality_report(customer, product)

        if 'error' in report:
            return

        # Update statistics
        self.total_orders_label.setText(str(report['count']))
        self.avg_quantity_label.setText(f"{report['quantity_stats']['mean']:.2f}")
        self.order_frequency_label.setText(f"{report['order_frequency']['avg_days_between']:.2f} days")

        # Plot historical data
        self.analysis_canvas.clear()
        self.data_manager.plot_historical_data(customer, product, self.analysis_canvas.axes)
        self.analysis_canvas.draw()

    def perform_analysis(self):
        """Perform detailed analysis for selected customer and product.

        Note: This method is kept for internal use but is no longer connected to a UI button
        since analysis updates automatically when selections change.
        """
        customer = self.analysis_customer_combo.currentText()
        product = self.analysis_product_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select both customer and product")
            return

        # Show progress
        self.status_label.setText("Performing analysis...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(10)
        QApplication.processEvents()

        # Preprocess data
        success, message, data = self.data_manager.preprocess_for_prophet(customer, product)

        if not success:
            self.progress_bar.setVisible(False)
            QMessageBox.warning(self, "Warning", message)
            self.status_label.setText("Analysis failed")
            return

        # Update progress
        self.progress_bar.setValue(50)
        QApplication.processEvents()

        # Update analysis display
        self.update_analysis()

        # Hide progress
        self.progress_bar.setValue(100)
        self.status_label.setText("Analysis completed")

        # Hide progress bar after a delay
        QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        logger.info(f"Analysis performed for {customer} - {product}")

    def export_analysis(self):
        """Export analysis results."""
        customer = self.analysis_customer_combo.currentText()
        product = self.analysis_product_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select both customer and product")
            return

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Analysis", "", "CSV Files (*.csv)")

        if not file_path:
            return

        # Ensure file has .csv extension
        if not file_path.lower().endswith('.csv'):
            file_path += '.csv'

        # Export data
        success, message = self.data_manager.export_data(customer, product, file_path)

        if success:
            QMessageBox.information(self, "Success", message)
        else:
            QMessageBox.critical(self, "Error", message)

    def update_prediction_combos(self, selected_customer=None, selected_product=None):
        """Update customer and product combos in prediction tab."""
        # Get customers
        customers = self.data_manager.get_customers()

        # Update customer combo
        self.prediction_customer_combo.clear()
        if customers:
            self.prediction_customer_combo.addItems(customers)

            # Set the previously selected customer if it exists
            if selected_customer and selected_customer in customers:
                index = self.prediction_customer_combo.findText(selected_customer)
                if index >= 0:
                    self.prediction_customer_combo.setCurrentIndex(index)

                    # Also update products and select the previously selected product
                    if selected_product:
                        products = self.data_manager.get_products(selected_customer)
                        if selected_product in products:
                            self.update_prediction_products()
                            index = self.prediction_product_combo.findText(selected_product)
                            if index >= 0:
                                self.prediction_product_combo.setCurrentIndex(index)



    def update_prediction_products(self):
        """Update product combo based on selected customer in prediction tab."""
        customer = self.prediction_customer_combo.currentText()

        if not customer:
            return

        # Get products for selected customer
        products = self.data_manager.get_products(customer)

        # Update product combo
        self.prediction_product_combo.clear()
        if products:
            self.prediction_product_combo.addItems(products)



    def train_all_products(self):
        """Train individual models for all products of the selected customer.

        This method trains separate models for each product of the selected customer,
        allowing for more accurate predictions per product while still being efficient.
        """
        # Disable the entire UI while training models to prevent multiple clicks
        self.setEnabled(False)
        QApplication.processEvents()  # Update the UI to show it's disabled

        try:
            # Get selected customer
            customer = self.prediction_customer_combo.currentText()

            if not customer:
                QMessageBox.warning(self, "Warning", "Please select a customer")
                self.setEnabled(True)
                return

            # Get selected model
            model_name = self.model_selection_combo.currentText()
            logger.info(f"Selected model name: {model_name}")

            # Confirm with user
            products = self.data_manager.get_products(customer)
            if not products:
                QMessageBox.warning(self, "Warning", f"No products found for customer {customer}")
                self.setEnabled(True)
                return

            reply = QMessageBox.question(
                self, "Train All Products",
                f"This will train individual {model_name} models for all {len(products)} products of {customer}. This may take some time. Continue?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.No:
                self.setEnabled(True)
                return

            # Show progress
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(5)
            self.status_label.setText(f"Training {model_name} models for all products of {customer}...")
            QApplication.processEvents()  # Update the UI

            # Train models for each product
            successful_models = 0
            failed_models = 0
            skipped_models = 0

            for i, product in enumerate(products):
                # Update progress
                progress = 5 + int(90 * (i / len(products)))
                self.progress_bar.setValue(progress)
                self.status_label.setText(f"Training model for {customer} - {product} ({i+1}/{len(products)})...")
                QApplication.processEvents()  # Update the UI

                try:
                    # Get data for this product
                    data = self.data_manager.get_product_data(customer, product)

                    if data is None or len(data) < 5:
                        logger.warning(f"Insufficient data for {customer} - {product}")
                        skipped_models += 1
                        continue

                    # Train the model
                    if model_name == "TCI-fix":
                        # Prepare data for the TCI-fix model
                        if 'date' in data.columns and 'quantity' in data.columns:
                            # Train the model
                            logger.info(f"Training TCI-fix model for {customer} - {product}")
                            success, message = self.model_integration.train_model(
                                "TCI-fix", customer, product
                            )
                            logger.info(f"TCI-fix model training result: success={success}, message={message}")

                            if success:
                                successful_models += 1
                            else:
                                failed_models += 1
                                logger.warning(f"Failed to train model for {customer} - {product}: {message}")
                        else:
                            logger.warning(f"Data must have 'date' and 'quantity' columns for {customer} - {product}")
                            skipped_models += 1
                    else:
                        # For other model types
                        logger.warning(f"Training all products is currently optimized for TCI-fix model only")
                        skipped_models += 1

                except Exception as e:
                    logger.error(f"Error training model for {customer} - {product}: {str(e)}")
                    failed_models += 1

            # Update progress
            self.progress_bar.setValue(100)
            self.status_label.setText(f"Completed training models for {customer}")
            QApplication.processEvents()  # Update the UI

            # Show summary
            summary = f"Training Summary for {customer}:\n\n"
            summary += f"✅ Successfully trained: {successful_models} models\n"
            summary += f"❌ Failed to train: {failed_models} models\n"
            summary += f"⏭️ Skipped (insufficient data): {skipped_models} models\n"

            QMessageBox.information(self, "Training Complete", summary)

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error training models: {str(e)}")
            logger.error(f"Error training models: {str(e)}")
            import traceback
            traceback.print_exc()

        finally:
            # Hide progress bar after a delay
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))
            self.setEnabled(True)

    def train_global_model(self):
        """Train a global model on all customer-product data combined.

        This method trains a single model on all available data, allowing it to learn
        patterns across different products and customers.
        """
        # Disable the entire UI while training model to prevent multiple clicks
        self.setEnabled(False)
        QApplication.processEvents()  # Update the UI to show it's disabled

        try:
            # Get selected model
            model_name = self.model_selection_combo.currentText()
            logger.info(f"Selected model name: {model_name}")
            logger.info(f"Model name (repr): {repr(model_name)}")
            logger.info(f"Model name type: {type(model_name)}")

            # Validate model selection
            if model_name != "Ensemble (TCI + XGBoost)":
                QMessageBox.warning(self, "Warning", "Global model training is currently only supported for Ensemble (TCI + XGBoost) model")
                self.setEnabled(True)
                return

            # Confirm with user
            reply = QMessageBox.question(
                self, "Train Global Model",
                "This will train a single model on all customer-product data combined. This may take some time. Continue?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.No:
                self.setEnabled(True)
                return

            # Show progress
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(10)
            self.status_label.setText(f"Training global {model_name} model...")
            QApplication.processEvents()  # Update the UI

            # Train the global model
            self.progress_bar.setValue(30)
            QApplication.processEvents()  # Update the UI

            # Call the train_global_model method in model_integration
            model_info = self.model_integration.train_global_model(model_name)

            # Update progress
            self.progress_bar.setValue(90)
            QApplication.processEvents()  # Update the UI

            # Check if training was successful
            if model_info and model_info.get("status") == "success":
                # Update progress
                self.progress_bar.setValue(100)
                self.status_label.setText(f"Global {model_name} model trained successfully")
                QMessageBox.information(self, "Success", f"Global {model_name} model trained successfully")

                # Auto-check the use global model checkbox
                self.use_global_model_checkbox.setChecked(True)
            else:
                # Show error message
                error_message = model_info.get("message", "Unknown error") if model_info else "Unknown error"
                self.status_label.setText(f"Error training global model: {error_message}")
                QMessageBox.critical(self, "Error", f"Error training global model: {error_message}")

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error training global model: {str(e)}")
            logger.error(f"Error training global model: {str(e)}")
            import traceback
            traceback.print_exc()

        finally:
            # Hide progress bar after a delay
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))
            self.setEnabled(True)

    def train_model(self):
        """Train a model for selected customer and product.

        This method focuses only on training the model and saving it for later use.
        It does not generate predictions. The process involves:

        1. Preprocessing the data to prepare it for the prediction model
        2. Training the selected model on the historical data
        3. Saving the trained model to disk
        4. Displaying model metrics and diagnostics

        The trained model will be saved in a structured folder with metrics and plots
        to help evaluate model quality.
        """
        # Disable the entire UI while training model to prevent multiple clicks
        self.setEnabled(False)
        QApplication.processEvents()  # Update the UI to show it's disabled

        try:
            customer = self.prediction_customer_combo.currentText()
            product = self.prediction_product_combo.currentText()

            # Get the model name
            model_name = self.model_selection_combo.currentText()
            logger.info(f"Selected model name for forecast: '{model_name}'")
            logger.info(f"Model name type: {type(model_name)}")
            logger.info(f"Model name repr: {repr(model_name)}")
            logger.info(f"Model name bytes: {model_name.encode('utf-8')}")
            logger.info(f"Training model: '{model_name}' for {customer} - {product}")
            logger.info(f"Model name (repr): {repr(model_name)}")

            if not customer or not product:
                QMessageBox.warning(self, "Warning", "Please select both customer and product")
                return

            # Show progress
            self.status_label.setText(f"Training {model_name} model for {customer} - {product}...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(10)
            QApplication.processEvents()

            # Preprocess data with timeout
            success = False
            message = "Timeout while preprocessing data"
            data = None

            # Set up a timer to prevent hanging
            timer = QTimer()
            timer.setSingleShot(True)
            timer.start(60000)  # 60 second timeout

            # Preprocess data
            while timer.isActive():
                try:
                    data = self.data_manager.get_product_data(customer, product)
                    if data is None or len(data) < 5:
                        message = f"Insufficient data for {customer} - {product}"
                        break
                    success = True
                    break
                except Exception as e:
                    message = f"Error preprocessing data: {str(e)}"
                    logger.error(message)
                    break

            if not success or data is None:
                self.status_label.setText(message)
                self.progress_bar.setVisible(False)
                QMessageBox.warning(self, "Warning", message)
                self.setEnabled(True)
                return

            # Update progress
            self.progress_bar.setValue(30)
            QApplication.processEvents()

            # Train the model with timeout
            success = False
            message = "Timeout while training model"

            # Set up a timer to prevent hanging
            timer = QTimer()
            timer.setSingleShot(True)
            timer.start(300000)  # 5 minute timeout

            # Train the model
            while timer.isActive():
                try:
                    if model_name == "Prophet":
                        success, message = self.prophet_model.train_model(data, customer, product)
                    elif model_name == "TCI":
                        # Use the direct training approach that we know works
                        try:
                            # Import the direct training function
                            import sys
                            import importlib.util
                            spec = importlib.util.spec_from_file_location("train_tci_directly", "utils/train_tci_directly.py")
                            train_tci_module = importlib.util.module_from_spec(spec)
                            sys.modules["train_tci_directly"] = train_tci_module
                            spec.loader.exec_module(train_tci_module)

                            # Use the function from the module
                            success = train_tci_module.train_tci_model(customer, product)
                            message = "Model trained successfully" if success else "Failed to train model"

                            # Load the trained model
                            if success:
                                self.tci_model.load_model(customer, product)
                        except Exception as e:
                            logger.error(f"Error training TCI model: {str(e)}")
                            success = False
                            message = f"Error training TCI model: {str(e)}"

                    elif model_name == "Ensemble (TCI + XGBoost)":
                        # Train the Ensemble model
                        try:
                            # Prepare data for the Ensemble model (same format as Prophet)
                            if 'date' in data.columns and 'quantity' in data.columns:
                                # Rename columns to expected format
                                ensemble_data = data.rename(columns={'date': 'ds', 'quantity': 'y'})
                                # Convert date to datetime
                                ensemble_data['ds'] = pd.to_datetime(ensemble_data['ds'])
                                # Sort by date
                                ensemble_data = ensemble_data.sort_values('ds')

                                # Train the model
                                logger.info(f"Training Ensemble (TCI + XGBoost) model for {customer} - {product}")
                                success, message = self.model_integration.ensemble_predictor.train_model(ensemble_data, customer, product)
                                logger.info(f"Ensemble model training result: success={success}, message={message}")
                            else:
                                success = False
                                message = "Data must have 'date' and 'quantity' columns"
                        except Exception as e:
                            logger.error(f"Error training Ensemble model: {str(e)}")
                            success = False
                            message = f"Error training Ensemble model: {str(e)}"

                    elif model_name == "TCI Premium":
                        # Train the TCI Premium model
                        try:
                            # Prepare data for the TCI Premium model (same format as Prophet)
                            if 'date' in data.columns and 'quantity' in data.columns:
                                # Rename columns to expected format
                                premium_data = data.rename(columns={'date': 'ds', 'quantity': 'y'})
                                # Convert date to datetime
                                premium_data['ds'] = pd.to_datetime(premium_data['ds'])
                                # Sort by date
                                premium_data = premium_data.sort_values('ds')

                                # Check if hyperparameter optimization is requested
                                optimize_hyperparams = self.optimize_hyperparams_checkbox.isChecked()

                                # Train the model
                                logger.info(f"Training TCI Premium model for {customer} - {product}")
                                success, message = self.model_integration.train_model(
                                    "TCI Premium", customer, product,
                                    optimize_hyperparams=optimize_hyperparams
                                )
                                logger.info(f"TCI Premium model training result: success={success}, message={message}")
                            else:
                                success = False
                                message = "Data must have 'date' and 'quantity' columns"
                        except Exception as e:
                            logger.error(f"Error training TCI Premium model: {str(e)}")
                            success = False
                            message = f"Error training TCI Premium model: {str(e)}"
                    elif model_name == "TCI-fix":
                        # Train the TCI-fix model
                        try:
                            # Prepare data for the TCI-fix model
                            if 'date' in data.columns and 'quantity' in data.columns:
                                # Train the model
                                logger.info(f"Training TCI-fix model for {customer} - {product}")
                                success, message = self.model_integration.train_model(
                                    "TCI-fix", customer, product
                                )
                                logger.info(f"TCI-fix model training result: success={success}, message={message}")
                            else:
                                success = False
                                message = "Data must have 'date' and 'quantity' columns"
                        except Exception as e:
                            logger.error(f"Error training TCI-fix model: {str(e)}")
                            success = False
                            message = f"Error training TCI-fix model: {str(e)}"
                    # We've removed the Hybrid TCI model, so this should never happen
                    else:
                        # Debug logging to help diagnose model name issues
                        logger.warning(f"Unknown model name: '{model_name}'")
                        logger.warning(f"Model name (repr): {repr(model_name)}")
                        logger.warning(f"Available models: Prophet, TCI, Ensemble (TCI + XGBoost), TCI Premium, TCI-fix")

                        success = False
                        message = f"Unknown model: {model_name}"
                    break
                except Exception as e:
                    message = f"Error training model: {str(e)}"
                    logger.error(message)
                    break

            # Update progress
            self.progress_bar.setValue(80)
            QApplication.processEvents()

            if not success:
                self.status_label.setText(message)
                self.progress_bar.setVisible(False)
                QMessageBox.warning(self, "Warning", message)
                self.setEnabled(True)
                return

            # Create model info directory
            model_dir = f"models/{model_name}/{customer}_{product}"
            os.makedirs(model_dir, exist_ok=True)

            # Save model metrics
            if model_name == "TCI":
                key = f"{customer}_{product}"
                if key in self.tci_model.models:
                    metrics = self.tci_model.models[key].get('metrics', {})

                    # Display metrics
                    metrics_text = f"MAPE: {metrics.get('mape', 'N/A'):.4f}, "
                    metrics_text += f"R²: {metrics.get('r2', 'N/A'):.4f}, "
                    metrics_text += f"RMSE: {metrics.get('rmse', 'N/A'):.4f}"
                    self.metrics_label.setText(metrics_text)

                    # Save metrics to file
                    with open(f"{model_dir}/metrics.txt", 'w') as f:
                        f.write(f"Model: {model_name}\n")
                        f.write(f"Customer: {customer}\n")
                        f.write(f"Product: {product}\n")
                        f.write(f"Training Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                        f.write(f"MAPE: {metrics.get('mape', 'N/A'):.4f}\n")
                        f.write(f"R²: {metrics.get('r2', 'N/A'):.4f}\n")
                        f.write(f"RMSE: {metrics.get('rmse', 'N/A'):.4f}\n")

                    # Copy feature importance plot
                    feature_plot = f"results/tci/{key}_feature_importance.png"
                    if os.path.exists(feature_plot):
                        import shutil
                        shutil.copy(feature_plot, f"{model_dir}/feature_importance.png")

                    # Copy causal graph plot
                    causal_plot = f"results/tci/{key}_causal_graph.png"
                    if os.path.exists(causal_plot):
                        import shutil
                        shutil.copy(causal_plot, f"{model_dir}/causal_graph.png")

            # Display model metrics
            if model_name == "TCI":
                key = f"{customer}_{product}"
                if key in self.tci_model.models:
                    metrics = self.tci_model.models[key].get('metrics', {})
                    mape = metrics.get('mape', 'N/A')
                    r2 = metrics.get('r2', 'N/A')
                    rmse = metrics.get('rmse', 'N/A')

                    # Format metrics for display
                    if isinstance(mape, (int, float)):
                        mape_str = f"{mape:.4f}"
                    else:
                        mape_str = str(mape)

                    if isinstance(r2, (int, float)):
                        r2_str = f"{r2:.4f}"
                    else:
                        r2_str = str(r2)

                    if isinstance(rmse, (int, float)):
                        rmse_str = f"{rmse:.2f}"
                    else:
                        rmse_str = str(rmse)

                    # Display metrics in the UI
                    metrics_text = f"MAPE: {mape_str} | R²: {r2_str} | RMSE: {rmse_str}"
                    self.metrics_label.setText(metrics_text)

                    # Create a visual score message
                    if isinstance(mape, (int, float)):
                        if mape < 0.05:  # Less than 5% error
                            quality = "Excellent"
                            color = "darkgreen"
                        elif mape < 0.10:  # Less than 10% error
                            quality = "Very Good"
                            color = "green"
                        elif mape < 0.20:  # Less than 20% error
                            quality = "Good"
                            color = "orange"
                        elif mape < 0.30:  # Less than 30% error
                            quality = "Fair"
                            color = "darkorange"
                        else:  # 30% or more error
                            quality = "Poor"
                            color = "red"

                        quality_message = f"<span style='color:{color}; font-weight:bold;'>Model Quality: {quality}</span>"
                    else:
                        quality_message = "<span style='color:gray;'>Model Quality: Unknown</span>"

                    # Update progress
                    self.progress_bar.setValue(100)
                    self.status_label.setText(f"Model trained successfully for {customer} - {product}")

                    # Show a detailed message box with metrics
                    msg = QMessageBox(self)
                    msg.setWindowTitle("Model Training Results")
                    msg.setIcon(QMessageBox.Information)

                    # Create HTML content for the message box
                    html_content = f"""
                    <h3>Model trained successfully!</h3>
                    <p><b>Customer:</b> {customer}</p>
                    <p><b>Product:</b> {product}</p>
                    <p><b>Model Type:</b> {model_name}</p>
                    <br>
                    <h4>Performance Metrics:</h4>
                    <p><b>MAPE:</b> {mape_str} <i>(Mean Absolute Percentage Error - lower is better)</i></p>
                    <p><b>R²:</b> {r2_str} <i>(Coefficient of Determination - higher is better)</i></p>
                    <p><b>RMSE:</b> {rmse_str} <i>(Root Mean Square Error - lower is better)</i></p>
                    <br>
                    <h4>{quality_message}</h4>
                    <p>The model has been saved and is ready for generating forecasts.</p>
                    """

                    msg.setText(html_content)
                    msg.exec_()
                else:
                    # Update progress
                    self.progress_bar.setValue(100)
                    self.status_label.setText(f"Model trained successfully for {customer} - {product}")
                    QMessageBox.information(self, "Success", f"Model trained successfully for {customer} - {product}")
            else:
                # Update progress
                self.progress_bar.setValue(100)
                self.status_label.setText(f"Model trained successfully for {customer} - {product}")
                QMessageBox.information(self, "Success", f"Model trained successfully for {customer} - {product}")

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error training model: {str(e)}")
            logger.error(f"Error training model: {str(e)}")
            import traceback
            traceback.print_exc()

        # Re-enable the UI
        self.progress_bar.setVisible(False)
        self.setEnabled(True)

    def generate_forecast(self):
        """Generate a forecast for future customer orders using a pre-trained model.

        This method focuses on loading a pre-trained model and using it to generate
        predictions. The process involves:

        1. Loading the pre-trained model for the selected customer-product combination
        2. Generating predictions for the specified time period
        3. Visualizing the results in the UI

        If no pre-trained model exists, it will train a new one first.
        """
        # Disable the entire UI while generating forecast to prevent multiple clicks
        # This prevents the user from clicking buttons while processing
        self.setEnabled(False)
        QApplication.processEvents()  # Update the UI to show it's disabled

        try:
            customer = self.prediction_customer_combo.currentText()
            product = self.prediction_product_combo.currentText()
            model_name = self.model_selection_combo.currentText()
            period = self.prediction_period_combo.currentText()

            if not customer or not product:
                QMessageBox.warning(self, "Warning", "Please select both customer and product")
                return

            # Show progress
            self.status_label.setText(f"Generating forecast for {customer} - {product}...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(10)
            QApplication.processEvents()

            # Get the model name
            model_name = self.model_selection_combo.currentText()
            logger.info(f"Selected model for prediction: '{model_name}'")
            logger.info(f"Model name (repr): {repr(model_name)}")
            logger.info(f"Model name type: {type(model_name)}")
            logger.info(f"Model name bytes: {model_name.encode('utf-8')}")

            # Check if a pre-trained model exists
            key = f"{customer}_{product}"
            logger.info(f"Customer-product key: '{key}'")
            success = False
            message = ""

            # Normalize model name to handle potential whitespace or case issues
            model_name = model_name.strip()
            logger.info(f"Normalized model name: '{model_name}'")

            if model_name == "Prophet":
                model_path = f"models/Prophet/{key}.pkl"
                if not os.path.exists(model_path):
                    message = "No pre-trained Prophet model found. Please train the model first."
                    self.status_label.setText(message)
                    self.progress_bar.setVisible(False)
                    QMessageBox.warning(self, "Warning", message)
                    self.setEnabled(True)
                    return
                # Prophet doesn't have a load_model method yet, so we need to train it
                # This is a temporary solution until we implement model loading for Prophet
                success = True
            elif model_name == "TCI":
                model_path = f"models/tci/{key}.pkl"
                if not os.path.exists(model_path):
                    message = "No pre-trained TCI model found. Please train the model first."
                    self.status_label.setText(message)
                    self.progress_bar.setVisible(False)
                    QMessageBox.warning(self, "Warning", message)
                    self.setEnabled(True)
                    return
                # Load the pre-trained TCI model
                success = self.tci_model.load_model(customer, product)
                if not success:
                    message = "Failed to load pre-trained TCI model. Please train the model again."
                    self.status_label.setText(message)
                    self.progress_bar.setVisible(False)
                    QMessageBox.warning(self, "Warning", message)
                    self.setEnabled(True)
                    return

            elif model_name == "Ensemble (TCI + XGBoost)":
                # Check if the model exists
                model_dir = f"models/ensemble_tci_xgboost/{customer.replace('/', '_')}_{product.replace('/', '_')}"
                model_path = f"{model_dir}/model.pkl"
                if not os.path.exists(model_path):
                    message = "No pre-trained Ensemble (TCI + XGBoost) model found. Please train the model first."
                    self.status_label.setText(message)
                    self.progress_bar.setVisible(False)
                    QMessageBox.warning(self, "Warning", message)
                    self.setEnabled(True)
                    return
                # The ensemble model is loaded automatically when needed
                success = True

            elif model_name == "TCI Premium":
                # Check if the model exists
                model_dir = f"models/tci_premium/{customer.replace('/', '_')}_{product.replace('/', '_')}"
                keras_model_path = f"{model_dir}/keras_model.h5"
                best_model_path = f"{model_dir}/best_model.h5"

                # Check for either keras_model.h5 or best_model.h5
                if not os.path.exists(model_dir) or (not os.path.exists(keras_model_path) and not os.path.exists(best_model_path)):
                    logger.warning(f"No pre-trained TCI Premium model found in {model_dir}")
                    logger.warning(f"Checked for {keras_model_path} and {best_model_path}")
                    message = "No pre-trained TCI Premium model found. Please train the model first."
                    self.status_label.setText(message)
                    self.progress_bar.setVisible(False)
                    QMessageBox.warning(self, "Warning", message)
                    self.setEnabled(True)
                    return
                # The TCI Premium model is loaded automatically when needed
                logger.info(f"Found TCI Premium model in {model_dir}")
                success = True

            elif model_name == "TCI-fix":
                # Check if the model exists (new path format)
                model_dir = f"models/tci_fix/{customer.replace('/', '_')}_{product.replace('/', '_')}"
                model_path = os.path.join(model_dir, "model.pkl")

                # Also check the old path format for backward compatibility
                old_model_path = f"models/tci_fix_{customer}_{product}.pkl"

                if not os.path.exists(model_path) and not os.path.exists(old_model_path):
                    logger.warning(f"No pre-trained TCI-fix model found at {model_path} or {old_model_path}")
                    message = f"No pre-trained TCI-fix model found for {customer} - {product}. Please train the model first."
                    self.status_label.setText(message)
                    self.progress_bar.setVisible(False)
                    QMessageBox.warning(self, "Warning", message)
                    self.setEnabled(True)
                    return
                # The TCI-fix model is loaded automatically when needed
                logger.info(f"Found TCI-fix model at {model_path}")
                success = True

            else:
                message = f"Unknown model type: {model_name}"
                self.status_label.setText(message)
                self.progress_bar.setVisible(False)
                QMessageBox.warning(self, "Warning", message)
                self.setEnabled(True)
                return

            # Update progress
            self.progress_bar.setValue(30)
            QApplication.processEvents()

            # Generate predictions with timeout
            success = False
            message = "Timeout while generating predictions"

            # Set up a timer to prevent hanging
            timer = QTimer()
            timer.setSingleShot(True)
            timer.start(60000)  # 60 second timeout

            # Get the number of periods to predict
            try:
                periods = int(period.split()[0])
                # For multi-year forecasts, convert years to months if needed
                if 'year' in period.lower() or 'years' in period.lower():
                    periods = periods * 12
                    logger.info(f"Converting {period} to {periods} months")
            except Exception as e:
                logger.warning(f"Error parsing period '{period}': {str(e)}")
                periods = 12  # Default to 12 months

            # Get the start year for predictions
            start_year_text = self.prediction_year_combo.currentText()
            if start_year_text == "After Historical Data":
                # Use None to indicate we should start right after the historical data
                start_year = None
                logger.info("Using 'After Historical Data' option - will start predictions immediately after last historical data point")
            else:
                # Convert to integer
                try:
                    start_year = int(start_year_text)
                    logger.info(f"Starting predictions from year: {start_year}")
                except ValueError:
                    # Default to None if conversion fails
                    start_year = None
                    logger.warning(f"Could not parse start year '{start_year_text}', using end of historical data instead")
            forecast = None

            while timer.isActive():
                try:
                    if model_name == "Prophet":
                        # For Prophet, we need to get the data first
                        data = self.data_manager.get_product_data(customer, product)
                        if data is None or len(data) < 5:
                            success = False
                            message = f"Insufficient data for {customer} - {product}"
                            break

                        # Preprocess data for Prophet
                        if 'date' in data.columns and 'quantity' in data.columns:
                            # Rename columns to Prophet's expected format
                            prophet_data = data.rename(columns={'date': 'ds', 'quantity': 'y'})
                            # Convert date to datetime
                            prophet_data['ds'] = pd.to_datetime(prophet_data['ds'])
                            # Sort by date
                            prophet_data = prophet_data.sort_values('ds')

                            # First train the model if it doesn't exist
                            key = f"{customer}_{product}"
                            if key not in self.prophet_model.models:
                                success, message = self.prophet_model.train_model(prophet_data, customer, product)
                                if not success:
                                    break

                            # Generate predictions
                            success, message, forecast = self.prophet_model.predict_future(customer, product, periods, start_year=start_year)
                        else:
                            success = False
                            message = f"Missing required columns in data for {customer} - {product}"
                    elif model_name == "TCI":
                        # Generate predictions using the pre-trained TCI model
                        forecast = self.tci_model.predict_future(customer, product, periods, start_year=start_year)
                        if forecast is not None:
                            success = True
                            message = "Predictions generated successfully"
                        else:
                            success = False
                            message = "Failed to generate predictions"
                    elif model_name == "TCI Premium":
                        # Generate predictions using the pre-trained TCI Premium model
                        logger.info(f"Using TCI Premium model for prediction: {customer}, {product}, {periods} periods")
                        forecast = self.model_integration.tci_premium_predictor.predict_future(customer, product, periods, start_year=start_year)
                        if forecast is not None:
                            success = True
                            message = "Predictions generated successfully"
                        else:
                            success = False
                            message = "Failed to generate predictions"
                    elif model_name == "Ensemble (TCI + XGBoost)":
                        # Generate predictions using the pre-trained Ensemble model
                        forecast = self.model_integration.ensemble_predictor.predict_future(customer, product, periods)
                        if forecast is not None:
                            # Convert to standard format if needed
                            if not isinstance(forecast, pd.DataFrame):
                                logger.warning("Ensemble model returned non-DataFrame forecast")
                                success = False
                                message = "Invalid forecast format returned by model"
                            else:
                                # Ensure the forecast has the expected columns
                                if 'ds' in forecast.columns and 'yhat' in forecast.columns:
                                    # Convert to the standard format expected by the UI
                                    forecast = pd.DataFrame({
                                        'date': forecast['ds'],
                                        'predicted_quantity': forecast['yhat'],
                                        'lower_bound': forecast['yhat_lower'] if 'yhat_lower' in forecast.columns else None,
                                        'upper_bound': forecast['yhat_upper'] if 'yhat_upper' in forecast.columns else None
                                    })
                                    success = True
                                    message = "Predictions generated successfully"
                                else:
                                    logger.warning(f"Ensemble model forecast missing required columns: {forecast.columns}")
                                    success = False
                                    message = "Invalid forecast format returned by model"
                        else:
                            success = False
                            message = "Failed to generate predictions"
                    elif model_name == "TCI-fix":
                        # Generate predictions using the pre-trained TCI-fix model
                        logger.info(f"Using TCI-fix model for prediction: {customer}, {product}, {periods} periods")
                        forecast = self.model_integration.generate_forecast("TCI-fix", customer, product, periods, start_year=start_year)
                        if forecast is not None:
                            success = True
                            message = "Predictions generated successfully"
                        else:
                            success = False
                            message = "Failed to generate predictions"
                    elif model_name not in ["Prophet", "TCI", "Ensemble (TCI + XGBoost)", "TCI Premium", "TCI-fix"]:
                        # Only show the unknown model error if it's not one of the known models
                        success = False
                        message = f"Unknown model type: {model_name}"
                    break
                except Exception as e:
                    message = f"Error generating predictions: {str(e)}"
                    logger.error(message)
                    break

            # Update progress
            self.progress_bar.setValue(60)
            QApplication.processEvents()

            if not success or forecast is None:
                self.status_label.setText(message)
                self.progress_bar.setVisible(False)
                QMessageBox.warning(self, "Warning", message)
                self.setEnabled(True)
                return

            # Display the forecast in the UI
            try:
                # Format the forecast for display
                forecast_table = self.format_forecast_for_display(forecast)

                # Set the forecast in the table
                self.prediction_result_table.setRowCount(len(forecast_table))

                for i, (date, mean, lower, upper) in enumerate(forecast_table):
                    # Date
                    self.prediction_result_table.setItem(i, 0, QTableWidgetItem(date))
                    # Mean prediction
                    self.prediction_result_table.setItem(i, 1, QTableWidgetItem(str(mean)))
                    # Lower bound
                    self.prediction_result_table.setItem(i, 2, QTableWidgetItem(str(lower)))
                    # Upper bound
                    self.prediction_result_table.setItem(i, 3, QTableWidgetItem(str(upper)))

                # Save the forecast to a CSV file with organized folder structure
                # Create customer folder structure: results/{model_name}/forecasts/{customer}/{product}.csv
                clean_customer = customer.replace('/', '_').replace('\\', '_')
                clean_product = product.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')

                customer_folder = f"results/{model_name.lower()}/forecasts/{clean_customer}"
                os.makedirs(customer_folder, exist_ok=True)
                forecast_path = os.path.join(customer_folder, f"{clean_product}.csv")

                # Prepare export data with clean column names and integer values
                export_forecast = forecast.copy()

                # Handle different column naming conventions
                if 'date' in export_forecast.columns and 'prediction' in export_forecast.columns:
                    # TCI-fix format
                    column_mapping = {
                        'date': 'Date',
                        'prediction': 'Predicted_Quantity'
                    }
                    if 'lower_bound' in export_forecast.columns:
                        column_mapping['lower_bound'] = 'Lower_Bound'
                    if 'upper_bound' in export_forecast.columns:
                        column_mapping['upper_bound'] = 'Upper_Bound'

                    export_forecast = export_forecast.rename(columns=column_mapping)

                elif 'ds' in export_forecast.columns and 'yhat' in export_forecast.columns:
                    # Prophet/TCI format
                    column_mapping = {
                        'ds': 'Date',
                        'yhat': 'Predicted_Quantity'
                    }
                    if 'yhat_lower' in export_forecast.columns:
                        column_mapping['yhat_lower'] = 'Lower_Bound'
                    if 'yhat_upper' in export_forecast.columns:
                        column_mapping['yhat_upper'] = 'Upper_Bound'

                    export_forecast = export_forecast.rename(columns=column_mapping)

                # Format dates and round numeric values to integers
                if 'Date' in export_forecast.columns:
                    export_forecast['Date'] = pd.to_datetime(export_forecast['Date']).dt.strftime('%Y-%m-%d')

                # Round numeric columns to integers for tidier presentation
                numeric_columns = ['Predicted_Quantity', 'Lower_Bound', 'Upper_Bound']
                for col in numeric_columns:
                    if col in export_forecast.columns:
                        export_forecast[col] = export_forecast[col].round().astype(int)

                # Select only the columns we want to export
                export_columns = ['Date', 'Predicted_Quantity']
                if 'Lower_Bound' in export_forecast.columns:
                    export_columns.append('Lower_Bound')
                if 'Upper_Bound' in export_forecast.columns:
                    export_columns.append('Upper_Bound')

                export_forecast = export_forecast[export_columns]
                export_forecast.to_csv(forecast_path, index=False)

                # Also save to the organized current directory structure
                current_dir_customer_folder = f"etc/Forecast/forecasts/{clean_customer}"
                os.makedirs(current_dir_customer_folder, exist_ok=True)
                current_dir_path = os.path.join(current_dir_customer_folder, f"{clean_product}.csv")
                export_forecast.to_csv(current_dir_path, index=False)

                # Create a beautiful visualization like in the Manual Review Tool
                self.create_enhanced_visualization(customer, product, forecast)

                logger.info(f"Forecast saved to {forecast_path} and {current_dir_path}")
                logger.info(f"Organized folder structure: {model_name.lower()}/forecasts/{clean_customer}/{clean_product}.csv")
            except Exception as e:
                logger.error(f"Error displaying forecast: {str(e)}")
                raise

            # Update progress
            self.progress_bar.setValue(100)
            self.status_label.setText(f"Forecast generated for {customer} - {product}")

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error generating forecast: {str(e)}")
            logger.error(f"Error generating forecast: {str(e)}")
            import traceback
            traceback.print_exc()

        # Re-enable the UI
        self.progress_bar.setVisible(False)
        self.setEnabled(True)

    def create_enhanced_visualization(self, customer, product, forecast):
        """Create an enhanced visualization of the forecast similar to the Manual Review Tool.

        Args:
            customer (str): Customer name
            product (str): Product name
            forecast (pandas.DataFrame): Forecast DataFrame with ds, yhat, yhat_lower, yhat_upper columns
        """
        # Get the current model name from the UI
        model_name = self.model_selection_combo.currentText()
        try:
            # Get historical data for comparison
            historical_data = self.data_manager.get_product_data(customer, product)
            if historical_data is None or len(historical_data) < 5:
                logger.warning(f"Insufficient historical data for visualization: {customer} - {product}")
                return

            # Prepare historical data
            if 'date' in historical_data.columns and 'quantity' in historical_data.columns:
                # Rename columns to expected format
                historical = historical_data.rename(columns={'date': 'ds', 'quantity': 'y'})
                # Convert date to datetime
                historical['ds'] = pd.to_datetime(historical['ds'])
                # Sort by date
                historical = historical.sort_values('ds')
            else:
                logger.warning(f"Missing required columns in historical data for {customer} - {product}")
                return

            # Clear the existing plot
            self.prediction_canvas_combined.fig.clear()
            ax = self.prediction_canvas_combined.fig.add_subplot(111)

            # Set a professional style
            plt.style.use('seaborn-v0_8-whitegrid')

            # Plot historical data with a more visible line
            ax.plot(historical['ds'], historical['y'],
                   color='#1f77b4', label='Historical',
                   linewidth=2.5, marker='o', markersize=4, alpha=0.8)

            # Check the column names to handle different formats
            date_col = 'date' if 'date' in forecast.columns else 'ds'

            # Handle different naming conventions for prediction values
            if 'predicted_quantity' in forecast.columns:
                mean_col = 'predicted_quantity'
            elif 'prediction' in forecast.columns:
                mean_col = 'prediction'
            elif 'yhat' in forecast.columns:
                mean_col = 'yhat'
            else:
                # If none of the expected columns are found, log an error and use the first numeric column
                logger.error(f"No prediction column found in forecast. Available columns: {forecast.columns.tolist()}")
                numeric_cols = [col for col in forecast.columns if pd.api.types.is_numeric_dtype(forecast[col])]
                if numeric_cols:
                    mean_col = numeric_cols[0]
                    logger.warning(f"Using {mean_col} as prediction column")
                else:
                    logger.error("No numeric columns found in forecast")
                    return None

            # Handle different naming conventions for lower/upper bounds
            if 'lower_bound' in forecast.columns:
                lower_col = 'lower_bound'
            elif 'yhat_lower' in forecast.columns:
                lower_col = 'yhat_lower'
            else:
                lower_col = None

            if 'upper_bound' in forecast.columns:
                upper_col = 'upper_bound'
            elif 'yhat_upper' in forecast.columns:
                upper_col = 'yhat_upper'
            else:
                upper_col = None

            # Log the column mapping for debugging
            logger.info(f"Visualization columns mapping: date={date_col}, mean={mean_col}, lower={lower_col}, upper={upper_col}")
            logger.info(f"Available columns: {forecast.columns.tolist()}")

            # Plot forecast with a contrasting color
            ax.plot(forecast[date_col], forecast[mean_col],
                   color='#ff7f0e', label='Forecast',
                   linewidth=2.5)

            # Plot confidence intervals if available
            if lower_col is not None and upper_col is not None and lower_col in forecast.columns and upper_col in forecast.columns:
                ax.fill_between(forecast[date_col],
                               forecast[lower_col],
                               forecast[upper_col],
                               color='#ff7f0e', alpha=0.2,
                               label='95% Confidence')

            # Add a vertical line to separate historical data from forecast
            last_date = historical['ds'].max()
            ax.axvline(x=last_date, color='#d62728',
                      linestyle='--', alpha=0.7, linewidth=1.5)

            # Add a text label for the forecast start
            y_range = ax.get_ylim()[1] - ax.get_ylim()[0]
            ax.text(last_date, ax.get_ylim()[0] + y_range*0.9,
                   'Forecast Start', ha='center', va='center',
                   color='#d62728', fontweight='bold',
                   bbox={'facecolor': 'white', 'alpha': 0.9, 'pad': 5,
                         'edgecolor': '#d62728', 'boxstyle': 'round,pad=0.5'})

            # Add labels and title with better formatting
            ax.set_xlabel('Date', fontsize=12, fontweight='bold')
            ax.set_ylabel('Quantity', fontsize=12, fontweight='bold')
            ax.set_title(f'Forecast for {customer}\n{product}',
                        fontsize=16, fontweight='bold', pad=20)

            # Add legend with better positioning
            ax.legend(loc='best', frameon=True, framealpha=0.9, fontsize=10)

            # Add grid but make it less prominent
            ax.grid(True, linestyle='--', alpha=0.3)

            # Format x-axis dates for better readability with long forecasts
            self.prediction_canvas_combined.fig.autofmt_xdate()

            # For longer forecasts, adjust the date formatting
            # Use the date column we identified earlier
            if len(forecast[date_col]) > 24:  # If more than 2 years
                import matplotlib.dates as mdates
                # Use a more compact date format for long forecasts
                date_format = mdates.DateFormatter('%b\n%Y')  # Month and year only
                ax.xaxis.set_major_formatter(date_format)
                # Set appropriate tick intervals based on forecast length
                if len(forecast[date_col]) > 60:  # 5+ years
                    ax.xaxis.set_major_locator(mdates.YearLocator())
                else:  # 2-5 years
                    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))  # Every 3 months

            # Add a subtle background color
            ax.set_facecolor('#f8f9fa')

            # Add a border around the plot
            for spine in ax.spines.values():
                spine.set_visible(True)
                spine.set_color('#cccccc')
                spine.set_linewidth(0.8)

            # Add a text annotation explaining the forecast
            forecast_months = len(forecast[date_col])
            model_type = "Ensemble (TCI + XGBoost)" if model_name == "Ensemble (TCI + XGBoost)" else "TCI"
            self.prediction_canvas_combined.fig.text(0.5, 0.01,
                                                  f"Forecast for next {forecast_months} months based on {model_type} model with external factors",
                                                  ha='center', fontsize=10,
                                                  bbox={'facecolor': 'white', 'alpha': 0.8, 'pad': 5, 'edgecolor': '#cccccc'})

            # Update the canvas
            self.prediction_canvas_combined.fig.tight_layout(pad=2)
            self.prediction_canvas_combined.draw()

            # Also update the separate view
            self.create_separate_view_visualization(historical, forecast)

        except Exception as e:
            logger.error(f"Error creating enhanced visualization: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_separate_view_visualization(self, historical, forecast):
        """Create a visualization with separate panels for historical and forecast data.

        Args:
            historical (pandas.DataFrame): Historical data with ds and y columns
            forecast (pandas.DataFrame): Forecast data with ds, yhat, yhat_lower, yhat_upper columns
                                        or date, predicted_quantity, lower_bound, upper_bound columns
        """
        try:
            # Clear the existing plot
            self.prediction_canvas_separate.fig.clear()

            # Create two subplots
            gs = self.prediction_canvas_separate.fig.add_gridspec(2, 1, height_ratios=[1, 1], hspace=0.3)
            ax1 = self.prediction_canvas_separate.fig.add_subplot(gs[0])
            ax2 = self.prediction_canvas_separate.fig.add_subplot(gs[1])

            # Set a professional style
            plt.style.use('seaborn-v0_8-whitegrid')

            # Check the column names to handle different formats for forecast data
            date_col = 'date' if 'date' in forecast.columns else 'ds'

            # Handle different naming conventions for prediction values
            if 'predicted_quantity' in forecast.columns:
                mean_col = 'predicted_quantity'
            elif 'prediction' in forecast.columns:
                mean_col = 'prediction'
            elif 'yhat' in forecast.columns:
                mean_col = 'yhat'
            else:
                # If none of the expected columns are found, log an error and use the first numeric column
                logger.error(f"No prediction column found in forecast. Available columns: {forecast.columns.tolist()}")
                numeric_cols = [col for col in forecast.columns if pd.api.types.is_numeric_dtype(forecast[col])]
                if numeric_cols:
                    mean_col = numeric_cols[0]
                    logger.warning(f"Using {mean_col} as prediction column")
                else:
                    logger.error("No numeric columns found in forecast")
                    return None

            # Handle different naming conventions for lower/upper bounds
            if 'lower_bound' in forecast.columns:
                lower_col = 'lower_bound'
            elif 'yhat_lower' in forecast.columns:
                lower_col = 'yhat_lower'
            else:
                lower_col = None

            if 'upper_bound' in forecast.columns:
                upper_col = 'upper_bound'
            elif 'yhat_upper' in forecast.columns:
                upper_col = 'yhat_upper'
            else:
                upper_col = None

            # Log the column mapping for debugging
            logger.info(f"Separate view visualization columns mapping: date={date_col}, mean={mean_col}, lower={lower_col}, upper={upper_col}")
            logger.info(f"Available forecast columns: {forecast.columns.tolist()}")

            # Plot historical data in the top panel
            ax1.plot(historical['ds'], historical['y'],
                    color='#1f77b4', label='Historical',
                    linewidth=2.5, marker='o', markersize=4, alpha=0.8)
            ax1.set_title('Historical Data', fontsize=14, fontweight='bold')
            ax1.set_ylabel('Quantity', fontsize=12, fontweight='bold')
            ax1.grid(True, linestyle='--', alpha=0.3)
            ax1.legend(loc='best')

            # Plot forecast in the bottom panel
            ax2.plot(forecast[date_col], forecast[mean_col],
                    color='#ff7f0e', label='Forecast',
                    linewidth=2.5)

            # Plot confidence intervals if available
            if lower_col is not None and upper_col is not None and lower_col in forecast.columns and upper_col in forecast.columns:
                ax2.fill_between(forecast[date_col],
                                forecast[lower_col],
                                forecast[upper_col],
                                color='#ff7f0e', alpha=0.2,
                                label='95% Confidence')
            ax2.set_title('Forecast', fontsize=14, fontweight='bold')
            ax2.set_xlabel('Date', fontsize=12, fontweight='bold')
            ax2.set_ylabel('Quantity', fontsize=12, fontweight='bold')
            ax2.grid(True, linestyle='--', alpha=0.3)
            ax2.legend(loc='best')

            # Format dates for better readability with long forecasts
            self.prediction_canvas_separate.fig.autofmt_xdate()

            # For longer forecasts, adjust the date formatting
            if len(forecast[date_col]) > 24:  # If more than 2 years
                import matplotlib.dates as mdates
                # Use a more compact date format for long forecasts
                date_format = mdates.DateFormatter('%b\n%Y')  # Month and year only
                ax2.xaxis.set_major_formatter(date_format)
                # Set appropriate tick intervals based on forecast length
                if len(forecast[date_col]) > 60:  # 5+ years
                    ax2.xaxis.set_major_locator(mdates.YearLocator())
                else:  # 2-5 years
                    ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=3))  # Every 3 months

            # Update the canvas
            self.prediction_canvas_separate.fig.tight_layout()
            self.prediction_canvas_separate.draw()

        except Exception as e:
            logger.error(f"Error creating separate view visualization: {str(e)}")
            import traceback
            traceback.print_exc()

    def format_forecast_for_display(self, forecast):
        """Format the forecast DataFrame for display in the UI.

        Args:
            forecast (pandas.DataFrame): Forecast DataFrame with ds, yhat, yhat_lower, yhat_upper columns
            or date, predicted_quantity, lower_bound, upper_bound columns

        Returns:
            list: List of tuples (date_str, mean, lower, upper) for display in the table
        """
        result = []

        # Check the column names to handle different formats
        date_col = 'date' if 'date' in forecast.columns else 'ds'

        # Handle different naming conventions for prediction values
        if 'predicted_quantity' in forecast.columns:
            mean_col = 'predicted_quantity'
        elif 'prediction' in forecast.columns:
            mean_col = 'prediction'
        elif 'yhat' in forecast.columns:
            mean_col = 'yhat'
        else:
            # If none of the expected columns are found, log an error and use the first numeric column
            logger.error(f"No prediction column found in forecast. Available columns: {forecast.columns.tolist()}")
            numeric_cols = [col for col in forecast.columns if pd.api.types.is_numeric_dtype(forecast[col])]
            if numeric_cols:
                mean_col = numeric_cols[0]
                logger.warning(f"Using {mean_col} as prediction column")
            else:
                logger.error("No numeric columns found in forecast")
                return []

        # Handle different naming conventions for lower/upper bounds
        if 'lower_bound' in forecast.columns:
            lower_col = 'lower_bound'
        elif 'yhat_lower' in forecast.columns:
            lower_col = 'yhat_lower'
        else:
            lower_col = None

        if 'upper_bound' in forecast.columns:
            upper_col = 'upper_bound'
        elif 'yhat_upper' in forecast.columns:
            upper_col = 'yhat_upper'
        else:
            upper_col = None

        # Log the column mapping for debugging
        logger.info(f"Forecast columns mapping: date={date_col}, mean={mean_col}, lower={lower_col}, upper={upper_col}")
        logger.info(f"Available columns: {forecast.columns.tolist()}")

        for i, (_, row) in enumerate(forecast.iterrows()):
            # Format the date
            date_value = row[date_col]
            if isinstance(date_value, str):
                date_str = date_value
            elif pd.isna(date_value) or pd.isnull(date_value):
                # Handle NaT (Not a Time) values
                date_str = "Unknown Date"
                print(f"Warning: Found NaT date value in row {i}")
            else:
                try:
                    date_str = date_value.strftime('%Y-%m-%d')
                except Exception as e:
                    # Fallback for any other date formatting errors
                    print(f"Error formatting date {date_value}: {str(e)}")
                    date_str = f"Date Error: {type(date_value).__name__}"

            # Round the values
            try:
                mean = round(float(row[mean_col]))
            except (ValueError, TypeError) as e:
                logger.error(f"Error converting prediction value {row[mean_col]} to float: {str(e)}")
                logger.error(f"Row data: {row}")
                logger.error(f"Available columns: {forecast.columns.tolist()}")
                mean = 0  # Default to zero if conversion fails

            # Handle the case where lower/upper bounds might not be available
            if lower_col is not None and lower_col in forecast.columns and not pd.isna(row[lower_col]):
                try:
                    lower = round(float(row[lower_col]))
                except (ValueError, TypeError):
                    logger.warning(f"Could not convert lower bound value {row[lower_col]} to float")
                    lower = mean - round(mean * 0.1)  # Default to 10% below mean
            else:
                lower = mean - round(mean * 0.1)  # Default to 10% below mean

            if upper_col is not None and upper_col in forecast.columns and not pd.isna(row[upper_col]):
                try:
                    upper = round(float(row[upper_col]))
                except (ValueError, TypeError):
                    logger.warning(f"Could not convert upper bound value {row[upper_col]} to float")
                    upper = mean + round(mean * 0.1)  # Default to 10% above mean
            else:
                upper = mean + round(mean * 0.1)  # Default to 10% above mean

            result.append((date_str, mean, lower, upper))

        return result
    def update_prediction_display(self):
        """Update prediction display based on current settings."""
        try:
            # Get parameters
            customer = self.prediction_customer_combo.currentText()
            product = self.prediction_product_combo.currentText()
            show_interval = self.prediction_interval_check.isChecked()
            graph_style = self.graph_style_combo.currentText()

            if not customer or not product:
                return

            # Get the selected model name
            model_name = self.model_selection_combo.currentText()
            key = f"{customer}_{product}"

            # Get MAPE if available
            mape_value = None
            mape_text = ""
            if model_name == "Prophet":
                mape_success, mape_message, mape_value = self.prophet_model.get_mape(customer, product)
                if mape_success and mape_value is not None:
                    mape_text = f"MAPE: {mape_value:.2f}%"
            elif model_name == "XGBoost":
                # For future implementation with XGBoost
                pass

            # Update progress
            self.status_label.setText("Updating prediction display...")
            QApplication.processEvents()

            # 1. Update the combined view (history and prediction together)
            try:
                # Clear the canvas first
                self.prediction_canvas_combined.clear()

                # Plot forecast with selected graph style
                if model_name == "Prophet":
                    self.prophet_model.plot_forecast(customer, product, self.prediction_canvas_combined.axes,
                                             uncertainty=show_interval, graph_style=graph_style)

                    # Add MAPE text to the plot if available
                    if mape_value is not None:
                        # Make the MAPE text more visible with a larger font and more prominent box
                        # Position it in the top-left corner to avoid the legend
                        self.prediction_canvas_combined.axes.text(
                            0.02, 0.98, mape_text,
                            transform=self.prediction_canvas_combined.axes.transAxes,
                            verticalalignment='top', horizontalalignment='left',
                            fontsize=14, fontweight='bold',
                            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.9, pad=0.5, edgecolor='black'),
                            zorder=10  # Ensure it's drawn on top of other elements
                        )

                        # Also add MAPE to the title in LARGE text
                        self.prediction_canvas_combined.axes.set_title(f"{customer} - {product}", fontsize=16)
                else:  # XGBoost
                    self.xgboost_model.plot_forecast(customer, product, self.prediction_canvas_combined.axes,
                                              uncertainty=show_interval)

                # Draw the canvas
                self.prediction_canvas_combined.draw()
            except Exception as e:
                logger.error(f"Error updating combined view: {str(e)}")
                self.prediction_canvas_combined.clear()
                self.prediction_canvas_combined.axes.text(0.5, 0.5, f"Error displaying combined view: {str(e)}",
                                                     ha='center', va='center', fontsize=12)
                self.prediction_canvas_combined.draw()

            # 2. Update the separate view (history and prediction as separate plots)
            try:
                # Clear the canvas first
                self.prediction_canvas_separate.clear()

                # Get the model and forecast data
                if model_name == "Prophet":
                    model_data = self.prophet_model.models.get(key, {})
                    forecast_data = self.prophet_model.forecasts.get(key, None)
                else:  # XGBoost
                    model_data = self.xgboost_model.models.get(key, {})
                    forecast_data = self.xgboost_model.forecasts.get(key, None)

                # Add a label to show the MAPE in the status bar
                if mape_value is not None:
                    self.status_label.setText(f"Forecast display updated. {mape_text} (lower is better)")

                if model_data and forecast_data is not None:
                    # Get the history data
                    if model_name == "Prophet":
                        model = model_data.get('model', None)
                        if model is not None and hasattr(model, 'history'):
                            history = model.history.copy()  # Make a copy to avoid modifying the original
                        else:
                            history = model_data.get('data', pd.DataFrame()).copy()
                    else:  # XGBoost
                        history = model_data.get('data', pd.DataFrame()).copy()

                    if not history.empty and forecast_data is not None:
                        # Set colors
                        historical_color = '#1f77b4'  # Blue
                        forecast_color = '#ff7f0e'    # Orange

                        # Plot historical data and forecast with different colors
                        self.prediction_canvas_separate.axes.plot(history['ds'], history['y'], 'o-',
                                                              color=historical_color, label='Historical')
                        self.prediction_canvas_separate.axes.plot(forecast_data['ds'], forecast_data['yhat'], 'o-',
                                                              color=forecast_color, label='Forecast')

                        # Add a vertical line to separate history and forecast
                        if len(history) > 0 and len(forecast_data) > 0:
                            last_date = history['ds'].max()
                            self.prediction_canvas_separate.axes.axvline(x=last_date, color='black',
                                                                     linestyle='--', alpha=0.7)

                        # Add labels and title
                        self.prediction_canvas_separate.axes.set_title(f"History & Forecast: {customer} - {product}")
                        self.prediction_canvas_separate.axes.set_xlabel("Date")
                        self.prediction_canvas_separate.axes.set_ylabel("Quantity")
                        self.prediction_canvas_separate.axes.grid(True, linestyle='--', alpha=0.7)
                        self.prediction_canvas_separate.axes.legend()

                        # Format x-axis dates
                        date_formatter = plt.matplotlib.dates.DateFormatter('%b %Y')
                        self.prediction_canvas_separate.axes.xaxis.set_major_formatter(date_formatter)
                        plt.setp(self.prediction_canvas_separate.axes.get_xticklabels(), rotation=45, ha='right')
                else:
                    self.prediction_canvas_separate.axes.text(0.5, 0.5, f"No forecast data available for {customer} - {product}",
                                                         ha='center', va='center', fontsize=12)

                # Draw the separate view
                self.prediction_canvas_separate.draw()
            except Exception as e:
                logger.error(f"Error updating separate view: {str(e)}")
                self.prediction_canvas_separate.clear()
                self.prediction_canvas_separate.axes.text(0.5, 0.5, f"Error displaying separate view: {str(e)}",
                                                     ha='center', va='center', fontsize=12)
                self.prediction_canvas_separate.draw()

            # Update status
            self.status_label.setText("Forecast display updated")

            # If using XGBoost, also show feature importance in a separate window
            if model_name == "XGBoost" and key in self.xgboost_model.models:
                try:
                    success, message, feature_importance = self.xgboost_model.get_feature_importance(customer, product)
                    if success and feature_importance is not None:
                        # Show top 5 features in a message box
                        top_features = feature_importance.head(5)
                        feature_text = "\n".join([f"{row['Feature']}: {row['Importance']:.4f}"
                                              for _, row in top_features.iterrows()])
                        QMessageBox.information(self, "XGBoost Feature Importance",
                                              f"Top 5 features for {customer} - {product}:\n\n{feature_text}")
                except Exception as e:
                    logger.warning(f"Error showing feature importance: {str(e)}")
        except Exception as e:
            logger.error(f"Error in update_prediction_display: {str(e)}")
            # Clear both canvases and show error message
            self.prediction_canvas_combined.clear()
            self.prediction_canvas_combined.axes.text(0.5, 0.5, f"Error displaying forecast: {str(e)}",
                                                 ha='center', va='center', fontsize=12)
            self.prediction_canvas_combined.draw()

            self.prediction_canvas_separate.clear()
            self.prediction_canvas_separate.axes.text(0.5, 0.5, f"Error displaying forecast: {str(e)}",
                                                 ha='center', va='center', fontsize=12)
            self.prediction_canvas_separate.draw()

            self.status_label.setText(f"Error updating display: {str(e)}")

    def process_all_data(self):
        """Process all data from the raw data folder.

        This method processes all Excel files in the 'data' folder (or creates it if it doesn't exist)
        and converts them to the structured dataset format used by the application.

        It uses the functionality from process_all_data.py to handle the actual processing.
        """
        # Show confirmation dialog
        reply = QMessageBox.question(
            self, "Process All Data",
            "This will process all Excel files in the data folder and may take some time. Continue?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.No:
            return

        # Show progress
        self.status_label.setText("Preparing to process Excel files...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(5)
        QApplication.processEvents()

        try:
            # Get the script directory
            script_dir = os.path.dirname(os.path.abspath(__file__))

            # Set up directory paths for input and output
            data_dir = os.path.join(script_dir, "data")
            output_dir = os.path.join(script_dir, "dataset")

            # Create directories if they don't exist
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                self.status_label.setText("Created data directory. Please add Excel files and try again.")
                QMessageBox.information(self, "Directory Created",
                                      f"The data directory has been created at:\n{data_dir}\n\nPlease add your Excel files to this directory and try again.")
                self.progress_bar.setVisible(False)
                return

            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Update progress
            self.progress_bar.setValue(20)
            QApplication.processEvents()

            # Find all Excel files in the data directory (including subdirectories)
            excel_files = []
            for root, dirs, files in os.walk(data_dir):
                for file in files:
                    if file.endswith(".xlsx") or file.endswith(".xls"):
                        excel_files.append(os.path.join(root, file))

            if not excel_files:
                self.status_label.setText("No Excel files found in the data directory.")
                QMessageBox.warning(self, "No Files Found",
                                  f"No Excel files were found in the data directory:\n{data_dir}\n\nPlease add your Excel files and try again.")
                self.progress_bar.setVisible(False)
                return

            # Update progress
            self.progress_bar.setValue(30)
            QApplication.processEvents()

            # Process the files
            self.status_label.setText(f"Processing {len(excel_files)} Excel files...")

            # Import the processing function from process_all_data.py
            import importlib.util
            spec = importlib.util.spec_from_file_location("process_all_data", "process_all_data.py")
            process_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(process_module)

            # Redirect stdout to capture output
            from io import StringIO
            import sys
            original_stdout = sys.stdout
            captured_output = StringIO()
            sys.stdout = captured_output

            # Override the paths in the module
            process_module.data_dir = data_dir
            process_module.output_dir = output_dir

            # Create a worker thread for processing
            class ProcessingThread(QThread):
                progress_signal = pyqtSignal(int, str)
                finished_signal = pyqtSignal(bool, str, int, int, int)

                def __init__(self, process_module):
                    super().__init__()
                    self.process_module = process_module

                def run(self):
                    try:
                        # Send progress update
                        self.progress_signal.emit(10, "Finding Excel files...")

                        # Process the files
                        self.progress_signal.emit(20, "Processing Excel files...")
                        # Call the process_all_files function from process_all_data.py
                        num_files, num_customers, num_products = self.process_module.process_all_files()

                        # Signal success with statistics
                        self.progress_signal.emit(90, "Finishing up...")
                        self.finished_signal.emit(True, "", num_files, num_customers, num_products)

                    except Exception as e:
                        # Signal error
                        error_message = f"Error processing Excel files: {str(e)}\n\n{traceback.format_exc()}"
                        self.finished_signal.emit(False, error_message, 0, 0, 0)

            # Create and configure the processing thread
            self.processing_thread = ProcessingThread(process_module)

            # Connect signals
            self.processing_thread.progress_signal.connect(self.update_processing_progress)
            self.processing_thread.finished_signal.connect(self.processing_finished)

            # Create cancel button
            self.cancel_processing_btn = QPushButton("Cancel")
            self.cancel_processing_btn.clicked.connect(self.cancel_processing)
            self.cancel_processing_btn.setStyleSheet("font-weight: bold;")
            self.cancel_processing_btn.setMinimumHeight(30)  # Make the button taller

            # Add it next to the process button
            try:
                # Get the parent of the process button
                parent_layout = self.process_all_btn.parent().layout()
                if parent_layout:
                    parent_layout.addWidget(self.cancel_processing_btn)
                else:
                    # Fallback: add it to the main window
                    self.layout().addWidget(self.cancel_processing_btn)
            except Exception as e:
                print(f"Error adding cancel button: {e}")
                # Fallback: add it to the main window
                self.layout().addWidget(self.cancel_processing_btn)

            # Disable the process button while processing
            self.process_all_btn.setEnabled(False)

            # Start the thread
            self.processing_thread.start()

            # We'll restore stdout in the processing_finished method
            # Don't restore it here as it would be premature
            return  # Exit the method to prevent further execution

            # The rest of the processing will be handled by the thread
            # We'll show the results in the processing_finished method

        except Exception as e:
            logger.error(f"Error processing data: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to process data: {str(e)}")
            self.status_label.setText("Error processing data.")
        finally:
            # Hide progress bar after a delay
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

    def export_forecast(self):
        """Export forecast results."""
        customer = self.prediction_customer_combo.currentText()
        product = self.prediction_product_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select both customer and product")
            return

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Forecast", "", "CSV Files (*.csv)")

        if not file_path:
            return

        # Ensure file has .csv extension
        if not file_path.lower().endswith('.csv'):
            file_path += '.csv'

        # Get the selected model name
        model_name = self.model_selection_combo.currentText()

        # Export forecast using the appropriate model
        if model_name == "Prophet":
            success, message = self.prophet_model.export_forecast(customer, product, file_path)
        elif model_name == "TCI":
            success, message = self.tci_model.export_forecast(customer, product, file_path)
        elif model_name == "Ensemble (TCI + XGBoost)":
            # For Ensemble model, copy the forecast file
            key = f"{customer}_{product}"
            forecast_path = f"ENSEMBLE_TCI_XGBOOST_FORECAST_{key.replace('/', '_')}.csv"
            if os.path.exists(forecast_path):
                # Copy the file to the specified location
                import shutil
                shutil.copy(forecast_path, file_path)
                success = True
                message = f"Forecast exported to {file_path}"
            else:
                # Check in the models directory
                model_dir = f"models/ensemble_tci_xgboost/{key.replace('/', '_')}"
                model_forecast = f"{model_dir}/forecast.csv"
                if os.path.exists(model_forecast):
                    import shutil
                    shutil.copy(model_forecast, file_path)
                    success = True
                    message = f"Forecast exported to {file_path}"
                else:
                    success = False
                    message = f"No forecast available for {customer} - {product}"
        elif model_name == "TCI Premium":
            # For TCI Premium model, copy the forecast file
            key = f"{customer}_{product}"
            safe_key = key.replace('/', '_')
            model_dir = f"models/tci_premium/{safe_key}"
            model_forecast = f"{model_dir}/forecast.csv"
            if os.path.exists(model_forecast):
                import shutil
                shutil.copy(model_forecast, file_path)
                success = True
                message = f"Forecast exported to {file_path}"
            else:
                success = False
                message = f"No forecast available for {customer} - {product}"
        elif model_name == "TCI-fix":
            # For TCI-fix model, export the forecast
            try:
                # Check if the model exists (new path format)
                model_dir = f"models/tci_fix/{customer.replace('/', '_')}_{product.replace('/', '_')}"
                model_path = os.path.join(model_dir, "model.pkl")

                # Also check the old path format for backward compatibility
                old_model_path = f"models/tci_fix_{customer}_{product}.pkl"

                # Use the path that exists
                if os.path.exists(model_path):
                    use_model_path = model_path
                elif os.path.exists(old_model_path):
                    use_model_path = old_model_path
                else:
                    use_model_path = None
                if use_model_path is None:
                    success = False
                    message = f"No trained model found for {customer} - {product}"
                else:
                    # Create a temporary directory for extraction
                    import tempfile
                    temp_dir = tempfile.mkdtemp(prefix="tci_fix_export_")

                    # Import the extraction function
                    from extract_tci_fix_results import extract_tci_fix_results

                    # Get the date range from the GUI
                    start_year = self.prediction_year_combo.currentText()
                    if start_year == "Next Year":
                        # Use the next year after the last data point
                        data = self.data_manager.get_product_data(customer, product)
                        if data is not None and len(data) > 0:
                            data['date'] = pd.to_datetime(data['date'])
                            last_year = data['date'].dt.year.max()
                            start_year = str(last_year + 1)
                        else:
                            start_year = "2023"  # Default if no data

                    # Set date range for 24 months
                    if start_year == "After Historical Data":
                        # Handle the special case for "After Historical Data"
                        # Get the last date from the historical data and start from the next year
                        try:
                            # Load the data to find the last date
                            data_path = os.path.join("dataset", customer, f"{product}.csv")
                            if os.path.exists(data_path):
                                data = pd.read_csv(data_path)
                                data['date'] = pd.to_datetime(data['date'])
                                last_date = data['date'].max()
                                start_year = str(last_date.year + 1)
                            else:
                                start_year = "2023"  # Default if no data file
                        except Exception as e:
                            print(f"Error determining start year from historical data: {str(e)}")
                            start_year = "2023"  # Default if there's an error

                    # Now we have a numeric start_year
                    start_date = f"{start_year}-01-25"
                    end_year = int(start_year) + 2
                    end_date = f"{end_year}-12-25"

                    # Extract results
                    success = extract_tci_fix_results(customer, product, start_date, end_date,
                                                     output_folder=temp_dir, dataset_folder="dataset")

                    if success:
                        # Copy the CSV file to the specified location
                        temp_csv = os.path.join(temp_dir, f"{customer}_{product}_predictions.csv")
                        if os.path.exists(temp_csv):
                            import shutil
                            shutil.copy(temp_csv, file_path)
                            message = f"Forecast exported to {file_path}"

                            # Also copy the plots to the plots directory
                            os.makedirs("plots", exist_ok=True)
                            for plot_file in os.listdir(temp_dir):
                                if plot_file.endswith(".png"):
                                    shutil.copy(
                                        os.path.join(temp_dir, plot_file),
                                        os.path.join("plots", plot_file)
                                    )
                        else:
                            success = False
                            message = f"Failed to generate forecast for {customer} - {product}"
                    else:
                        message = f"Failed to extract results for {customer} - {product}"

                    # Clean up temporary directory
                    import shutil
                    shutil.rmtree(temp_dir)
            except Exception as e:
                success = False
                message = f"Error exporting forecast: {str(e)}"
                logger.error(f"Error exporting TCI-fix forecast: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
        else:  # Unknown model
            success = False
            message = f"Unknown model: {model_name}"

        if success:
            QMessageBox.information(self, "Success", message)
        else:       
            QMessageBox.critical(self, "Error", message)

    # setup_metrics_table method removed

    def update_processing_progress(self, value, message):
        """Update the progress bar and status message during processing."""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        QApplication.processEvents()

    def cancel_processing(self):
        """Cancel the processing operation."""
        if hasattr(self, 'processing_thread') and self.processing_thread.isRunning():
            # Terminate the thread
            self.processing_thread.terminate()
            self.processing_thread.wait()

            # Update UI
            self.status_label.setText("Processing cancelled")
            self.progress_bar.setVisible(False)
            self.process_all_btn.setEnabled(True)

            # Remove cancel button
            if hasattr(self, 'cancel_processing_btn'):
                try:
                    self.cancel_processing_btn.setParent(None)
                    self.cancel_processing_btn.deleteLater()
                except Exception as e:
                    print(f"Error removing cancel button: {e}")

    def processing_finished(self, success, message, num_files=0, num_customers=0, num_products=0):
        """Handle completion of the processing operation."""
        # Restore stdout if needed
        try:
            # These variables should be in the local scope of process_all_data
            # We need to access them through the frame locals
            import inspect
            frame = inspect.currentframe()
            while frame:
                if 'original_stdout' in frame.f_locals:
                    sys.stdout = frame.f_locals['original_stdout']
                    break
                frame = frame.f_back
        except Exception as e:
            print(f"Error restoring stdout: {e}")

        # Get the captured output
        output = ""  # Default empty output
        try:
            # Try to get the captured output from the frame locals
            frame = inspect.currentframe()
            while frame:
                if 'captured_output' in frame.f_locals:
                    output = frame.f_locals['captured_output'].getvalue()
                    break
                frame = frame.f_back
        except Exception as e:
            print(f"Error getting captured output: {e}")

        # Remove cancel button
        if hasattr(self, 'cancel_processing_btn'):
            try:
                self.cancel_processing_btn.setParent(None)
                self.cancel_processing_btn.deleteLater()
            except Exception as e:
                print(f"Error removing cancel button: {e}")

        # Update UI
        self.progress_bar.setValue(100)
        self.progress_bar.setVisible(False)
        self.process_all_btn.setEnabled(True)

        if success:
            # Show success message with statistics
            self.status_label.setText("Data processing complete!")

            # Create a more detailed success message with statistics
            stats_message = f"Successfully processed Excel files to CSV format.\n\n"
            stats_message += f"📊 Processing Statistics:\n"
            stats_message += f"- Files processed: {num_files}\n"
            stats_message += f"- Customers found: {num_customers}\n"
            stats_message += f"- Products processed: {num_products}\n\n"

            # Add the output if available
            if output.strip():
                stats_message += f"Detailed output:\n{output}"

            QMessageBox.information(self, "Processing Complete", stats_message)

            # Reload the data
            self.load_data_from_folder()
        else:
            # Show error message
            self.status_label.setText("Processing failed")
            logger.error(message)
            QMessageBox.critical(self, "Processing Error", message)

    def calculate_metrics(self):
        """Calculate and display metrics for selected customer and product."""
        print("Calculating metrics...")

        customer = self.metrics_customer_combo.currentText()
        product = self.metrics_product_combo.currentText()
        model_name = self.metrics_model_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select a customer and product first.")
            return

        # Show progress
        self.status_label.setText(f"Calculating metrics for {customer} - {product}...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(10)
        QApplication.processEvents()

        try:
            # Get the appropriate model
            if model_name == "TCI":
                model = self.tci_model
            elif model_name == "TCI Premium":
                model = self.tci_premium_model
            elif model_name == "TCI-fix":
                # For TCI-fix, we need to load the metrics from the saved file
                model_dir = f"models/tci_fix/{customer.replace('/', '_')}_{product.replace('/', '_')}"
                metrics_path = os.path.join(model_dir, "metrics.json")

                if os.path.exists(metrics_path):
                    with open(metrics_path, 'r') as f:
                        metrics = json.load(f)

                    # Update the table
                    self.update_metrics_table(metrics)
                    self.status_label.setText(f"Metrics loaded for {customer} - {product}")
                    self.progress_bar.setVisible(False)
                    return
                else:
                    QMessageBox.warning(self, "No Metrics Found",
                                      f"No metrics found for {customer} - {product} with {model_name} model.\n\n"
                                      f"Please train the model first.")
                    self.progress_bar.setVisible(False)
                    return
            else:
                QMessageBox.warning(self, "Invalid Model", f"Unknown model: {model_name}")
                self.progress_bar.setVisible(False)
                return

            # Check if the model has been trained
            key = f"{customer}_{product}"
            if not hasattr(model, 'models') or key not in model.models:
                QMessageBox.warning(self, "Model Not Trained",
                                  f"No {model_name} model has been trained for {customer} - {product}.\n\n"
                                  f"Please go to the Prediction tab first, select {customer} and {product}, "
                                  f"and train the model.")
                self.progress_bar.setVisible(False)
                return

            # Calculate metrics
            self.progress_bar.setValue(30)
            QApplication.processEvents()

            # Get the model data
            model_data = model.models[key]
            data = model_data.get('data')

            # Generate forecast if not already available
            if key not in model.forecasts:
                success, message, forecast = model.predict(customer, product, periods=6, freq='M', include_history=True)
                if not success:
                    QMessageBox.warning(self, "Warning", f"Failed to generate forecast: {message}")
                    self.progress_bar.setVisible(False)
                    return
            else:
                forecast = model.forecasts[key]

            # Merge actual and predicted values
            merged_data = pd.merge(data, forecast[['ds', 'yhat']], on='ds')

            # Calculate metrics
            metrics = {}

            # Calculate MAPE (Mean Absolute Percentage Error)
            mape_data = merged_data[merged_data['y'] > 0].copy()
            if len(mape_data) > 0:
                mape_data['ape'] = 100 * abs(mape_data['y'] - mape_data['yhat']) / mape_data['y']
                metrics['MAPE'] = mape_data['ape'].mean()

            # Calculate RMSE (Root Mean Square Error)
            metrics['RMSE'] = np.sqrt(((merged_data['y'] - merged_data['yhat']) ** 2).mean())

            # Calculate MAE (Mean Absolute Error)
            metrics['MAE'] = abs(merged_data['y'] - merged_data['yhat']).mean()

            # Calculate R² (Coefficient of Determination)
            ss_total = ((merged_data['y'] - merged_data['y'].mean()) ** 2).sum()
            ss_residual = ((merged_data['y'] - merged_data['yhat']) ** 2).sum()
            if ss_total > 0:
                metrics['R2'] = 1 - (ss_residual / ss_total)

            # Update the table
            self.update_metrics_table(metrics)

            # Complete
            self.progress_bar.setValue(100)
            self.status_label.setText(f"Metrics calculated for {customer} - {product}")
            QApplication.processEvents()

        except Exception as e:
            logger.error(f"Error calculating metrics: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error calculating metrics: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def update_metrics_table(self, metrics):
        """Update the metrics table with calculated values."""
        # MAPE
        if 'MAPE' in metrics:
            mape_value = metrics['MAPE']
            mape_text = f"{mape_value:.2f}%"
            # Add quality assessment
            if mape_value < 10:
                mape_text += " (Excellent)"
            elif mape_value < 20:
                mape_text += " (Good)"
            elif mape_value < 30:
                mape_text += " (Fair)"
            else:
                mape_text += " (Poor)"
            self.metrics_table.setItem(0, 1, QTableWidgetItem(mape_text))
        else:
            self.metrics_table.setItem(0, 1, QTableWidgetItem("Not available"))

        # RMSE
        if 'RMSE' in metrics:
            rmse_text = f"{metrics['RMSE']:.2f}"
            self.metrics_table.setItem(1, 1, QTableWidgetItem(rmse_text))
        else:
            self.metrics_table.setItem(1, 1, QTableWidgetItem("Not available"))

        # MAE
        if 'MAE' in metrics:
            mae_text = f"{metrics['MAE']:.2f}"
            self.metrics_table.setItem(2, 1, QTableWidgetItem(mae_text))
        else:
            self.metrics_table.setItem(2, 1, QTableWidgetItem("Not available"))

        # R²
        if 'R2' in metrics:
            r2_value = metrics['R2']
            r2_text = f"{r2_value:.4f}"
            # Add quality assessment
            if r2_value > 0.9:
                r2_text += " (Excellent)"
            elif r2_value > 0.8:
                r2_text += " (Good)"
            elif r2_value > 0.6:
                r2_text += " (Fair)"
            else:
                r2_text += " (Poor)"
            self.metrics_table.setItem(3, 1, QTableWidgetItem(r2_text))
        else:
            self.metrics_table.setItem(3, 1, QTableWidgetItem("Not available"))

        # Enable the plot and export buttons
        self.plot_metrics_btn.setEnabled(True)
        self.export_metrics_btn.setEnabled(True)

    def plot_metrics(self):
        """Generate a plot of the metrics for the selected customer-product combination."""
        customer = self.metrics_customer_combo.currentText()
        product = self.metrics_product_combo.currentText()
        model_name = self.metrics_model_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select a customer and product first.")
            return

        # Get the metrics values from the table
        metrics = {}
        metric_names = ["MAPE", "RMSE", "MAE", "R²"]

        for i, name in enumerate(metric_names):
            value_item = self.metrics_table.item(i, 1)
            if value_item and value_item.text() != "Not available" and value_item.text() != "Not calculated":
                try:
                    # Extract numeric value from text (remove any special formatting)
                    value_text = value_item.text().split(" ")[0]  # Get the first part before any space
                    if '%' in value_text:
                        value = float(value_text.replace('%', ''))
                    else:
                        value = float(value_text)
                    metrics[name] = value
                except ValueError:
                    print(f"Could not convert {value_item.text()} to float")

        if not metrics:
            QMessageBox.warning(self, "Warning", "No metrics available to plot.")
            return

        try:
            # Clear the canvas
            self.metrics_canvas.figure.clear()

            # Create the plot
            ax = self.metrics_canvas.figure.add_subplot(111)

            # Set up colors for each metric
            colors = {
                'MAPE': '#FF9999',  # Light red
                'RMSE': '#66B2FF',  # Light blue
                'MAE': '#99CC99',   # Light green
                'R²': '#FFCC66'     # Light orange
            }

            # Create bar chart
            bars = []
            values = []
            labels = []
            colors_list = []

            for name, value in metrics.items():
                labels.append(name)
                values.append(value)
                colors_list.append(colors[name])

            # Create the bar chart
            bars = ax.bar(labels, values, color=colors_list, alpha=0.7)

            # Add value labels on top of bars
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{height:.2f}', ha='center', va='bottom')

            # Add title and labels
            ax.set_title(f"Model Quality Metrics for {customer} - {product} ({model_name})")
            ax.set_ylabel("Value")

            # Add a horizontal line at y=1 for R² reference
            if 'R²' in metrics:
                ax.axhline(y=1, color='r', linestyle='--', alpha=0.5)
                ax.text(0, 1.05, "Perfect R² (1.0)", color='r', alpha=0.7)

            # Add grid
            ax.grid(True, linestyle='--', alpha=0.7)

            # Add interpretation guide
            interpretation_text = "Interpretation Guide:\n"
            if 'MAPE' in metrics:
                mape_value = metrics['MAPE']
                if mape_value < 10:
                    mape_quality = "Excellent"
                elif mape_value < 20:
                    mape_quality = "Good"
                elif mape_value < 30:
                    mape_quality = "Fair"
                else:
                    mape_quality = "Poor"
                interpretation_text += f"MAPE: {mape_quality} (lower is better)\n"

            if 'R²' in metrics:
                r2_value = metrics['R²']
                if r2_value > 0.9:
                    r2_quality = "Excellent"
                elif r2_value > 0.8:
                    r2_quality = "Good"
                elif r2_value > 0.6:
                    r2_quality = "Fair"
                else:
                    r2_quality = "Poor"
                interpretation_text += f"R²: {r2_quality} (higher is better)"

            # Add the interpretation text
            props = dict(boxstyle='round', facecolor='white', alpha=0.7)
            ax.text(0.02, 0.02, interpretation_text, transform=ax.transAxes,
                    bbox=props, fontsize=9)

            # Make the canvas visible
            self.metrics_canvas_frame.setVisible(True)

            # Draw the canvas
            self.metrics_canvas.draw()

            # Adjust layout to ensure everything fits
            self.metrics_canvas.figure.tight_layout()

        except Exception as e:
            print(f"Error generating metrics plot: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to generate metrics plot: {str(e)}")

    def export_metrics_data(self):
        """Export metrics data to CSV file."""
        customer = self.metrics_customer_combo.currentText()
        product = self.metrics_product_combo.currentText()
        model_name = self.metrics_model_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select a customer and product first.")
            return

        # Get the metrics values from the table
        metrics = {}
        metric_names = ["MAPE", "RMSE", "MAE", "R²"]

        for i, name in enumerate(metric_names):
            value_item = self.metrics_table.item(i, 1)
            if value_item and value_item.text() != "Not available" and value_item.text() != "Not calculated":
                metrics[name] = value_item.text()

        if not metrics:
            QMessageBox.warning(self, "Warning", "No metrics available to export.")
            return

        # Ask user for save location
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Metrics", f"{customer}_{product}_metrics.csv", "CSV Files (*.csv)"
        )

        if not filename:
            return  # User cancelled

        try:
            # Ensure filename has .csv extension
            if not filename.lower().endswith('.csv'):
                filename += '.csv'

            # Write to CSV
            with open(filename, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                # Write header
                writer.writerow(["Customer", "Product", "Model", "Metric", "Value"])

                # Write data rows
                for name, value in metrics.items():
                    # Clean up the value (remove any special formatting)
                    clean_value = value.split(" ")[0]  # Get the first part before any space
                    writer.writerow([customer, product, model_name, name, clean_value])

            # Show success message with the full path
            QMessageBox.information(self, "Success", f"Metrics exported to {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export metrics: {str(e)}")

    def update_metrics_products(self):
        """Update the product list in the metrics tab based on selected customer."""
        customer = self.metrics_customer_combo.currentText()
        if not customer:
            return

        # Get products for this customer
        products = self.data_manager.get_products(customer)

        # Remember current selection
        current_product = self.metrics_product_combo.currentText()

        # Update the combo box
        self.metrics_product_combo.clear()
        self.metrics_product_combo.addItems(products)

        # Restore previous selection if possible
        if current_product in products:
            index = self.metrics_product_combo.findText(current_product)
            if index >= 0:
                self.metrics_product_combo.setCurrentIndex(index)

    def create_metrics_tab(self):
        """Create the model metrics tab with scrolling support."""
        # Create a base widget for the tab
        metrics_tab = QWidget()

        # Create a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)  # Allow the widget to resize
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold the content
        scroll_content = QWidget()

        # Set the content widget for the scroll area
        scroll_area.setWidget(scroll_content)

        # Create a layout for the tab
        tab_layout = QVBoxLayout(metrics_tab)
        tab_layout.addWidget(scroll_area)

        # Create a layout for the content
        layout = QVBoxLayout(scroll_content)

        # Create selection group
        selection_group = QGroupBox("Select Model and Data")
        selection_layout = QFormLayout()

        # Customer selection
        self.metrics_customer_combo = QComboBox()
        selection_layout.addRow("Customer:", self.metrics_customer_combo)

        # Product selection
        self.metrics_product_combo = QComboBox()
        selection_layout.addRow("Product:", self.metrics_product_combo)

        # Model selection
        self.metrics_model_combo = QComboBox()
        self.metrics_model_combo.addItems(["TCI", "TCI Premium", "TCI-fix"])
        selection_layout.addRow("Model:", self.metrics_model_combo)

        # Calculate button
        self.calculate_metrics_btn = QPushButton("Calculate Metrics")
        selection_layout.addRow("", self.calculate_metrics_btn)

        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)

        # Metrics display group
        metrics_group = QGroupBox("Model Quality Metrics")
        metrics_layout = QVBoxLayout()

        # Create metrics table
        self.metrics_table = QTableWidget(4, 2)
        self.metrics_table.setHorizontalHeaderLabels(["Metric", "Value"])
        self.metrics_table.setVerticalHeaderLabels(["MAPE", "RMSE", "MAE", "R²"])
        self.metrics_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Initialize with metric names
        metric_names = ["MAPE", "RMSE", "MAE", "R²"]
        for row, name in enumerate(metric_names):
            self.metrics_table.setItem(row, 0, QTableWidgetItem(name))
            self.metrics_table.setItem(row, 1, QTableWidgetItem("Not calculated"))

        metrics_layout.addWidget(self.metrics_table)

        # Visualization options
        viz_layout = QHBoxLayout()

        # Plot button
        self.plot_metrics_btn = QPushButton("Plot Metrics")
        self.plot_metrics_btn.setEnabled(False)
        viz_layout.addWidget(self.plot_metrics_btn)

        # Export button
        self.export_metrics_btn = QPushButton("Export Metrics")
        self.export_metrics_btn.setEnabled(False)
        viz_layout.addWidget(self.export_metrics_btn)

        metrics_layout.addLayout(viz_layout)

        # Add canvas for plots
        self.metrics_canvas_frame = QFrame()
        self.metrics_canvas_frame.setFrameShape(QFrame.StyledPanel)
        self.metrics_canvas_frame.setFrameShadow(QFrame.Raised)
        self.metrics_canvas_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        metrics_canvas_layout = QVBoxLayout(self.metrics_canvas_frame)
        self.metrics_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        metrics_canvas_layout.addWidget(self.metrics_canvas)

        # Initially hide the canvas
        self.metrics_canvas_frame.setVisible(False)

        metrics_layout.addWidget(self.metrics_canvas_frame)

        metrics_group.setLayout(metrics_layout)
        layout.addWidget(metrics_group)

        # Add the tab
        self.tabs.addTab(metrics_tab, "Metrics")

    def update_metrics_combos(self, current_customer=None, current_product=None):
        """Update customer and product combos in the metrics tab."""
        # Get all customers
        customers = self.data_manager.get_customers()

        # Update customer combo
        self.metrics_customer_combo.clear()
        self.metrics_customer_combo.addItems(customers)

        # Restore previous selection if possible
        if current_customer in customers:
            index = self.metrics_customer_combo.findText(current_customer)
            if index >= 0:
                self.metrics_customer_combo.setCurrentIndex(index)

        # Update product combo based on selected customer
        self.update_metrics_products()

        # Restore previous product selection if possible
        if current_product:
            index = self.metrics_product_combo.findText(current_product)
            if index >= 0:
                self.metrics_product_combo.setCurrentIndex(index)

    def export_components(self):
        """Export forecast components as image."""
        customer = self.prediction_customer_combo.currentText()
        product = self.prediction_product_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select both customer and product")
            return

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Components", "", "PNG Files (*.png)")

        if not file_path:
            return

        # Ensure file has .png extension
        if not file_path.lower().endswith('.png'):
            file_path += '.png'

        try:
            # Get the selected model name
            model_name = self.model_selection_combo.currentText()

            # Generate components plot using the appropriate model
            if model_name == "Prophet":
                fig = self.prophet_model.plot_components(customer, product)
            else:  # XGBoost
                fig = self.xgboost_model.plot_components(customer, product)

            # Save figure
            fig.savefig(file_path, bbox_inches='tight', dpi=300)

            QMessageBox.information(self, "Success", f"Components exported to {file_path}")

        except Exception as e:
            logger.error(f"Error exporting components: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to export components: {str(e)}")

    def export_report(self):
        """Export full report with analysis and forecast."""
        customer = self.prediction_customer_combo.currentText()
        product = self.prediction_product_combo.currentText()

        if not customer or not product:
            QMessageBox.warning(self, "Warning", "Please select both customer and product")
            return

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Report", "", "Excel Files (*.xlsx)")

        if not file_path:
            return

        # Ensure file has .xlsx extension
        if not file_path.lower().endswith('.xlsx'):
            file_path += '.xlsx'

        try:
            # Check if xlsxwriter is available
            try:
                import xlsxwriter
            except ImportError:
                QMessageBox.critical(self, "Error",
                                    "xlsxwriter package is required. Install with: pip install xlsxwriter")
                return

            # Create Excel writer
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')

            # Export historical data
            key = f"{customer}_{product}"
            if key in self.data_manager.preprocessed_data:
                historical_data = self.data_manager.preprocessed_data[key]['raw']
                historical_data.to_excel(writer, sheet_name='Historical Data', index=False)

            # Export forecast data
            if key in self.model.forecasts:
                forecast_data = self.model.forecasts[key][['ds', 'yhat', 'yhat_lower', 'yhat_upper']]
                forecast_data.columns = ['Date', 'Predicted_Quantity', 'Lower_Bound', 'Upper_Bound']
                forecast_data.to_excel(writer, sheet_name='Forecast', index=False)

            # Close writer
            writer.close()

            QMessageBox.information(self, "Success", f"Report exported to {file_path}")

        except Exception as e:
            logger.error(f"Error exporting report: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to export report: {str(e)}")
def main():
    """Start the application.

    This is the entry point of our application. It:
    1. Creates the Qt application object
    2. Initializes our main application window
    3. Shows the window to the user
    4. Starts the event loop that handles user interactions

    When the user closes the window, the application will exit gracefully.
    """
    # Suppress specific Qt warnings
    import os
    os.environ["QT_LOGGING_RULES"] = "qt.qpa.wayland=false"

    # Create the Qt application object
    app = QApplication(sys.argv)

    # Create and initialize our main application window
    window = OrderPredictionApp()

    # Show the window to the user
    window.show()

    # Start the event loop and exit when it's done
    # This line will block until the user closes the application
    sys.exit(app.exec_())


# This is the standard Python idiom to check if this file is being run directly
# (as opposed to being imported by another Python file)
if __name__ == "__main__":
    # If this file is being run directly, start the application
    main()
