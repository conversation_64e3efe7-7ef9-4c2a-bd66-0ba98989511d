"""
Model Integration Module

Provides unified interface for different forecasting models.
"""

import logging

logger = logging.getLogger(__name__)

class ModelIntegration:
    """
    Unified interface for all forecasting models.
    
    Available models:
    1. TCI (Temporal Causality Infusion)
    2. TCI Premium (Enhanced TCI with additional features)
    3. TCI-fix (TCI with external data integration)
    """
    
    def __init__(self, data_manager=None):
        """Initialize all available models."""
        self.logger = logging.getLogger(__name__)
        self.data_manager = data_manager  # Store data_manager if provided
        
        # Initialize TCI
        try:
            from tci_model import TCIModel
            self.tci = TCIModel()
            self.logger.info("Initialized TCI predictor")
        except Exception as e:
            self.logger.error(f"Error initializing TCI predictor: {str(e)}")
            self.tci = None
        
        # Initialize TCI Premium
        try:
            from tci_premium_model import TCIPremiumModel
            self.tci_premium = TCIPremiumModel()
            self.logger.info("Initialized TCI Premium predictor")
        except Exception as e:
            self.logger.error(f"Error initializing TCI Premium predictor: {str(e)}")
            self.tci_premium = None
        
        # Initialize TCI-fix
        try:
            from tci_fix_integration import TCIFixIntegration
            self.tci_fix = TCIFixIntegration()
            self.logger.info("Initialized TCI-fix predictor")
        except Exception as e:
            self.logger.error(f"Error initializing TCI-fix predictor: {str(e)}")
            self.tci_fix = None
    
    def get_available_models(self):
        """Get list of available models."""
        return ["TCI", "TCI Premium", "TCI-fix"]
    
    def train_model(self, model_name, customer, product, force_retrain=False):
        """Train a specific model for a customer-product combination."""
        
        if model_name == "TCI":
            if self.tci is not None:
                return self.tci.train_model(customer, product, force_retrain)
            else:
                return False, "TCI model not available"
        
        elif model_name == "TCI Premium":
            if self.tci_premium is not None:
                return self.tci_premium.train_model(customer, product, force_retrain)
            else:
                return False, "TCI Premium model not available"
        
        elif model_name == "TCI-fix":
            if self.tci_fix is not None:
                return self.tci_fix.train_model(customer, product, force_retrain)
            else:
                return False, "TCI-fix model not available"
        
        else:
            return False, f"Unknown model: {model_name}"
    
    def predict_future(self, model_name, customer, product, periods=12, start_date=None):
        """Generate predictions using a specific model."""
        
        if model_name == "TCI":
            if self.tci is not None:
                return self.tci.predict_future(customer, product, periods, start_date)
            else:
                self.logger.error("TCI model not available")
                return None
        
        elif model_name == "TCI Premium":
            if self.tci_premium is not None:
                return self.tci_premium.predict_future(customer, product, periods, start_date)
            else:
                self.logger.error("TCI Premium model not available")
                return None
        
        elif model_name == "TCI-fix":
            if self.tci_fix is not None:
                return self.tci_fix.predict_future(customer, product, periods, start_date)
            else:
                self.logger.error("TCI-fix model not available")
                return None
        
        else:
            self.logger.error(f"Unknown model: {model_name}")
            return None
