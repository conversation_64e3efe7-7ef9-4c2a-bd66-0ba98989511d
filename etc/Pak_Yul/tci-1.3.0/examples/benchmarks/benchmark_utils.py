"""
Utility functions for TCI benchmarking.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Tuple, Optional
import networkx as nx

# Set up matplotlib for high-quality figures
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12
plt.rcParams['legend.fontsize'] = 12

def create_extension(name: str, **kwargs) -> Any:
    """Create an extension by name with given parameters."""
    try:
        from tci.extensions import (
            GAT, SparseCausal, MAML, DynaQ, PER, ThompsonSampling, 
            IncrementalCausalDiscovery, CausalDimReduction
        )
    except ImportError:
        print("Warning: Could not import all extensions. Some extensions may not be available.")
        # Define empty classes for extensions that couldn't be imported
        class DummyExtension:
            def __init__(self, **kwargs):
                pass
        
        # Try to import each extension individually
        try:
            from tci.extensions import GAT
        except ImportError:
            GAT = DummyExtension
        
        try:
            from tci.extensions import SparseCausal
        except ImportError:
            SparseCausal = DummyExtension
        
        try:
            from tci.extensions import MAML
        except ImportError:
            MAML = DummyExtension
        
        try:
            from tci.extensions import DynaQ
        except ImportError:
            DynaQ = DummyExtension
        
        try:
            from tci.extensions import PER
        except ImportError:
            PER = DummyExtension
        
        try:
            from tci.extensions import ThompsonSampling
        except ImportError:
            ThompsonSampling = DummyExtension
        
        try:
            from tci.extensions import IncrementalCausalDiscovery
        except ImportError:
            IncrementalCausalDiscovery = DummyExtension
        
        try:
            from tci.extensions import CausalDimReduction
        except ImportError:
            CausalDimReduction = DummyExtension
    
    extensions = {
        "gat": lambda: GAT(hidden_dim=kwargs.get("hidden_dim", 64), 
                          num_heads=kwargs.get("num_heads", 4)),
        "sparse_causal": lambda: SparseCausal(sparsity_factor=kwargs.get("sparsity_factor", 0.1)),
        "maml": lambda: MAML(inner_lr=kwargs.get("inner_lr", 0.01), 
                            meta_lr=kwargs.get("meta_lr", 0.001)),
        "dyna_q": lambda: DynaQ(planning_steps=kwargs.get("planning_steps", 20)),
        "per": lambda: PER(alpha=kwargs.get("alpha", 0.6), beta=kwargs.get("beta", 0.4)),
        "thompson": lambda: ThompsonSampling(num_samples=kwargs.get("num_samples", 10)),
        "incremental_causal": lambda: IncrementalCausalDiscovery(
            update_frequency=kwargs.get("update_frequency", 100)),
        "causal_dim_reduction": lambda: CausalDimReduction(
            latent_dim=kwargs.get("latent_dim", 8))
    }
    
    if name.lower() not in extensions:
        raise ValueError(f"Unknown extension: {name}")
    
    return extensions[name.lower()]()

def parse_extension_list(extension_str: str) -> List[Any]:
    """Parse a comma-separated list of extensions."""
    if not extension_str:
        return []
    
    extension_names = [name.strip() for name in extension_str.split(",")]
    return [create_extension(name) for name in extension_names]

def generate_statistical_report(ab_test_results, metrics, title, output_path):
    """Generate a detailed statistical report in HTML format."""
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    html = []
    html.append("<!DOCTYPE html>")
    html.append("<html lang='en'>")
    html.append("<head>")
    html.append("    <meta charset='UTF-8'>")
    html.append("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>")
    html.append(f"    <title>{title}</title>")
    html.append("    <style>")
    html.append("        body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 1200px; margin: 0 auto; padding: 20px; }")
    html.append("        h1, h2, h3 { color: #2c3e50; }")
    html.append("        table { width: 100%; border-collapse: collapse; margin: 20px 0; }")
    html.append("        th, td { padding: 12px; text-align: left; border: 1px solid #ddd; }")
    html.append("        th { background-color: #2c3e50; color: white; }")
    html.append("        tr:nth-child(even) { background-color: #f2f2f2; }")
    html.append("        .significant { color: green; font-weight: bold; }")
    html.append("        .not-significant { color: red; }")
    html.append("        .effect-small { color: #888; }")
    html.append("        .effect-medium { color: #0066cc; }")
    html.append("        .effect-large { color: #006600; font-weight: bold; }")
    html.append("        .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }")
    html.append("        footer { margin-top: 50px; text-align: center; color: #666; }")
    html.append("    </style>")
    html.append("</head>")
    html.append("<body>")
    
    # Header
    html.append(f"<h1>{title}</h1>")
    html.append("<p>Statistical analysis of benchmark results</p>")
    
    # Summary
    html.append("<div class='summary'>")
    html.append("<h2>Summary</h2>")
    
    # Count significant results
    significant_count = sum(1 for result in ab_test_results.values() if result['significant'])
    total_count = len(ab_test_results)
    
    html.append(f"<p><strong>{significant_count}/{total_count}</strong> metrics showed statistically significant differences (α = {ab_test_results[list(ab_test_results.keys())[0]]['alpha']}).</p>")
    
    if significant_count > 0:
        html.append("<p>Significant metrics:</p>")
        html.append("<ul>")
        for metric, result in ab_test_results.items():
            if result['significant']:
                control_mean = np.mean(metrics[metric]["control"])
                variant_mean = np.mean(metrics[metric]["variant"])
                diff_percent = (variant_mean - control_mean) / control_mean * 100
                better_worse = "better" if (diff_percent > 0 and metric != "training_time") or (diff_percent < 0 and metric == "training_time") else "worse"
                html.append(f"<li><strong>{metric}</strong>: {better_worse} by {abs(diff_percent):.2f}% (p = {result['p_value']:.4f}, effect size = {result['effect_size']:.2f})</li>")
        html.append("</ul>")
    
    html.append("</div>")
    
    # Detailed results
    html.append("<h2>Detailed Results</h2>")
    html.append("<table>")
    html.append("<tr>")
    html.append("    <th>Metric</th>")
    html.append("    <th>Control Mean ± SD</th>")
    html.append("    <th>Variant Mean ± SD</th>")
    html.append("    <th>Difference</th>")
    html.append("    <th>Difference (%)</th>")
    html.append("    <th>p-value</th>")
    html.append("    <th>Significant</th>")
    html.append("    <th>Effect Size</th>")
    html.append("    <th>Effect Interpretation</th>")
    html.append("</tr>")
    
    for metric, result in ab_test_results.items():
        control_mean = np.mean(metrics[metric]["control"])
        control_std = np.std(metrics[metric]["control"])
        variant_mean = np.mean(metrics[metric]["variant"])
        variant_std = np.std(metrics[metric]["variant"])
        diff = variant_mean - control_mean
        diff_percent = diff / control_mean * 100 if control_mean != 0 else float('inf')
        
        # Determine CSS classes
        significant_class = "significant" if result['significant'] else "not-significant"
        
        if abs(result['effect_size']) < 0.2:
            effect_class = "effect-small"
        elif abs(result['effect_size']) < 0.8:
            effect_class = "effect-medium"
        else:
            effect_class = "effect-large"
        
        html.append("<tr>")
        html.append(f"    <td>{metric}</td>")
        html.append(f"    <td>{control_mean:.4f} ± {control_std:.4f}</td>")
        html.append(f"    <td>{variant_mean:.4f} ± {variant_std:.4f}</td>")
        html.append(f"    <td>{diff:.4f}</td>")
        html.append(f"    <td>{diff_percent:.2f}%</td>")
        html.append(f"    <td>{result['p_value']:.4f}</td>")
        html.append(f"    <td class='{significant_class}'>{result['significant']}</td>")
        html.append(f"    <td class='{effect_class}'>{result['effect_size']:.2f}</td>")
        html.append(f"    <td class='{effect_class}'>{result['effect_size_interpretation']}</td>")
        html.append("</tr>")
    
    html.append("</table>")
    
    # Distribution plots
    html.append("<h2>Distribution Plots</h2>")
    
    for metric in metrics:
        # Create distribution plot
        plt.figure(figsize=(10, 6))
        
        # Plot distributions
        sns.histplot(metrics[metric]["control"], kde=True, label="Control", alpha=0.6)
        sns.histplot(metrics[metric]["variant"], kde=True, label="Variant", alpha=0.6)
        
        # Add vertical lines for means
        plt.axvline(np.mean(metrics[metric]["control"]), color='blue', linestyle='--', linewidth=2)
        plt.axvline(np.mean(metrics[metric]["variant"]), color='orange', linestyle='--', linewidth=2)
        
        plt.title(f"Distribution of {metric}")
        plt.xlabel(metric)
        plt.ylabel("Frequency")
        plt.legend()
        plt.grid(alpha=0.3)
        
        # Save plot
        plot_dir = os.path.dirname(output_path)
        plot_path = os.path.join(plot_dir, f"{metric}_distribution.png")
        plt.savefig(plot_path)
        plt.close()
        
        # Add to HTML
        html.append(f"<h3>{metric}</h3>")
        html.append(f"<img src='{os.path.basename(plot_path)}' alt='Distribution of {metric}' style='max-width: 100%;'>")
    
    # Footer
    html.append("<footer>")
    html.append("    <p>TCI Framework & it's Documentation © 2025 By Yuliadi Rumanto</p>")
    html.append("</footer>")
    
    html.append("</body>")
    html.append("</html>")
    
    # Write HTML to file
    with open(output_path, "w") as f:
        f.write("\n".join(html))

def generate_learning_curves(all_metrics, output_dir):
    """Generate learning curve plots for all metrics."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all available metrics
    all_metric_names = set()
    for config in all_metrics.values():
        for seed in config.values():
            all_metric_names.update(seed.keys())
    
    # Filter for metrics that are lists (learning curves)
    curve_metrics = []
    for metric in all_metric_names:
        for config in all_metrics.values():
            for seed in config.values():
                if metric in seed and isinstance(seed[metric], list) and len(seed[metric]) > 1:
                    curve_metrics.append(metric)
                    break
            if metric in curve_metrics:
                break
    
    # Generate plots for each metric
    for metric in curve_metrics:
        plt.figure(figsize=(12, 8))
        
        # Plot each configuration
        for config_name, config in all_metrics.items():
            # Collect data for all seeds
            all_data = []
            for seed, seed_metrics in config.items():
                if metric in seed_metrics and isinstance(seed_metrics[metric], list):
                    all_data.append(seed_metrics[metric])
            
            # Skip if no data
            if not all_data:
                continue
            
            # Pad shorter sequences with NaN
            max_len = max(len(data) for data in all_data)
            padded_data = []
            for data in all_data:
                if len(data) < max_len:
                    padded_data.append(data + [np.nan] * (max_len - len(data)))
                else:
                    padded_data.append(data)
            
            # Convert to numpy array
            data_array = np.array(padded_data)
            
            # Calculate mean and std
            mean = np.nanmean(data_array, axis=0)
            std = np.nanstd(data_array, axis=0)
            
            # Plot mean and confidence interval
            x = np.arange(len(mean))
            plt.plot(x, mean, label=config_name)
            plt.fill_between(x, mean - std, mean + std, alpha=0.3)
        
        plt.title(f"Learning Curve: {metric}")
        plt.xlabel("Episode")
        plt.ylabel(metric)
        plt.legend()
        plt.grid(alpha=0.3)
        
        # Save plot
        plt.savefig(os.path.join(output_dir, f"{metric}_learning_curve.png"))
        plt.close()

def save_causal_graphs(causal_graph, state_dim, output_dir, format="png"):
    """Save causal graphs to files."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Create a NetworkX graph
    G = nx.DiGraph()
    
    # Add nodes
    for i in range(state_dim):
        G.add_node(i, label=f"X{i}")
    
    # Add edges
    if hasattr(causal_graph, "causal_edges"):
        for i, j in causal_graph.causal_edges:
            G.add_edge(i, j)
    elif isinstance(causal_graph, nx.DiGraph):
        G = causal_graph
    elif hasattr(causal_graph, "G") and hasattr(causal_graph.G, "shape"):
        # Adjacency matrix
        for i in range(causal_graph.G.shape[0]):
            for j in range(causal_graph.G.shape[1]):
                if causal_graph.G[i, j] != 0:
                    G.add_edge(i, j, weight=causal_graph.G[i, j])
    
    # Save graph visualization
    try:
        from tci.visualization import plot_causal_graph
        plot_causal_graph(
            G, 
            node_labels={i: f"X{i}" for i in range(state_dim)},
            save_path=os.path.join(output_dir, f"causal_graph.{format}")
        )
    except ImportError:
        # Fallback to NetworkX plotting
        plt.figure(figsize=(10, 10))
        pos = nx.spring_layout(G)
        nx.draw(G, pos, with_labels=True, node_color='lightblue', 
                node_size=500, arrowsize=20, font_size=12)
        plt.savefig(os.path.join(output_dir, f"causal_graph.{format}"))
        plt.close()
    
    # Save adjacency matrix
    adj_matrix = nx.to_numpy_array(G)
    np.savetxt(os.path.join(output_dir, "adjacency_matrix.csv"), adj_matrix, delimiter=",")
    
    # Save edge list
    with open(os.path.join(output_dir, "edge_list.txt"), "w") as f:
        for i, j in G.edges():
            f.write(f"{i} -> {j}\n")

def print_summary(ab_test_results, valid_metrics):
    """Print a summary of the benchmark results."""
    print("\nSummary:")
    for metric, result in ab_test_results.items():
        control_mean = np.mean(valid_metrics[metric]["control"])
        control_std = np.std(valid_metrics[metric]["control"])
        variant_mean = np.mean(valid_metrics[metric]["variant"])
        variant_std = np.std(valid_metrics[metric]["variant"])
        
        print(f"  - {metric}:")
        print(f"      Control: {control_mean:.4f} ± {control_std:.4f}")
        print(f"      Variant: {variant_mean:.4f} ± {variant_std:.4f}")
        print(f"      Diff:    {variant_mean - control_mean:.4f} ({(variant_mean - control_mean) / control_mean * 100:.2f}%)")
        print(f"      p-value: {result['p_value']:.4f} {'*' if result['significant'] else '(n.s.)'}")
        print(f"      Effect:  {result['effect_size']:.4f} ({result['effect_size_interpretation']})")
        print()
