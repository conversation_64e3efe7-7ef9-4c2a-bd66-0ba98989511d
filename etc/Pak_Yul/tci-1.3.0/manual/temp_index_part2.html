                <div class="section" id="key-features">
                    <h2>Key Features</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    Online Causal Discovery
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        TCI continuously updates its causal model as new data becomes available, allowing it to adapt to changing environments and discover new causal relationships over time.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    Temporal Reasoning
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        TCI explicitly models how causal relationships evolve over time, capturing both immediate and delayed effects between variables.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    Causal Interventions
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        TCI can perform targeted interventions to test causal hypotheses and disambiguate between different causal structures.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    Extensible Architecture
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        TCI's modular design allows for easy integration of new extensions and algorithms, making it adaptable to a wide range of applications.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    Statistical Testing
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        TCI includes comprehensive statistical testing tools for A/B testing, effect size calculation, and significance analysis to validate experimental results.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    Docker Support
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        TCI provides Docker configurations for easy deployment and testing across different environments and Python versions.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="framework-architecture">
                    <h2>Framework Architecture</h2>
                    <p>
                        The TCI framework consists of several core components that work together to enable causal reinforcement learning:
                    </p>
                    <div class="row">
                        <div class="col-lg-10 offset-lg-1">
                            <img src="images/tci_architecture_v2.png" alt="TCI Framework Architecture" class="img-fluid mb-4">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Component</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Causal Discovery Module</strong></td>
                                    <td>Infers causal relationships from observed data using algorithms like PC, GES, NOTEARS, or correlation-based methods. Supports multiple discovery methods with configurable parameters.</td>
                                </tr>
                                <tr>
                                    <td><strong>Bayesian Neural Network (BNN)</strong></td>
                                    <td>Models uncertainty in state-action value estimates, enabling exploration and robust decision-making. Includes dropout layers for approximate Bayesian inference.</td>
                                </tr>
                                <tr>
                                    <td><strong>Variational Autoencoder (VAE)</strong></td>
                                    <td>Learns compact representations of states, reducing dimensionality and improving generalization. Now a core component of the TCI baseline.</td>
                                </tr>
                                <tr>
                                    <td><strong>Experience Replay</strong></td>
                                    <td>Stores and reuses past experiences to improve sample efficiency. Prioritized experience replay is available as an extension.</td>
                                </tr>
                                <tr>
                                    <td><strong>DynaQ Model</strong></td>
                                    <td>Predicts how states evolve over time, enabling planning and counterfactual reasoning. Now enabled by default in the TCI baseline.</td>
                                </tr>
                                <tr>
                                    <td><strong>Extension Manager</strong></td>
                                    <td>Coordinates the integration of various extensions to enhance the core TCI functionality. Supports dynamic loading and configuration of extensions.</td>
                                </tr>
                                <tr>
                                    <td><strong>Statistical Testing</strong></td>
                                    <td>Provides tools for rigorous statistical analysis of experimental results, including A/B testing and effect size calculation.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p>
                        These components interact through a well-defined API, allowing for modular development and easy extension of the framework.
                    </p>
                </div>
