#!/usr/bin/env python3
"""
Enhanced TCI-fix Integration

Integration module for the enhanced TCI-fix with two-stage modeling.
This provides the same interface as the original integration but with improved accuracy.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Import the enhanced predictor
from tci_fix_enhanced import EnhancedTCIFixPredictor
from tci_fix_integration import load_external_data
from folder_data_manager import FolderDataManager

class EnhancedTCIFixIntegration:
    """
    Enhanced TCI-fix integration with improved prediction accuracy.
    
    Features:
    - Two-stage modeling (order/no-order + size)
    - Bias correction
    - Ensemble approach
    - Volatility-aware modeling
    - Uncertainty quantification
    """
    
    def __init__(self):
        """Initialize the enhanced TCI-fix integration."""
        self.logger = logging.getLogger(__name__)
        self.logger.info("Enhanced TCI-fix Integration initialized")
        
        # Data manager
        self.data_manager = FolderDataManager("dataset")
        
        # Model cache
        self.model_cache = {}
        
        # Performance tracking
        self.performance_log = []
    
    def train_model(self, customer, product, force_retrain=False):
        """
        Train an enhanced TCI-fix model for a specific customer-product combination.
        
        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name
        force_retrain : bool
            Whether to force retraining even if model exists
            
        Returns:
        --------
        tuple
            (success, message)
        """
        try:
            self.logger.info(f"Training enhanced model for {customer} - {product}")
            
            # Check if model already exists
            model_path = self._get_model_path(customer, product)
            if os.path.exists(model_path) and not force_retrain:
                return True, f"Enhanced model already exists: {model_path}"
            
            # Load data
            success, message = self.data_manager.load_data_from_folder()
            if not success:
                return False, f"Failed to load data: {message}"
            
            # Get specific customer-product data
            key = f"{customer}_{product}"
            if key not in self.data_manager.data:
                return False, f"No data found for {customer} - {product}"
            
            data = self.data_manager.data[key].copy()
            
            if len(data) < 12:  # Need at least 12 months
                return False, f"Insufficient data: {len(data)} rows (need at least 12)"
            
            self.logger.info(f"Successfully loaded data for {customer} - {product}: {len(data)} rows")
            
            # Load external data
            external_data = self._load_external_data()
            self.logger.info(f"Loaded {len(external_data)} external data sources for enhanced TCI-fix model")
            
            # Create and train enhanced model
            model = EnhancedTCIFixPredictor(
                order_threshold=100,  # Minimum quantity to consider as order
                bias_correction=True,
                uncertainty_quantification=True,
                ensemble_weights=[0.5, 0.3, 0.2]  # TCI-fix, Prophet-like, Simple MA
            )
            
            # Train the model
            model.fit(data, external_data)
            
            # Save the model
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            model.save(model_path)
            
            # Log performance
            self._log_training_performance(customer, product, data, model)
            
            return True, f"Enhanced model trained successfully and saved to {model_path}"
            
        except Exception as e:
            error_msg = f"Error training enhanced model: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def predict_future(self, customer, product, periods=12, start_date=None):
        """
        Generate enhanced predictions for a customer-product combination.
        
        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name
        periods : int
            Number of periods to predict
        start_date : str or datetime
            Start date for predictions (default: next month)
            
        Returns:
        --------
        pandas.DataFrame or None
            Enhanced predictions with uncertainty bounds
        """
        try:
            self.logger.info(f"Generating enhanced predictions for {customer} - {product}")
            
            # Load model
            model_path = self._get_model_path(customer, product)
            if not os.path.exists(model_path):
                # Try to train the model
                success, message = self.train_model(customer, product)
                if not success:
                    self.logger.error(f"Cannot generate predictions: {message}")
                    return None
            
            model = EnhancedTCIFixPredictor.load(model_path)
            self.logger.info(f"Loaded enhanced model from {model_path}")
            
            # Load data
            success, message = self.data_manager.load_data_from_folder()
            if not success:
                self.logger.error(f"Failed to load data: {message}")
                return None
            
            key = f"{customer}_{product}"
            if key not in self.data_manager.data:
                self.logger.error(f"No data found for {customer} - {product}")
                return None
            
            data = self.data_manager.data[key].copy()
            self.logger.info(f"Successfully loaded data for {customer} - {product}: {len(data)} rows")
            
            # Load external data
            external_data = self._load_external_data()
            self.logger.info(f"Loaded {len(external_data)} external data sources for enhanced TCI-fix model")
            
            # Determine prediction dates
            if start_date is None:
                last_date = pd.to_datetime(data['Date']).max()
                start_date = last_date + pd.DateOffset(months=1)
                start_date = start_date.replace(day=25)
            else:
                start_date = pd.to_datetime(start_date)
            
            end_date = start_date + pd.DateOffset(months=periods-1)
            
            self.logger.info(f"Generating enhanced predictions from {start_date} to {end_date}...")
            
            # Generate enhanced predictions
            predictions = model.predict(start_date, end_date, data, external_data)
            
            if predictions is not None:
                self.logger.info(f"✅ Enhanced predictions generated: {len(predictions)} periods")
                
                # Add metadata
                predictions['customer'] = customer
                predictions['product'] = product
                predictions['model_type'] = 'enhanced_tci_fix'
                predictions['prediction_timestamp'] = datetime.now()
                
                # Log prediction performance
                self._log_prediction_performance(customer, product, predictions)
                
                return predictions
            else:
                self.logger.error("❌ Failed to generate enhanced predictions")
                return None
                
        except Exception as e:
            self.logger.error(f"Error generating enhanced TCI-fix forecast: {str(e)}")
            return None
    
    def compare_with_original(self, customer, product, periods=12):
        """
        Compare enhanced predictions with original TCI-fix predictions.
        
        Returns:
        --------
        dict
            Comparison results
        """
        try:
            # Get enhanced predictions
            enhanced_pred = self.predict_future(customer, product, periods)
            
            # Get original predictions (using original integration)
            from tci_fix_integration import TCIFixIntegration
            original_integration = TCIFixIntegration()
            original_pred = original_integration.predict_future(customer, product, periods)
            
            if enhanced_pred is None or original_pred is None:
                return None
            
            # Compare predictions
            comparison = {
                'customer': customer,
                'product': product,
                'periods': periods,
                'enhanced_mean': enhanced_pred['predicted_quantity'].mean(),
                'original_mean': original_pred['predicted_quantity'].mean(),
                'enhanced_std': enhanced_pred['predicted_quantity'].std(),
                'original_std': original_pred['predicted_quantity'].std(),
                'improvement_factor': None,
                'has_uncertainty_bounds': 'lower_bound' in enhanced_pred.columns,
                'has_confidence_levels': 'confidence_level' in enhanced_pred.columns
            }
            
            # Calculate improvement factor (if we have actual data to compare)
            if 'lower_bound' in enhanced_pred.columns:
                avg_uncertainty = (enhanced_pred['upper_bound'] - enhanced_pred['lower_bound']).mean()
                comparison['average_uncertainty_range'] = avg_uncertainty
            
            return comparison
            
        except Exception as e:
            self.logger.error(f"Error comparing predictions: {e}")
            return None
    
    def _get_model_path(self, customer, product):
        """Get the file path for a model."""
        safe_customer = customer.replace("/", "_").replace("\\", "_")
        safe_product = product.replace("/", "_").replace("\\", "_")
        return f"models/enhanced_tci_fix_{safe_customer}_{safe_product}.pkl"
    
    def _load_external_data(self):
        """Load external data for the enhanced model."""
        try:
            # Use the same external data loading as the original
            external_data = load_external_data(start_year=2016, end_year=2025)
            
            if external_data:
                self.logger.info(f"Loaded {len(external_data)} external data sources for enhanced TCI-fix model")
                return external_data
            else:
                self.logger.warning("No external data loaded, using empty dictionary")
                return {}
                
        except Exception as e:
            self.logger.error(f"Error loading external data: {str(e)}")
            return {}
    
    def _log_training_performance(self, customer, product, data, model):
        """Log training performance metrics."""
        try:
            target_col = model.base_predictor.target_column
            
            # Calculate basic statistics
            stats = {
                'timestamp': datetime.now(),
                'customer': customer,
                'product': product,
                'training_samples': len(data),
                'data_mean': data[target_col].mean(),
                'data_std': data[target_col].std(),
                'data_cv': data[target_col].std() / (data[target_col].mean() + 1e-6),
                'volatility_regime': model.volatility_regime,
                'bias_correction_factor': model.bias_correction_factor,
                'order_threshold': model.order_threshold
            }
            
            self.performance_log.append(stats)
            
            self.logger.info(f"Training performance logged for {customer} - {product}")
            
        except Exception as e:
            self.logger.warning(f"Could not log training performance: {e}")
    
    def _log_prediction_performance(self, customer, product, predictions):
        """Log prediction performance metrics."""
        try:
            stats = {
                'timestamp': datetime.now(),
                'customer': customer,
                'product': product,
                'prediction_periods': len(predictions),
                'prediction_mean': predictions['predicted_quantity'].mean(),
                'prediction_std': predictions['predicted_quantity'].std(),
                'has_uncertainty': 'lower_bound' in predictions.columns,
                'model_type': 'enhanced_tci_fix'
            }
            
            if 'confidence_level' in predictions.columns:
                stats['avg_confidence'] = predictions['confidence_level'].mean()
            
            self.performance_log.append(stats)
            
            self.logger.info(f"Prediction performance logged for {customer} - {product}")
            
        except Exception as e:
            self.logger.warning(f"Could not log prediction performance: {e}")
    
    def get_performance_summary(self):
        """Get a summary of model performance."""
        if not self.performance_log:
            return "No performance data available"
        
        df = pd.DataFrame(self.performance_log)
        
        summary = {
            'total_models_trained': len(df[df['training_samples'].notna()]),
            'total_predictions_made': len(df[df['prediction_periods'].notna()]),
            'avg_volatility_regime': df['volatility_regime'].mode().iloc[0] if 'volatility_regime' in df.columns else 'unknown',
            'avg_bias_correction': df['bias_correction_factor'].mean() if 'bias_correction_factor' in df.columns else 1.0
        }
        
        return summary
