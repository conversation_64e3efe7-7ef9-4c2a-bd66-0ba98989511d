#!/usr/bin/env python
"""
Generate a release announcement for the TCI framework.

This script generates a release announcement for the TCI framework based on the CHANGELOG.md file.
It can output the announcement in different formats (markdown, html, text).

Usage:
    python scripts/generate_announcement.py [--format FORMAT] [--output OUTPUT_FILE]

Options:
    --format FORMAT          Output format (markdown, html, text) (default: markdown)
    --output OUTPUT_FILE     Output file (default: announcement.md)
"""

import os
import sys
import re
import argparse
from typing import Dict, List, Any, Optional, Tuple

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def parse_changelog(changelog_path: str) -> Tuple[str, Dict[str, List[str]]]:
    """
    Parse the CHANGELOG.md file to extract the latest release information.
    
    Parameters
    ----------
    changelog_path : str
        Path to the CHANGELOG.md file
    
    Returns
    -------
    Tuple[str, Dict[str, List[str]]]
        A tuple containing the version and a dictionary of sections and their items
    """
    with open(changelog_path, "r") as f:
        content = f.read()
    
    # Find the latest version section
    version_match = re.search(r"## \[([\d\.]+)\] - ([\d-]+)", content)
    if not version_match:
        raise ValueError("Could not find version information in CHANGELOG.md")
    
    version = version_match.group(1)
    date = version_match.group(2)
    
    # Extract the content of the latest version section
    version_section_match = re.search(
        r"## \[" + re.escape(version) + r"\] - " + re.escape(date) + r"(.*?)(?=\n## \[|$)",
        content,
        re.DOTALL
    )
    if not version_section_match:
        raise ValueError("Could not extract version section from CHANGELOG.md")
    
    version_section = version_section_match.group(1).strip()
    
    # Parse sections (Added, Changed, Fixed, etc.)
    sections = {}
    current_section = None
    
    for line in version_section.split("\n"):
        section_match = re.match(r"### (\w+)", line)
        if section_match:
            current_section = section_match.group(1)
            sections[current_section] = []
        elif current_section and line.strip().startswith("- "):
            sections[current_section].append(line.strip()[2:])
    
    return version, sections


def generate_markdown_announcement(version: str, sections: Dict[str, List[str]]) -> str:
    """
    Generate a markdown announcement.
    
    Parameters
    ----------
    version : str
        Version number
    sections : Dict[str, List[str]]
        Dictionary of sections and their items
    
    Returns
    -------
    str
        Markdown announcement
    """
    announcement = f"# TCI Framework {version} Released!\n\n"
    announcement += "We're excited to announce the release of version " + version + " of the Temporal Causality Infusion (TCI) framework!\n\n"
    
    announcement += "## What's New\n\n"
    
    for section, items in sections.items():
        if items:
            announcement += f"### {section}\n\n"
            for item in items:
                announcement += f"- {item}\n"
            announcement += "\n"
    
    announcement += "## Installation\n\n"
    announcement += "You can install the latest version using pip:\n\n"
    announcement += "```bash\npip install tci\n```\n\n"
    
    announcement += "Or with specific extras:\n\n"
    announcement += "```bash\n# With GPU support\npip install tci[gpu]\n\n# With visualization tools\npip install tci[visualization]\n\n# With all dependencies\npip install tci[all]\n```\n\n"
    
    announcement += "## Documentation\n\n"
    announcement += "For more information, check out the [documentation](https://tci.readthedocs.io/) and [GitHub repository](https://github.com/yourusername/tci).\n\n"
    
    announcement += "## Feedback\n\n"
    announcement += "We welcome your feedback and contributions! Please open issues or pull requests on the [GitHub repository](https://github.com/yourusername/tci).\n\n"
    
    announcement += "Happy causal reinforcement learning!\n"
    
    return announcement


def generate_html_announcement(version: str, sections: Dict[str, List[str]]) -> str:
    """
    Generate an HTML announcement.
    
    Parameters
    ----------
    version : str
        Version number
    sections : Dict[str, List[str]]
        Dictionary of sections and their items
    
    Returns
    -------
    str
        HTML announcement
    """
    announcement = f"<h1>TCI Framework {version} Released!</h1>\n\n"
    announcement += "<p>We're excited to announce the release of version " + version + " of the Temporal Causality Infusion (TCI) framework!</p>\n\n"
    
    announcement += "<h2>What's New</h2>\n\n"
    
    for section, items in sections.items():
        if items:
            announcement += f"<h3>{section}</h3>\n\n"
            announcement += "<ul>\n"
            for item in items:
                announcement += f"<li>{item}</li>\n"
            announcement += "</ul>\n\n"
    
    announcement += "<h2>Installation</h2>\n\n"
    announcement += "<p>You can install the latest version using pip:</p>\n\n"
    announcement += "<pre><code>pip install tci</code></pre>\n\n"
    
    announcement += "<p>Or with specific extras:</p>\n\n"
    announcement += "<pre><code># With GPU support\npip install tci[gpu]\n\n# With visualization tools\npip install tci[visualization]\n\n# With all dependencies\npip install tci[all]</code></pre>\n\n"
    
    announcement += "<h2>Documentation</h2>\n\n"
    announcement += "<p>For more information, check out the <a href=\"https://tci.readthedocs.io/\">documentation</a> and <a href=\"https://github.com/yourusername/tci\">GitHub repository</a>.</p>\n\n"
    
    announcement += "<h2>Feedback</h2>\n\n"
    announcement += "<p>We welcome your feedback and contributions! Please open issues or pull requests on the <a href=\"https://github.com/yourusername/tci\">GitHub repository</a>.</p>\n\n"
    
    announcement += "<p>Happy causal reinforcement learning!</p>\n"
    
    return announcement


def generate_text_announcement(version: str, sections: Dict[str, List[str]]) -> str:
    """
    Generate a plain text announcement.
    
    Parameters
    ----------
    version : str
        Version number
    sections : Dict[str, List[str]]
        Dictionary of sections and their items
    
    Returns
    -------
    str
        Plain text announcement
    """
    announcement = f"TCI Framework {version} Released!\n"
    announcement += "=" * len(f"TCI Framework {version} Released!") + "\n\n"
    announcement += "We're excited to announce the release of version " + version + " of the Temporal Causality Infusion (TCI) framework!\n\n"
    
    announcement += "What's New\n"
    announcement += "---------\n\n"
    
    for section, items in sections.items():
        if items:
            announcement += f"{section}\n"
            announcement += "-" * len(section) + "\n\n"
            for item in items:
                announcement += f"* {item}\n"
            announcement += "\n"
    
    announcement += "Installation\n"
    announcement += "-----------\n\n"
    announcement += "You can install the latest version using pip:\n\n"
    announcement += "    pip install tci\n\n"
    
    announcement += "Or with specific extras:\n\n"
    announcement += "    # With GPU support\n"
    announcement += "    pip install tci[gpu]\n\n"
    announcement += "    # With visualization tools\n"
    announcement += "    pip install tci[visualization]\n\n"
    announcement += "    # With all dependencies\n"
    announcement += "    pip install tci[all]\n\n"
    
    announcement += "Documentation\n"
    announcement += "-------------\n\n"
    announcement += "For more information, check out the documentation (https://tci.readthedocs.io/)\n"
    announcement += "and GitHub repository (https://github.com/yourusername/tci).\n\n"
    
    announcement += "Feedback\n"
    announcement += "--------\n\n"
    announcement += "We welcome your feedback and contributions! Please open issues or pull requests\n"
    announcement += "on the GitHub repository (https://github.com/yourusername/tci).\n\n"
    
    announcement += "Happy causal reinforcement learning!\n"
    
    return announcement


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Generate a release announcement for the TCI framework")
    parser.add_argument("--format", type=str, choices=["markdown", "html", "text"], default="markdown",
                        help="Output format (markdown, html, text)")
    parser.add_argument("--output", type=str, default="announcement.md",
                        help="Output file")
    args = parser.parse_args()
    
    # Get TCI version and changelog
    try:
        import tci
        version = tci.__version__
        print(f"Generating announcement for TCI version {version}")
    except ImportError:
        print("Error: Could not import tci. Make sure it's installed.")
        sys.exit(1)
    
    # Parse changelog
    changelog_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "CHANGELOG.md")
    try:
        version, sections = parse_changelog(changelog_path)
    except Exception as e:
        print(f"Error parsing CHANGELOG.md: {e}")
        sys.exit(1)
    
    # Generate announcement
    if args.format == "markdown":
        announcement = generate_markdown_announcement(version, sections)
    elif args.format == "html":
        announcement = generate_html_announcement(version, sections)
    else:  # text
        announcement = generate_text_announcement(version, sections)
    
    # Write announcement to file
    output_path = args.output
    with open(output_path, "w") as f:
        f.write(announcement)
    
    print(f"Announcement generated and saved to {output_path}")


if __name__ == "__main__":
    main()
