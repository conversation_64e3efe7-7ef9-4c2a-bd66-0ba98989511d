#!/usr/bin/env python3
"""
Diagnose API Integration Issues

This script investigates why the new API data isn't affecting production predictions
by checking various components of the system.
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_api_data_availability():
    """Check if API data is being generated and stored."""
    print("\n🌐 Checking API Data Availability")
    print("=" * 50)
    
    # Check external data directory
    external_data_dir = "external_data"
    if os.path.exists(external_data_dir):
        print(f"✅ External data directory exists: {external_data_dir}")
        
        # List all files in external data
        files = os.listdir(external_data_dir)
        if files:
            print(f"📁 Found {len(files)} files in external_data/:")
            for file in sorted(files):
                file_path = os.path.join(external_data_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    modified = datetime.fromtimestamp(os.path.getmtime(file_path))
                    print(f"   - {file} ({size:,} bytes, modified: {modified.strftime('%Y-%m-%d %H:%M')})")
                    
                    # Check if file has recent data
                    if file.endswith('.csv'):
                        try:
                            df = pd.read_csv(file_path)
                            print(f"     📊 {len(df)} rows, columns: {list(df.columns)}")
                            if 'date' in df.columns:
                                dates = pd.to_datetime(df['date'], errors='coerce').dropna()
                                if len(dates) > 0:
                                    print(f"     📅 Date range: {dates.min()} to {dates.max()}")
                        except Exception as e:
                            print(f"     ❌ Error reading CSV: {e}")
        else:
            print("❌ No files found in external_data/ directory")
    else:
        print(f"❌ External data directory not found: {external_data_dir}")
    
    return os.path.exists(external_data_dir) and len(os.listdir(external_data_dir)) > 0

def check_api_fetchers():
    """Check if API fetcher modules are working."""
    print("\n🔧 Checking API Fetcher Modules")
    print("=" * 50)
    
    api_modules = [
        'api_data_fetcher.py',
        'industry_data_fetcher.py',
        'utils/industrial_data_fetcher.py'
    ]
    
    working_modules = []
    
    for module in api_modules:
        if os.path.exists(module):
            print(f"✅ Found: {module}")
            try:
                # Try to import and test the module
                if module == 'api_data_fetcher.py':
                    from api_data_fetcher import APIDataFetcher
                    fetcher = APIDataFetcher()
                    print(f"   📡 APIDataFetcher initialized successfully")
                    working_modules.append(module)
                    
                elif module == 'industry_data_fetcher.py':
                    from industry_data_fetcher import IndustryDataFetcher
                    fetcher = IndustryDataFetcher()
                    print(f"   📡 IndustryDataFetcher initialized successfully")
                    working_modules.append(module)
                    
            except Exception as e:
                print(f"   ❌ Error importing {module}: {e}")
        else:
            print(f"❌ Not found: {module}")
    
    return working_modules

def check_environment_variables():
    """Check if API keys and environment variables are set."""
    print("\n🔑 Checking Environment Variables")
    print("=" * 50)
    
    api_keys = [
        'FRED_API_KEY',
        'TRADING_ECONOMICS_KEY', 
        'IHS_MARKIT_KEY',
        'ALPHA_VANTAGE_API_KEY'
    ]
    
    found_keys = []
    
    for key in api_keys:
        value = os.getenv(key)
        if value:
            print(f"✅ {key}: {'*' * (len(value) - 4) + value[-4:] if len(value) > 4 else '****'}")
            found_keys.append(key)
        else:
            print(f"❌ {key}: Not set")
    
    if not found_keys:
        print("⚠️ No API keys found - system will use synthetic data")
    
    return found_keys

def check_tci_fix_integration():
    """Check if TCI-fix model is properly integrated with external data."""
    print("\n🤖 Checking TCI-fix Model Integration")
    print("=" * 50)
    
    try:
        from tci_fix import TCIFixPredictor
        print("✅ TCIFixPredictor imported successfully")
        
        # Check if external data loading function exists
        try:
            from tci_fix_integration import load_external_data
            print("✅ load_external_data function found")
            
            # Test loading external data
            external_data = load_external_data(start_year=2023, end_year=2024)
            if external_data:
                print(f"✅ External data loaded: {len(external_data)} sources")
                for source, data in external_data.items():
                    print(f"   - {source}: {len(data)} records")
            else:
                print("❌ No external data loaded")
                
        except ImportError:
            print("❌ tci_fix_integration module not found")
        except Exception as e:
            print(f"❌ Error loading external data: {e}")
            
    except ImportError:
        print("❌ TCIFixPredictor not found")
    except Exception as e:
        print(f"❌ Error with TCI-fix: {e}")

def check_model_files():
    """Check if trained models include external data features."""
    print("\n💾 Checking Trained Model Files")
    print("=" * 50)
    
    model_dirs = ['models', 'saved_models', 'tci_models']
    
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            print(f"✅ Found model directory: {model_dir}")
            
            model_files = [f for f in os.listdir(model_dir) if f.endswith(('.pkl', '.joblib', '.model'))]
            
            for model_file in model_files[:5]:  # Check first 5 models
                model_path = os.path.join(model_dir, model_file)
                try:
                    # Try to load and inspect the model
                    with open(model_path, 'rb') as f:
                        model_data = pickle.load(f)
                    
                    print(f"   📁 {model_file}:")
                    
                    # Check if it's a TCI-fix model with feature information
                    if hasattr(model_data, 'feature_columns'):
                        features = model_data.feature_columns
                        external_features = [f for f in features if any(keyword in f.lower() 
                                           for keyword in ['industrial', 'automotive', 'manufacturing', 'jp_', 'id_'])]
                        
                        print(f"     📊 Total features: {len(features)}")
                        print(f"     🌐 External features: {len(external_features)}")
                        
                        if external_features:
                            print(f"     ✅ Contains external data features")
                            for feat in external_features[:3]:
                                print(f"       - {feat}")
                        else:
                            print(f"     ❌ No external data features found")
                    
                    elif isinstance(model_data, dict):
                        print(f"     📊 Model dict keys: {list(model_data.keys())}")
                    
                except Exception as e:
                    print(f"   ❌ Error loading {model_file}: {e}")
        else:
            print(f"❌ Model directory not found: {model_dir}")

def check_production_pipeline():
    """Check how predictions are generated in production."""
    print("\n🏭 Checking Production Pipeline")
    print("=" * 50)
    
    # Check main prediction scripts
    main_scripts = ['main.py', 'predict.py', 'forecast.py', 'run_predictions.py']
    
    for script in main_scripts:
        if os.path.exists(script):
            print(f"✅ Found: {script}")
            
            # Read the script and look for external data usage
            try:
                with open(script, 'r') as f:
                    content = f.read()
                
                # Look for external data keywords
                external_keywords = ['external_data', 'load_external_data', 'api_data', 'APIDataFetcher']
                found_keywords = [kw for kw in external_keywords if kw in content]
                
                if found_keywords:
                    print(f"   ✅ Contains external data references: {found_keywords}")
                else:
                    print(f"   ❌ No external data references found")
                
                # Look for TCI-fix usage
                if 'TCIFixPredictor' in content or 'tci_fix' in content:
                    print(f"   ✅ Uses TCI-fix model")
                else:
                    print(f"   ⚠️ May not use TCI-fix model")
                    
            except Exception as e:
                print(f"   ❌ Error reading {script}: {e}")

def check_recent_predictions():
    """Check recent prediction files to see if they include external data effects."""
    print("\n📈 Checking Recent Prediction Files")
    print("=" * 50)
    
    prediction_dirs = ['results/tci-fix', 'forecasts', 'predictions', 'exports']
    
    for pred_dir in prediction_dirs:
        if os.path.exists(pred_dir):
            print(f"✅ Found prediction directory: {pred_dir}")
            
            # Get recent files
            files = []
            for root, dirs, filenames in os.walk(pred_dir):
                for filename in filenames:
                    if filename.endswith('.csv'):
                        filepath = os.path.join(root, filename)
                        mtime = os.path.getmtime(filepath)
                        files.append((filepath, mtime))
            
            # Sort by modification time (newest first)
            files.sort(key=lambda x: x[1], reverse=True)
            
            print(f"   📁 Found {len(files)} prediction files")
            
            # Check the most recent files
            for filepath, mtime in files[:3]:
                modified = datetime.fromtimestamp(mtime)
                print(f"   📄 {os.path.basename(filepath)} (modified: {modified.strftime('%Y-%m-%d %H:%M')})")
                
                try:
                    df = pd.read_csv(filepath)
                    print(f"     📊 {len(df)} rows, columns: {list(df.columns)}")
                    
                    # Check if predictions look like they include external data effects
                    if 'prediction' in df.columns:
                        predictions = df['prediction'].dropna()
                        if len(predictions) > 1:
                            # Check for variation (external data should add variation)
                            variation = predictions.std() / predictions.mean() if predictions.mean() > 0 else 0
                            print(f"     📊 Prediction variation (CV): {variation:.3f}")
                            
                            if variation > 0.1:
                                print(f"     ✅ Good variation - likely includes external factors")
                            else:
                                print(f"     ⚠️ Low variation - may be using simple patterns only")
                
                except Exception as e:
                    print(f"     ❌ Error reading prediction file: {e}")

def run_integration_test():
    """Run a quick test to see if external data can be loaded and used."""
    print("\n🧪 Running Integration Test")
    print("=" * 50)
    
    try:
        # Test 1: Load external data
        print("🔬 Test 1: Loading external data...")
        from tci_fix_integration import load_external_data
        external_data = load_external_data(start_year=2023, end_year=2024)
        
        if external_data:
            print(f"✅ External data loaded successfully: {len(external_data)} sources")
        else:
            print("❌ Failed to load external data")
            return False
        
        # Test 2: Initialize TCI-fix with external data
        print("🔬 Test 2: Initializing TCI-fix with external data...")
        from tci_fix import TCIFixPredictor
        model = TCIFixPredictor()
        
        # Test 3: Check if model can use external data
        print("🔬 Test 3: Testing model with external data...")
        from folder_data_manager import FolderDataManager
        
        data_manager = FolderDataManager("dataset")
        success, message = data_manager.load_data_from_folder()
        
        if success:
            customers = data_manager.get_customers()
            if customers:
                customer = customers[0]
                products = data_manager.get_products(customer)
                if products:
                    product = products[0]
                    key = f"{customer}_{product}"
                    if key in data_manager.data:
                        sample_data = data_manager.data[key].copy()
                        
                        # Try to fit model with external data
                        model.fit(sample_data, external_data)
                        print("✅ Model fitted with external data successfully")
                        
                        # Try to predict with external data
                        predictions = model.predict(
                            start_date='2024-01-01',
                            end_date='2024-03-01',
                            data=sample_data,
                            external_data=external_data
                        )
                        
                        if len(predictions) > 0:
                            print(f"✅ Predictions generated with external data: {len(predictions)} periods")
                            return True
                        else:
                            print("❌ No predictions generated")
                            return False
        
        print("❌ Could not complete integration test")
        return False
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all diagnostic checks."""
    print("🔍 API Integration Diagnostic Tool")
    print("=" * 60)
    print("Investigating why new API data isn't affecting production predictions...\n")
    
    results = {}
    
    # Run all checks
    results['api_data'] = check_api_data_availability()
    results['api_fetchers'] = len(check_api_fetchers()) > 0
    results['env_vars'] = len(check_environment_variables()) > 0
    
    check_tci_fix_integration()
    check_model_files()
    check_production_pipeline()
    check_recent_predictions()
    
    results['integration_test'] = run_integration_test()
    
    # Summary
    print("\n📋 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    issues_found = []
    
    if not results['api_data']:
        issues_found.append("❌ No external data files found")
    else:
        print("✅ External data files are available")
    
    if not results['api_fetchers']:
        issues_found.append("❌ API fetcher modules not working")
    else:
        print("✅ API fetcher modules are functional")
    
    if not results['env_vars']:
        print("⚠️ No API keys set (using synthetic data)")
    else:
        print("✅ API keys are configured")
    
    if not results['integration_test']:
        issues_found.append("❌ Integration test failed")
    else:
        print("✅ Integration test passed")
    
    print(f"\n🎯 CONCLUSION:")
    if issues_found:
        print("❌ Issues found that prevent API data from affecting predictions:")
        for issue in issues_found:
            print(f"   {issue}")
        
        print(f"\n💡 RECOMMENDED ACTIONS:")
        print("1. Fix the issues listed above")
        print("2. Ensure production scripts use external_data=True")
        print("3. Verify models are retrained with external data")
        print("4. Test predictions with external data integration")
    else:
        print("✅ All systems appear functional")
        print("💡 The issue may be in production configuration or model selection")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Diagnostic interrupted by user")
    except Exception as e:
        print(f"\n❌ Diagnostic failed with error: {e}")
        import traceback
        traceback.print_exc()
