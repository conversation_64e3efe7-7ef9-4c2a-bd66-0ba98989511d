"""
Run all benchmarks for TCI.

This script runs all the benchmarks defined in the benchmark configuration.
"""

import argparse
import os
import sys
import time
import json
import numpy as np
import torch
from typing import Dict, List, Any, Optional, Tuple
from scipy.stats import ttest_ind, f_oneway, mannwhitneyu, kruskal
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
# Add current directory to path to import ab_testing
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from ab_testing import run_ab_test_benchmark, run_multiple_ab_tests, plot_ab_test_results, create_ab_test_report

# Add parent directory to path to import tci
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import tci
from tci.environments import ChemEnv, TEPEnv

# Import custom TEPAgent for TEP environment
from tep_agent import TEPAgent

from tci.extensions import (
    DynaQ, GAT, PER, CEVAE, ThompsonSampling, QMIX, HER,
    IncrementalCausalDiscovery, CausalDimReduction,
    UncertaintyExploration, LatentCausalRepresentation,
    SparseCausal, MAML
)

# Import custom causal discovery module instead of the one from causallearn
from tci_causal_discovery import infer_causal_graph

from examples.benchmarks.configs.benchmark_config import (
    get_benchmark_config, get_environment_config,
    get_extension_config, get_baseline_config,
    BENCHMARKS, OUTPUT_DIR
)
from examples.benchmarks.benchmark_runner import run_benchmark
from examples.benchmarks.comprehensive_benchmark import (
    create_tci_variants, create_baseline_algorithms,
    plot_comprehensive_results
)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run all TCI benchmarks")
    parser.add_argument("--benchmarks", type=str, default="all",
                        help="Comma-separated list of benchmarks to run or 'all'")
    parser.add_argument("--gpu", action="store_true",
                        help="Use GPU if available")
    parser.add_argument("--output-dir", type=str, default=OUTPUT_DIR,
                        help="Directory to save results")
    parser.add_argument("--save-causal-graphs", action="store_true",
                        help="Save causal graphs discovered during benchmarks")
    parser.add_argument("--causal-graph-format", type=str, default="png",
                        choices=["png", "pdf", "svg"],
                        help="Format for saving causal graphs")
    parser.add_argument("--generate-report", action="store_true",
                        help="Generate comprehensive statistical report")
    parser.add_argument("--report-file", type=str, default="statistical_report.html",
                        help="Filename for the statistical report")

    # A/B testing arguments
    parser.add_argument("--ab-test", action="store_true",
                        help="Run A/B tests comparing algorithm variants")
    parser.add_argument("--control", type=str, default="TCI-Base",
                        help="Control algorithm for A/B testing")
    parser.add_argument("--treatment", type=str, default="TCI-Full",
                        help="Treatment algorithm for A/B testing")
    parser.add_argument("--ab-iterations", type=int, default=10,
                        help="Number of iterations for A/B testing")
    parser.add_argument("--ab-alpha", type=float, default=0.05,
                        help="Significance level for A/B testing")
    parser.add_argument("--ab-parametric", action="store_true",
                        help="Use parametric tests for A/B testing")
    parser.add_argument("--ab-correction", type=str, default="fdr_bh",
                        choices=["fdr_bh", "bonferroni", "holm", "holm-sidak", "simes-hochberg", "hommel"],
                        help="Multiple comparison correction method for A/B testing")

    return parser.parse_args()


def create_environment(env_config: Dict[str, Any], seed: int) -> Any:
    """
    Create an environment based on configuration.

    Parameters
    ----------
    env_config : Dict[str, Any]
        Environment configuration
    seed : int
        Random seed

    Returns
    -------
    Any
        Environment instance
    """
    env_class = env_config["class"]
    params = env_config["params"]

    if env_class == "ChemEnv":
        return ChemEnv(seed=seed, **params)
    elif env_class == "TEPEnv":
        # Ensure causal rewards are enabled for TEP environment
        if "use_causal_reward" not in params:
            params["use_causal_reward"] = True
        if "reward_scaler" not in params:
            params["reward_scaler"] = 0.01

        # Create TEP environment with causal rewards
        env = TEPEnv(seed=seed, **params)
        print(f"Created TEP environment with causal rewards: {params['use_causal_reward']}")
        print(f"Reward scaler: {params['reward_scaler']}")
        return env
    else:
        raise ValueError(f"Unknown environment class: {env_class}")


def create_extension(ext_config: Dict[str, Any], state_dim: int = None, action_dim: int = None, device: str = None) -> Any:
    """
    Create an extension based on configuration.

    Parameters
    ----------
    ext_config : Dict[str, Any]
        Extension configuration
    state_dim : int, optional
        State dimension, by default None
    action_dim : int, optional
        Action dimension, by default None
    device : str, optional
        Device to use, by default None

    Returns
    -------
    Any
        Extension instance
    """
    ext_class = ext_config["class"]
    params = ext_config["params"]

    # We don't need to pass state_dim, action_dim, and device to extensions
    # as they are handled by the TCIAgent

    if ext_class == "DynaQ":
        return DynaQ(**params)
    elif ext_class == "GAT":
        return GAT(**params)
    elif ext_class == "PER":
        return PER(**params)
    elif ext_class == "CEVAE":
        return CEVAE(**params)
    elif ext_class == "ThompsonSampling":
        return ThompsonSampling(**params)
    elif ext_class == "HER":
        return HER(**params)
    elif ext_class == "QMIX":
        return QMIX(**params)
    # New extensions
    elif ext_class == "IncrementalCausalDiscovery":
        return IncrementalCausalDiscovery(**params)
    elif ext_class == "CausalDimReduction":
        return CausalDimReduction(**params)
    elif ext_class == "UncertaintyExploration":
        return UncertaintyExploration(**params)
    elif ext_class == "LatentCausalRepresentation":
        return LatentCausalRepresentation(**params)
    # Additional extensions
    elif ext_class == "SparseCausal":
        from tci.extensions import SparseCausal
        return SparseCausal(**params)
    elif ext_class == "MAML":
        from tci.extensions import MAML
        return MAML(**params)
    else:
        raise ValueError(f"Unknown extension class: {ext_class}")


def get_extension_config(extension_name: str) -> Dict[str, Any]:
    """
    Get extension configuration.

    Parameters
    ----------
    extension_name : str
        Extension name

    Returns
    -------
    Dict[str, Any]
        Extension configuration
    """
    # Define extension configurations
    EXTENSIONS = {
        "dynaq": {
            "class": "DynaQ",
            "params": {
                "planning_steps": 10
            }
        },
        "dyna_q": {
            "class": "DynaQ",
            "params": {
                "planning_steps": 10
            }
        },
        "gat": {
            "class": "GAT",
            "params": {}
        },
        "per": {
            "class": "PER",
            "params": {}
        },
        "cevae": {
            "class": "CEVAE",
            "params": {}
        },
        "thompson": {
            "class": "ThompsonSampling",
            "params": {}
        },
        "her": {
            "class": "HER",
            "params": {}
        },
        "qmix": {
            "class": "QMIX",
            "params": {}
        },
        "maml": {
            "class": "MAML",
            "params": {
                "inner_lr": 0.01,
                "meta_lr": 0.001,
                "num_inner_steps": 5
            }
        },
        "sparse_causal": {
            "class": "SparseCausal",
            "params": {
                "sparsity_factor": 0.1,
                "l1_reg": 0.01,
                "threshold": 0.1
            }
        }
    }

    # Normalize extension name
    extension_name = extension_name.lower()

    if extension_name not in EXTENSIONS:
        raise ValueError(f"Unknown extension: {extension_name}")
    return EXTENSIONS[extension_name]


def create_baseline(baseline_config: Dict[str, Any], state_dim: int, action_dim: int, device: str) -> Any:
    """
    Create a baseline algorithm based on configuration.

    Parameters
    ----------
    baseline_config : Dict[str, Any]
        Baseline configuration
    state_dim : int
        State dimension
    action_dim : int
        Action dimension
    device : str
        Device to use

    Returns
    -------
    Any
        Baseline algorithm instance
    """
    from examples.benchmarks.benchmark_runner import DQNBaseline, RandomBaseline

    baseline_class = baseline_config["class"]
    params = baseline_config["params"]

    if baseline_class == "DQNBaseline":
        return DQNBaseline(state_dim=state_dim, action_dim=action_dim, device=device, **params)
    elif baseline_class == "RandomBaseline":
        return RandomBaseline(state_dim=state_dim, action_dim=action_dim, **params)
    else:
        raise ValueError(f"Unknown baseline class: {baseline_class}")








def run_single_benchmark(
    benchmark_name: str,
    device: str,
    output_dir: str,
    save_causal_graphs: bool = False,
    causal_graph_format: str = "png",
    algorithm_name: str = None,
    causal_method: str = "pc",
    causal_alpha: float = 0.1,
    sparsity_factor: float = 0.1
) -> None:
    """
    Run a single benchmark.

    Parameters
    ----------
    benchmark_name : str
        Name of the benchmark
    device : str
        Device to use
    output_dir : str
        Directory to save results
    save_causal_graphs : bool, optional
        Whether to save causal graphs, by default False
    causal_graph_format : str, optional
        Format for saving causal graphs, by default "png"
    algorithm_name : str, optional
        Name of the algorithm to run, by default None (runs all algorithms)
    causal_method : str, optional
        Causal discovery method to use, by default "pc"
    causal_alpha : float, optional
        Significance level for independence tests, by default 0.1
    sparsity_factor : float, optional
        Sparsity factor for sparse causal discovery, by default 0.1
    """
    # Import required modules
    import numpy as np
    import torch
    print(f"Running benchmark: {benchmark_name}")

    # Get benchmark configuration
    benchmark_config = get_benchmark_config(benchmark_name)

    # Create output directory
    benchmark_output_dir = os.path.join(output_dir, benchmark_name)
    os.makedirs(benchmark_output_dir, exist_ok=True)

    # Test causal discovery if this is a TEP benchmark
    if benchmark_name.startswith("tep"):
        print("Testing causal discovery for TEP environment...")
        try:
            # Create a simple test for causal discovery
            import numpy as np
            states = [np.random.rand(10) for _ in range(100)]
            next_states = [np.random.rand(10) for _ in range(100)]
            G, edges = infer_causal_graph(states, next_states, 10, verbose=True)
            print(f"Causal discovery test successful! Found {len(edges)} edges.")

            # Save causal graph if requested
            if save_causal_graphs:
                try:
                    # Import visualization module
                    from tci.visualization.causal_graphs import visualize_causal_graph

                    # Create directory for causal graphs
                    causal_graph_dir = os.path.join(benchmark_output_dir, "causal_graphs")
                    os.makedirs(causal_graph_dir, exist_ok=True)

                    # Save test causal graph
                    test_graph_path = os.path.join(causal_graph_dir, f"test_causal_graph.{causal_graph_format}")
                    visualize_causal_graph(G, edges, save_path=test_graph_path, format=causal_graph_format)
                    print(f"Saved test causal graph to {test_graph_path}")
                except Exception as e:
                    print(f"Warning: Failed to save causal graph: {e}")
        except Exception as e:
            print(f"Warning: Causal discovery test failed: {e}")

    # Check if this is a transfer learning benchmark
    is_transfer_learning = benchmark_config.get("transfer_learning", False)
    source_agent = None

    # For transfer learning, we need to train on the first environment
    # and then transfer to the second environment
    if is_transfer_learning:
        print("Transfer learning benchmark detected")
        env_names = benchmark_config["environments"]
        if len(env_names) < 2:
            print("Warning: Transfer learning requires at least 2 environments. Skipping.")
            return

        # First environment is the source
        source_env_name = env_names[0]
        print(f"Source Environment: {source_env_name}")

        # Get environment configuration
        source_env_config = get_environment_config(source_env_name)

        # Create environment to get dimensions
        source_env = create_environment(source_env_config, seed=42)
        state_dim = source_env.state_dim
        action_dim = source_env.action_dim

        # Create extensions
        extensions = []
        for ext_name in benchmark_config["extensions"]:
            ext_config = get_extension_config(ext_name)
            extension = create_extension(ext_config)
            extensions.append(extension)

        # Create source agent with all extensions
        print("Creating source agent with all extensions")
        source_agent = tci.TCIAgent(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device,
            extensions=extensions
        )

        # Train source agent
        print(f"Training source agent on {source_env_name}...")
        train_episodes = source_env_config["episodes"] // 2  # Use half the episodes for source training
        max_steps = source_env_config["max_steps"]

        # Generate random seeds
        seeds = np.random.randint(0, 10000, benchmark_config["seeds"])

        # Run benchmark on source environment
        source_results = run_benchmark(
            env_name=source_env_name,
            algorithms={"TCI-Source": source_agent},
            episodes=train_episodes,
            max_steps=max_steps,
            seeds=seeds,
            eval_interval=benchmark_config["eval_interval"],
            output_dir=benchmark_output_dir,
            verbose=True,
            save_causal_graphs=save_causal_graphs,
            causal_graph_format=causal_graph_format
        )

        # Save source agent
        source_model_path = os.path.join(benchmark_output_dir, f"{source_env_name}_source_agent.pt")
        source_agent.save(source_model_path)
        print(f"Source agent saved to {source_model_path}")

        # Now run transfer learning on target environment
        target_env_name = env_names[1]
        print(f"\nTarget Environment: {target_env_name}")

        # Get environment configuration
        target_env_config = get_environment_config(target_env_name)

        # Create environment to get dimensions
        target_env = create_environment(target_env_config, seed=42)

        # Create target agent with transfer learning
        print("Creating target agent with transfer learning")
        target_agent = tci.TCIAgent(
            state_dim=target_env.state_dim,
            action_dim=target_env.action_dim,
            device=device,
            extensions=extensions
        )

        # Load source model weights
        target_agent.load(source_model_path, transfer_learning=True)

        # Run benchmark on target environment
        target_results = run_benchmark(
            env_name=target_env_name,
            algorithms={
                "TCI-Transfer": target_agent,
                "TCI-Scratch": tci.TCIAgent(
                    state_dim=target_env.state_dim,
                    action_dim=target_env.action_dim,
                    device=device,
                    extensions=extensions
                )
            },
            episodes=target_env_config["episodes"],
            max_steps=target_env_config["max_steps"],
            seeds=seeds,
            eval_interval=benchmark_config["eval_interval"],
            output_dir=benchmark_output_dir,
            verbose=True
        )

        # Print final results
        print(f"\nFinal Results for {target_env_name}:")
        for name, metrics in target_results.items():
            final_rewards = [rewards[-1] for rewards in metrics["eval_rewards"]]
            final_reward = np.mean(final_rewards)
            std_error = np.std(final_rewards) / np.sqrt(len(final_rewards))
            print(f"{name}: {final_reward:.2f} ± {std_error:.2f} (95% CI: {final_reward - 1.96*std_error:.2f}, {final_reward + 1.96*std_error:.2f})")

        # Perform statistical significance tests if there are multiple algorithms
        if len(target_results) >= 2:
            # Extract final rewards for each algorithm
            final_rewards_dict = {}
            for name, metrics in target_results.items():
                final_rewards_dict[name] = [rewards[-1] for rewards in metrics["eval_rewards"]]

            # Perform t-test between transfer and scratch
            if "TCI-Transfer" in final_rewards_dict and "TCI-Scratch" in final_rewards_dict:
                t_stat, p_val = ttest_ind(
                    final_rewards_dict["TCI-Transfer"],
                    final_rewards_dict["TCI-Scratch"],
                    equal_var=False  # Welch's t-test
                )
                print(f"\nTransfer Learning Effect:")
                print(f"TCI-Transfer vs TCI-Scratch: t={t_stat:.4f}, p={p_val:.4f}, Significant={p_val < 0.05}")

                # Calculate effect size (Cohen's d)
                mean1 = np.mean(final_rewards_dict["TCI-Transfer"])
                mean2 = np.mean(final_rewards_dict["TCI-Scratch"])
                std1 = np.std(final_rewards_dict["TCI-Transfer"], ddof=1)
                std2 = np.std(final_rewards_dict["TCI-Scratch"], ddof=1)
                n1 = len(final_rewards_dict["TCI-Transfer"])
                n2 = len(final_rewards_dict["TCI-Scratch"])

                # Pooled standard deviation
                pooled_std = np.sqrt(((n1 - 1) * std1**2 + (n2 - 1) * std2**2) / (n1 + n2 - 2))

                # Cohen's d
                d = (mean1 - mean2) / pooled_std if pooled_std > 0 else 0
                effect_size = "small" if abs(d) < 0.5 else "medium" if abs(d) < 0.8 else "large"

                print(f"Effect Size (Cohen's d): {d:.4f} ({effect_size})")
                print(f"Improvement from Transfer Learning: {((mean1 - mean2) / mean2 * 100):.2f}%")

        return

    # Standard benchmark (non-transfer learning)
    # Run benchmark for each environment
    for env_name in benchmark_config["environments"]:
        print(f"Environment: {env_name}")

        # Get environment configuration
        env_config = get_environment_config(env_name)

        # Create environment to get dimensions
        env = create_environment(env_config, seed=42)
        state_dim = env.state_dim
        action_dim = env.action_dim

        # Create TCI variants
        tci_variants = {}

        # Base TCI agent
        # The TCIAgent expects state_dim to be the dimension of a single state
        # The BNN inside the agent will use state_dim * history_length as its input dimension
        history_length = 5

        # For TEP environment, use our custom TEPAgent
        if env_name == "tep":
            print(f"Using custom TEPAgent for TEP environment with state_dim={state_dim}")

            tci_variants["TCI-Base"] = TEPAgent(
                state_dim=state_dim,
                action_dim=action_dim,
                device=device,
                causal_discovery_params={
                    "method": causal_method,
                    "alpha": causal_alpha,
                    "sparsity_factor": sparsity_factor
                },
                history_length=history_length
            )
        else:
            # For other environments, use the original TCIAgent
            tci_variants["TCI-Base"] = tci.TCIAgent(
                state_dim=state_dim,
                action_dim=action_dim,
                device=device,
                causal_discovery_params={
                    "method": causal_method,
                    "alpha": causal_alpha,
                    "sparsity_factor": sparsity_factor
                },
                history_length=history_length
            )

        # Create extensions
        extensions = []
        for ext_name in benchmark_config["extensions"]:
            ext_config = get_extension_config(ext_name)
            extension = create_extension(ext_config, state_dim, action_dim, device)
            extensions.append(extension)

            # Add single extension variant
            if env_name == "tep":
                # Use custom TEPAgent for TEP environment
                tci_variants[f"TCI-{ext_name.upper()}"] = TEPAgent(
                    state_dim=state_dim,
                    action_dim=action_dim,
                    device=device,
                    extensions=[extension],
                    causal_discovery_params={
                        "method": causal_method,
                        "alpha": causal_alpha,
                        "sparsity_factor": sparsity_factor
                    },
                    history_length=history_length
                )
            else:
                # Use original TCIAgent for other environments
                tci_variants[f"TCI-{ext_name.upper()}"] = tci.TCIAgent(
                    state_dim=state_dim,
                    action_dim=action_dim,
                    device=device,
                    extensions=[extension],
                    causal_discovery_params={
                        "method": causal_method,
                        "alpha": causal_alpha,
                        "sparsity_factor": sparsity_factor
                    },
                    history_length=history_length
                )

        # Add all extensions variant
        if len(extensions) > 0:
            if env_name == "tep":
                # Use custom TEPAgent for TEP environment
                tci_variants["TCI-All"] = TEPAgent(
                    state_dim=state_dim,
                    action_dim=action_dim,
                    device=device,
                    extensions=extensions,
                    causal_discovery_params={
                        "method": causal_method,
                        "alpha": causal_alpha,
                        "sparsity_factor": sparsity_factor
                    },
                    history_length=history_length
                )
            else:
                # Use original TCIAgent for other environments
                tci_variants["TCI-All"] = tci.TCIAgent(
                    state_dim=state_dim,
                    action_dim=action_dim,
                    device=device,
                    extensions=extensions,
                    causal_discovery_params={
                        "method": causal_method,
                        "alpha": causal_alpha,
                        "sparsity_factor": sparsity_factor
                    },
                    history_length=history_length
                )

        # Create baselines
        baselines = {}
        for baseline_name in benchmark_config["baselines"]:
            baseline_config = get_baseline_config(baseline_name)
            baseline = create_baseline(baseline_config, state_dim, action_dim, device)
            baselines[baseline_name.upper()] = baseline

        # Combine algorithms
        algorithms = {**tci_variants, **baselines}

        # Filter algorithms if algorithm_name is specified
        if algorithm_name is not None:
            if algorithm_name in algorithms:
                algorithms = {algorithm_name: algorithms[algorithm_name]}
            else:
                print(f"Warning: Algorithm '{algorithm_name}' not found. Available algorithms: {list(algorithms.keys())}")
                return

        # Generate random seeds
        seeds = np.random.randint(0, 10000, benchmark_config["seeds"])

        # Run benchmark
        results = run_benchmark(
            env_name=env_name,
            algorithms=algorithms,
            episodes=env_config["episodes"],
            max_steps=env_config["max_steps"],
            seeds=seeds,
            eval_interval=benchmark_config["eval_interval"],
            output_dir=benchmark_output_dir,
            verbose=True,
            save_causal_graphs=save_causal_graphs,
            causal_graph_format=causal_graph_format
        )

        # Print final results
        print(f"\nFinal Results for {env_name}:")
        for name, metrics in results.items():
            final_rewards = [rewards[-1] for rewards in metrics["eval_rewards"]]
            final_reward = np.mean(final_rewards)
            std_error = np.std(final_rewards) / np.sqrt(len(final_rewards))
            print(f"{name}: {final_reward:.2f} ± {std_error:.2f} (95% CI: {final_reward - 1.96*std_error:.2f}, {final_reward + 1.96*std_error:.2f})")

        # Perform statistical significance tests if there are multiple algorithms
        if len(results) >= 2:
            # Extract final rewards for each algorithm
            final_rewards_dict = {}
            for name, metrics in results.items():
                final_rewards_dict[name] = [rewards[-1] for rewards in metrics["eval_rewards"]]

            # Perform ANOVA
            groups = [rewards for rewards in final_rewards_dict.values()]
            f_stat, p_val = f_oneway(*groups)
            print(f"\nANOVA Test: F={f_stat:.4f}, p={p_val:.4f}, Significant={p_val < 0.05}")

            # Perform pairwise t-tests
            print("\nPairwise t-tests:")
            algorithms = list(final_rewards_dict.keys())
            for i, algo1 in enumerate(algorithms):
                for algo2 in algorithms[i+1:]:
                    t_stat, p_val = ttest_ind(
                        final_rewards_dict[algo1],
                        final_rewards_dict[algo2],
                        equal_var=False  # Welch's t-test
                    )
                    print(f"{algo1} vs {algo2}: t={t_stat:.4f}, p={p_val:.4f}, Significant={p_val < 0.05}")


def run_all_benchmarks(args):
    """
    Run all benchmarks.

    Parameters
    ----------
    args : argparse.Namespace
        Command line arguments
    """
    # Set device
    device = "cuda" if args.gpu and torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # Parse benchmarks
    if args.benchmarks.lower() == "all":
        benchmarks = list(BENCHMARKS.keys())
    else:
        benchmarks = [b.strip() for b in args.benchmarks.split(",")]

    # Run benchmarks
    for benchmark_name in benchmarks:
        if benchmark_name not in BENCHMARKS:
            print(f"Warning: Unknown benchmark '{benchmark_name}'. Skipping.")
            continue

        # Run A/B test if requested
        if args.ab_test:
            run_ab_test_benchmark_wrapper(
                benchmark_name=benchmark_name,
                control_name=args.control,
                treatment_name=args.treatment,
                device=device,
                output_dir=os.path.join(args.output_dir, benchmark_name, "ab_test"),
                n_iterations=args.ab_iterations,
                alpha=args.ab_alpha,
                parametric=args.ab_parametric,
                correction_method=args.ab_correction,
                save_causal_graphs=args.save_causal_graphs,
                causal_graph_format=args.causal_graph_format
            )
        else:
            # Check if this is a causal discovery benchmark
            benchmark_config = get_benchmark_config(benchmark_name)
            if "causal_discovery_method" in benchmark_config and benchmark_name.startswith("causal_discovery_"):
                # Get the causal discovery method from the benchmark config
                method = benchmark_config["causal_discovery_method"]
                print(f"\nRunning benchmark with causal discovery method: {method}")
                run_single_benchmark(
                    benchmark_name=benchmark_name,
                    device=device,
                    output_dir=os.path.join(args.output_dir, f"causal_method_{method}"),
                    save_causal_graphs=args.save_causal_graphs,
                    causal_graph_format=args.causal_graph_format,
                    causal_method=method,
                    causal_alpha=0.1,
                    sparsity_factor=0.1
                )
            else:
                # Run standard benchmark
                run_single_benchmark(
                    benchmark_name=benchmark_name,
                    device=device,
                    output_dir=args.output_dir,
                    save_causal_graphs=args.save_causal_graphs,
                    causal_graph_format=args.causal_graph_format
                )


def create_statistical_report(results_dir: str, output_file: str = "statistical_report.html"):
    """
    Create a comprehensive statistical report from all benchmark results.

    Parameters
    ----------
    results_dir : str
        Directory containing benchmark results
    output_file : str, optional
        Output HTML file, by default "statistical_report.html"
    """
    # Find all stats JSON files
    stats_files = []
    for root, _, files in os.walk(results_dir):
        for file in files:
            if file.endswith("_stats.json"):
                stats_files.append(os.path.join(root, file))

    if not stats_files:
        print("No statistical results found.")
        return

    # Load all stats data
    all_stats = {}
    for file in stats_files:
        benchmark_name = os.path.basename(file).replace("_stats.json", "")
        with open(file, "r") as f:
            all_stats[benchmark_name] = json.load(f)

    # Create HTML report
    html = ["<!DOCTYPE html>",
            "<html>",
            "<head>",
            "<title>TCI Statistical Analysis Report</title>",
            "<style>",
            "body { font-family: Arial, sans-serif; margin: 20px; }",
            "h1, h2, h3 { color: #2c3e50; }",
            "table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }",
            "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }",
            "th { background-color: #f2f2f2; }",
            "tr:nth-child(even) { background-color: #f9f9f9; }",
            ".significant { color: green; font-weight: bold; }",
            ".not-significant { color: red; }",
            ".section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }",
            ".summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }",
            "</style>",
            "</head>",
            "<body>",
            "<h1>TCI Framework Statistical Analysis Report</h1>",
            "<div class='summary'>",
            f"<p>Report generated on {time.strftime('%Y-%m-%d %H:%M:%S')}</p>",
            f"<p>Number of benchmarks analyzed: {len(all_stats)}</p>",
            "</div>"]

    # Add executive summary
    html.append("<div class='section'>")
    html.append("<h2>Executive Summary</h2>")
    html.append("<p>This report presents a comprehensive statistical analysis of the Temporal Causality Infusion (TCI) framework's performance across multiple benchmarks. The analysis includes:</p>")
    html.append("<ul>")
    html.append("<li>Descriptive statistics (mean, standard deviation, confidence intervals)</li>")
    html.append("<li>Inferential statistics (ANOVA, t-tests, non-parametric tests)</li>")
    html.append("<li>Effect size calculations (Cohen's d, Cliff's delta)</li>")
    html.append("<li>Statistical power analysis</li>")
    html.append("<li>Learning curve metrics</li>")
    html.append("</ul>")

    # Add key findings
    significant_findings = []
    for benchmark, stats in all_stats.items():
        if "pairwise_tests" in stats:
            for comparison, test in stats["pairwise_tests"].items():
                if isinstance(test, dict) and test.get("significant", False):
                    if "effect_sizes" in stats and comparison in stats["effect_sizes"]:
                        effect = stats["effect_sizes"][comparison]
                        if isinstance(effect, dict) and "cohen_d" in effect:
                            d = effect["cohen_d"]
                            if isinstance(d, (int, float)) and abs(d) >= 0.8:  # Large effect
                                significant_findings.append(f"<li><strong>{benchmark}:</strong> {comparison} showed a statistically significant difference (p={test['p_value']:.4f}) with a large effect size (d={d:.2f})</li>")

    if significant_findings:
        html.append("<h3>Key Findings</h3>")
        html.append("<ul>")
        html.extend(significant_findings)
        html.append("</ul>")
    html.append("</div>")

    # Add detailed results for each benchmark
    for benchmark, stats in all_stats.items():
        html.append(f"<div class='section'>")
        html.append(f"<h2>Benchmark: {benchmark}</h2>")

        # Summary statistics
        if "summary_stats" in stats:
            html.append("<h3>Summary Statistics</h3>")
            html.append("<table>")

            # Table headers
            if isinstance(stats["summary_stats"], list) and len(stats["summary_stats"]) > 0:
                headers = list(stats["summary_stats"][0].keys())
                html.append("<tr>")
                for header in headers:
                    html.append(f"<th>{header}</th>")
                html.append("</tr>")

                # Table rows
                for row in stats["summary_stats"]:
                    html.append("<tr>")
                    for header in headers:
                        value = row.get(header, "")
                        if isinstance(value, (int, float)) and header not in ["count", "Algorithm"]:
                            html.append(f"<td>{value:.4f}</td>")
                        else:
                            html.append(f"<td>{value}</td>")
                    html.append("</tr>")

            html.append("</table>")

        # ANOVA results
        if "anova" in stats and stats["anova"] is not None:
            html.append("<h3>ANOVA Test</h3>")
            anova = stats["anova"]
            if isinstance(anova, dict) and "statistic" in anova and "pvalue" in anova:
                html.append("<p>")
                html.append(f"F-statistic: {anova['statistic']:.4f}<br>")
                html.append(f"p-value: {anova['pvalue']:.4f}<br>")
                significant = anova['pvalue'] < 0.05
                html.append(f"Significant: <span class='{'significant' if significant else 'not-significant'}'>{significant}</span>")
                html.append("</p>")

        # Kruskal-Wallis results (non-parametric)
        if "kruskal" in stats and stats["kruskal"] is not None:
            html.append("<h3>Kruskal-Wallis Test (Non-parametric)</h3>")
            kruskal = stats["kruskal"]
            if isinstance(kruskal, dict) and "statistic" in kruskal and "pvalue" in kruskal:
                html.append("<p>")
                html.append(f"H-statistic: {kruskal['statistic']:.4f}<br>")
                html.append(f"p-value: {kruskal['pvalue']:.4f}<br>")
                significant = kruskal['pvalue'] < 0.05
                html.append(f"Significant: <span class='{'significant' if significant else 'not-significant'}'>{significant}</span>")
                html.append("</p>")

        # Pairwise tests
        if "pairwise_tests" in stats and stats["pairwise_tests"]:
            html.append("<h3>Pairwise Tests</h3>")
            html.append("<table>")
            html.append("<tr><th>Comparison</th><th>t-statistic</th><th>p-value</th><th>Corrected p-value</th><th>Significant</th></tr>")

            for comparison, test in stats["pairwise_tests"].items():
                if isinstance(test, dict):
                    t_stat = test.get("t_statistic", "N/A")
                    p_val = test.get("p_value", "N/A")
                    corrected_p = test.get("corrected_p_value", "N/A")
                    significant = test.get("significant", False)

                    html.append("<tr>")
                    html.append(f"<td>{comparison}</td>")
                    html.append(f"<td>{t_stat:.4f if isinstance(t_stat, (int, float)) else t_stat}</td>")
                    html.append(f"<td>{p_val:.4f if isinstance(p_val, (int, float)) else p_val}</td>")
                    html.append(f"<td>{corrected_p:.4f if isinstance(corrected_p, (int, float)) else corrected_p}</td>")
                    html.append(f"<td class='{'significant' if significant else 'not-significant'}'>{significant}</td>")
                    html.append("</tr>")

            html.append("</table>")

        # Non-parametric tests
        if "mannwhitney_tests" in stats and stats["mannwhitney_tests"]:
            html.append("<h3>Mann-Whitney U Tests (Non-parametric)</h3>")
            html.append("<table>")
            html.append("<tr><th>Comparison</th><th>Statistic</th><th>p-value</th><th>Significant</th></tr>")

            for comparison, test in stats["mannwhitney_tests"].items():
                if isinstance(test, dict):
                    stat = test.get("statistic", "N/A")
                    p_val = test.get("p_value", "N/A")
                    significant = test.get("significant", False)

                    html.append("<tr>")
                    html.append(f"<td>{comparison}</td>")
                    html.append(f"<td>{stat:.4f if isinstance(stat, (int, float)) else stat}</td>")
                    html.append(f"<td>{p_val:.4f if isinstance(p_val, (int, float)) else p_val}</td>")
                    html.append(f"<td class='{'significant' if significant else 'not-significant'}'>{significant}</td>")
                    html.append("</tr>")

            html.append("</table>")

        # Effect sizes
        if "effect_sizes" in stats and stats["effect_sizes"]:
            html.append("<h3>Effect Sizes</h3>")
            html.append("<table>")
            html.append("<tr><th>Comparison</th><th>Cohen's d</th><th>Interpretation</th><th>Cliff's delta</th><th>Interpretation</th></tr>")

            for comparison, effect in stats["effect_sizes"].items():
                if isinstance(effect, dict):
                    cohen_d = effect.get("cohen_d", "N/A")
                    cohen_interp = effect.get("interpretation", "N/A")
                    cliff_delta = effect.get("cliff_delta", "N/A")
                    cliff_interp = effect.get("cliff_interpretation", "N/A")

                    html.append("<tr>")
                    html.append(f"<td>{comparison}</td>")
                    html.append(f"<td>{cohen_d:.4f if isinstance(cohen_d, (int, float)) else cohen_d}</td>")
                    html.append(f"<td>{cohen_interp}</td>")
                    html.append(f"<td>{cliff_delta:.4f if isinstance(cliff_delta, (int, float)) else cliff_delta}</td>")
                    html.append(f"<td>{cliff_interp}</td>")
                    html.append("</tr>")

            html.append("</table>")

        # Power analysis
        if "power_analysis" in stats and stats["power_analysis"]:
            html.append("<h3>Statistical Power Analysis</h3>")
            html.append("<table>")
            html.append("<tr><th>Comparison</th><th>Effect Size</th><th>Power</th><th>Sufficient Power</th><th>Sample Size for 80% Power</th></tr>")

            for comparison, power_data in stats["power_analysis"].items():
                if isinstance(power_data, dict):
                    effect_size = power_data.get("effect_size", "N/A")
                    power = power_data.get("power", "N/A")
                    sufficient = power_data.get("sufficient_power", False)
                    sample_size = power_data.get("sample_size_for_80_power", "N/A")

                    html.append("<tr>")
                    html.append(f"<td>{comparison}</td>")
                    html.append(f"<td>{effect_size:.4f if isinstance(effect_size, (int, float)) else effect_size}</td>")
                    html.append(f"<td>{power:.4f if isinstance(power, (int, float)) else power}</td>")
                    html.append(f"<td class='{'significant' if sufficient else 'not-significant'}'>{sufficient}</td>")
                    if isinstance(sample_size, float) and sample_size == float('inf'):
                        html.append(f"<td>Too small effect</td>")
                    else:
                        html.append(f"<td>{int(sample_size) if isinstance(sample_size, (int, float)) else sample_size}</td>")
                    html.append("</tr>")

            html.append("</table>")

        # Learning curve metrics
        if "learning_curve_metrics" in stats and stats["learning_curve_metrics"]:
            html.append("<h3>Learning Curve Metrics</h3>")
            html.append("<table>")
            html.append("<tr><th>Algorithm</th><th>AUC</th><th>AUC Std</th><th>Time to Threshold</th><th>Time to Threshold Std</th></tr>")

            for algo, metrics in stats["learning_curve_metrics"].items():
                if isinstance(metrics, dict):
                    auc = metrics.get("auc", "N/A")
                    auc_std = metrics.get("auc_std", "N/A")
                    time_to_threshold = metrics.get("time_to_threshold", "N/A")
                    time_std = metrics.get("time_to_threshold_std", "N/A")

                    html.append("<tr>")
                    html.append(f"<td>{algo}</td>")
                    html.append(f"<td>{auc:.4f if isinstance(auc, (int, float)) else auc}</td>")
                    html.append(f"<td>{auc_std:.4f if isinstance(auc_std, (int, float)) else auc_std}</td>")
                    if isinstance(time_to_threshold, float) and time_to_threshold == float('inf'):
                        html.append(f"<td>Never reached</td>")
                    else:
                        html.append(f"<td>{time_to_threshold:.1f if isinstance(time_to_threshold, (int, float)) else time_to_threshold}</td>")
                    html.append(f"<td>{time_std:.4f if isinstance(time_std, (int, float)) else time_std}</td>")
                    html.append("</tr>")

            html.append("</table>")

        html.append("</div>")

    # Close HTML
    html.append("</body>")
    html.append("</html>")

    # Write HTML to file
    output_path = os.path.join(results_dir, output_file)
    with open(output_path, "w") as f:
        f.write("\n".join(html))

    print(f"Statistical report generated: {output_path}")
    return output_path


def run_ab_test_benchmark_wrapper(
    benchmark_name: str,
    control_name: str,
    treatment_name: str,
    device: str,
    output_dir: str,
    n_iterations: int = 10,
    alpha: float = 0.05,
    parametric: bool = True,
    correction_method: str = 'fdr_bh',
    save_causal_graphs: bool = False,
    causal_graph_format: str = "png"
) -> None:
    """
    Run A/B test benchmark comparing control and treatment algorithms.

    Parameters
    ----------
    benchmark_name : str
        Name of the benchmark
    control_name : str
        Name of the control algorithm
    treatment_name : str
        Name of the treatment algorithm
    device : str
        Device to use
    output_dir : str
        Directory to save results
    n_iterations : int, optional
        Number of iterations to run, by default 10
    alpha : float, optional
        Significance level, by default 0.05
    parametric : bool, optional
        Whether to use parametric tests, by default True
    correction_method : str, optional
        Method for multiple comparison correction, by default 'fdr_bh'
    save_causal_graphs : bool, optional
        Whether to save causal graphs, by default False
    causal_graph_format : str, optional
        Format for saving causal graphs, by default "png"
    """
    # Import required modules
    import numpy as np
    import torch
    print(f"Running A/B test benchmark: {benchmark_name}")
    print(f"Control: {control_name}, Treatment: {treatment_name}")
    print(f"Iterations: {n_iterations}, Alpha: {alpha}, Parametric: {parametric}")

    # Get benchmark configuration
    benchmark_config = get_benchmark_config(benchmark_name)

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Run standard benchmark for control and treatment
    print(f"\nRunning control benchmark ({control_name})...")
    run_single_benchmark(
        benchmark_name=benchmark_name,
        device=device,
        output_dir=os.path.join(output_dir, "control"),
        save_causal_graphs=save_causal_graphs,
        causal_graph_format=causal_graph_format,
        algorithm_name=control_name
    )

    print(f"\nRunning treatment benchmark ({treatment_name})...")
    run_single_benchmark(
        benchmark_name=benchmark_name,
        device=device,
        output_dir=os.path.join(output_dir, "treatment"),
        save_causal_graphs=save_causal_graphs,
        causal_graph_format=causal_graph_format,
        algorithm_name=treatment_name
    )

    print(f"\nA/B test benchmark complete. Results saved to {output_dir}")
    print("To perform statistical analysis, compare the results in the control and treatment directories.")


def main():
    """Run all benchmarks and generate statistical report."""
    args = parse_args()
    run_all_benchmarks(args)

    # Generate statistical report if requested
    if args.generate_report and args.output_dir:
        report_path = create_statistical_report(args.output_dir, args.report_file)
        print(f"\nStatistical report generated: {report_path}")
        print("Open this file in a web browser to view the comprehensive statistical analysis.")


if __name__ == "__main__":
    main()
