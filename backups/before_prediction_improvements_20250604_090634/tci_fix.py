"""
TCI-fix Model: An enhanced implementation of Temporal Causality Infusion
for accurate time series forecasting.

This model improves upon the original TCI implementation by:
1. Enhancing causal discovery with more robust algorithms
2. Improving temporal feature engineering
3. Adding better uncertainty estimation
4. Implementing transfer learning between products
5. Providing more accurate backtesting capabilities
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import pickle
from sklearn.preprocessing import MinMaxScaler
from sklearn.ensemble import RandomForestRegressor
import networkx as nx
from statsmodels.tsa.stattools import grangercausalitytests
from statsmodels.tsa.seasonal import seasonal_decompose
import warnings
warnings.filterwarnings('ignore')

class TCIFixPredictor:
    """
    TCIFixPredictor: Enhanced Temporal Causality Infusion model for time series forecasting.

    This class implements an improved version of the TCI algorithm with better
    causal discovery, temporal feature engineering, and uncertainty estimation.
    """

    def __init__(self,
                 max_lag=12,  # Increased from 6 to capture longer patterns
                 causality_alpha=0.05,
                 n_estimators=200,  # Increased for better ensemble averaging
                 min_samples_leaf=4,  # Increased to reduce overfitting
                 max_depth=20,  # Added to limit tree depth and prevent overfitting
                 random_state=42,
                 uncertainty_quantiles=[0.1, 0.9],
                 feature_engineering_level='advanced',
                 transfer_learning=True,
                 trend_weight=1.5,  # Added to emphasize trend components
                 seasonal_weight=1.2,  # Added to emphasize seasonal components
                 balance_historical_future=True):
        """
        Initialize the TCIFixPredictor.

        Parameters:
        -----------
        max_lag : int
            Maximum lag to consider for causal discovery and feature engineering
        causality_alpha : float
            Significance level for Granger causality tests
        n_estimators : int
            Number of trees in the Random Forest model
        min_samples_leaf : int
            Minimum samples required at a leaf node in Random Forest
        random_state : int
            Random seed for reproducibility
        uncertainty_quantiles : list
            Lower and upper quantiles for uncertainty estimation
        feature_engineering_level : str
            Level of feature engineering ('basic', 'intermediate', 'advanced')
        transfer_learning : bool
            Whether to use transfer learning between products
        """
        self.max_lag = max_lag
        self.causality_alpha = causality_alpha
        self.n_estimators = n_estimators
        self.min_samples_leaf = min_samples_leaf
        self.max_depth = max_depth
        self.random_state = random_state
        self.uncertainty_quantiles = uncertainty_quantiles
        self.feature_engineering_level = feature_engineering_level
        self.transfer_learning = transfer_learning
        self.trend_weight = trend_weight
        self.seasonal_weight = seasonal_weight
        self.balance_historical_future = balance_historical_future

        # Initialize model components
        self.causal_graph = None
        self.feature_importances = None
        self.model = None
        self.scaler_X = MinMaxScaler()
        self.scaler_y = MinMaxScaler()
        self.feature_columns = None
        self.external_data = None  # Store external data for reuse during prediction
        self.target_column = 'quantity'
        self.date_column = 'date'

    def fit(self, data, external_data=None):
        """
        Fit the TCI-fix model to the data.

        Parameters:
        -----------
        data : pandas.DataFrame
            Time series data with date and quantity columns
        external_data : dict of pandas.DataFrame
            Dictionary of external data sources

        Returns:
        --------
        self : TCIFixPredictor
            Fitted model
        """
        print("Step 1: Preprocessing data...")
        # Store external data for reuse during prediction
        self.external_data = external_data
        processed_data = self._preprocess_data(data, external_data)

        print("Step 2: Engineering features...")
        feature_data = self._engineer_features(processed_data)

        print("Step 3: Discovering causal relationships...")
        self.causal_graph = self._discover_causal_relationships(feature_data)

        print("Step 4: Creating causal features...")
        causal_feature_data = self._create_causal_features(feature_data)

        print("Step 5: Training predictive model...")
        self._train_model(causal_feature_data)

        print("Model training complete!")
        return self

    def predict(self, start_date, end_date, data=None, external_data=None, return_uncertainty=True):
        """
        Generate predictions for the specified date range.

        Parameters:
        -----------
        start_date : str or datetime
            Start date for predictions
        end_date : str or datetime
            End date for predictions
        data : pandas.DataFrame
            Historical data to use as context (if None, uses training data)
        external_data : dict of pandas.DataFrame
            Dictionary of external data sources
        return_uncertainty : bool
            Whether to return uncertainty intervals

        Returns:
        --------
        pandas.DataFrame
            DataFrame with predictions and optional uncertainty intervals
        """
        # Convert dates to strings for printing
        start_date_str = start_date if isinstance(start_date, str) else str(start_date)
        end_date_str = end_date if isinstance(end_date, str) else str(end_date)
        print(f"Generating predictions from {start_date_str} to {end_date_str}...")

        # Check if model is trained
        if self.model is None:
            raise ValueError("Model is not trained. Call fit() first.")

        # **FIX 1: Automatically use stored external data if none provided**
        if external_data is None and hasattr(self, 'external_data') and self.external_data is not None:
            external_data = self.external_data
            print(f"Using stored external data from training ({len(external_data)} sources)")
        elif external_data is not None:
            print(f"Using provided external data ({len(external_data)} sources)")
        else:
            print("No external data available for prediction")

        # Convert dates to datetime if they are strings
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)

        # Create a dataframe with the date range for predictions
        date_range = pd.date_range(
            start=start_date.replace(day=25),
            end=end_date.replace(day=25),
            freq='MS'  # Month start
        ).map(lambda x: x.replace(day=25))

        pred_df = pd.DataFrame({self.date_column: date_range})

        # If we have historical data, use it for context
        if data is not None:
            # Preprocess the historical data
            hist_data = self._preprocess_data(data, external_data)

            # Combine historical data with prediction dates
            combined_df = pd.concat([hist_data, pred_df])
            combined_df = combined_df.drop_duplicates(subset=[self.date_column])
            combined_df = combined_df.sort_values(by=self.date_column)

            # Fill missing values in the target column with 0 for prediction dates
            combined_df[self.target_column] = combined_df[self.target_column].fillna(0)
        else:
            # If no historical data provided, just use prediction dates
            combined_df = pred_df
            combined_df[self.target_column] = 0

        # Preprocess the combined data with external data
        processed_df = self._preprocess_data(combined_df, external_data)

        # Engineer features
        feature_df = self._engineer_features(processed_df)

        # Create causal features
        causal_feature_df = self._create_causal_features(feature_df)

        # Filter to only the prediction dates
        pred_feature_df = causal_feature_df[causal_feature_df[self.date_column].isin(date_range)]

        # Check if we have any prediction dates
        if len(pred_feature_df) == 0:
            print("Warning: No prediction dates found in the processed data")
            print("Creating default prediction dataframe with the requested date range")

            # Create a default prediction dataframe
            pred_feature_df = pd.DataFrame({self.date_column: date_range})

            # Add the target column
            pred_feature_df[self.target_column] = 0

            # Add all required feature columns with default values
            for feature in self.feature_columns:
                pred_feature_df[feature] = 0

        # If we don't have all features, we need to handle this
        missing_features = set(self.feature_columns) - set(pred_feature_df.columns)
        for feature in missing_features:
            pred_feature_df[feature] = 0  # Fill with 0 as a default

        # Ensure we have all required features in the correct order
        X_pred = pred_feature_df[self.feature_columns]

        # Check if X_pred is empty
        if len(X_pred) == 0:
            print("WARNING: Empty prediction array. Creating a dummy row for prediction.")
            # Create a dummy row with zeros for all features
            dummy_row = pd.DataFrame({feature: [0] for feature in self.feature_columns})
            X_pred = dummy_row

        # Double-check that we have data
        print(f"Prediction data shape: {X_pred.shape}")
        if X_pred.shape[0] == 0 or X_pred.shape[1] == 0:
            raise ValueError(f"Invalid prediction data shape: {X_pred.shape}")

        # Scale features
        try:
            X_pred_scaled = self.scaler_X.transform(X_pred)
        except Exception as e:
            print(f"Error during scaling: {str(e)}")
            print(f"X_pred shape: {X_pred.shape}")
            print(f"X_pred columns: {X_pred.columns.tolist()}")
            print(f"Feature columns: {self.feature_columns}")
            raise

        # Generate predictions
        y_pred_scaled = self.model.predict(X_pred_scaled)

        # Inverse transform to get predictions in original scale
        y_pred = self.scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()

        # Check if we used a dummy row
        used_dummy = len(X_pred) == 1 and len(pred_feature_df) == 0

        # Create results dataframe
        if used_dummy:
            print("Using date range for results since we used a dummy row")
            # Use the original date range since pred_feature_df is empty
            results_df = pd.DataFrame({
                self.date_column: date_range,
                'prediction': [y_pred[0]] * len(date_range)  # Use the same prediction for all dates
            })
        else:
            # Normal case - use the prediction dataframe dates
            # Ensure dates are valid datetime objects
            valid_dates = pd.to_datetime(pred_feature_df[self.date_column], errors='coerce')
            # Check for NaT values
            if valid_dates.isna().any():
                print(f"Warning: Found {valid_dates.isna().sum()} NaT values in dates. Replacing with valid dates.")
                # Replace NaT values with the last valid date + 1 month for each
                last_valid_date = valid_dates.dropna().iloc[-1] if not valid_dates.dropna().empty else pd.Timestamp('2023-01-25')
                for i in range(len(valid_dates)):
                    if pd.isna(valid_dates.iloc[i]):
                        # Add one month for each NaT value
                        last_valid_date = last_valid_date + pd.DateOffset(months=1)
                        valid_dates.iloc[i] = last_valid_date

            results_df = pd.DataFrame({
                self.date_column: valid_dates,
                'prediction': y_pred
            })

        # Check if we have the has_forecast feature in the prediction data
        if 'has_forecast' in pred_feature_df.columns:
            # Add the has_forecast column to the results
            results_df['has_forecast'] = pred_feature_df['has_forecast'].values

            # For dates without forecasts (has_forecast=0), we can optionally adjust the predictions
            # For example, we might want to make them exactly zero
            zero_forecast_mask = results_df['has_forecast'] == 0
            if zero_forecast_mask.any():
                print(f"Setting predictions to exactly zero for {zero_forecast_mask.sum()} dates without forecasts")
                results_df.loc[zero_forecast_mask, 'prediction'] = 0

        # Apply post-processing to improve future predictions
        # Check if we're predicting future (after the last historical date)
        if data is not None and len(data) > 0:
            last_historical_date = pd.to_datetime(data[self.date_column]).max()
            last_historical_value = data.loc[data[self.date_column] == data[self.date_column].max(), self.target_column].values[0]
            future_mask = pd.to_datetime(results_df[self.date_column]) > last_historical_date

            if future_mask.any():
                print("Applying post-processing to future predictions...")
                print(f"Last historical value: {last_historical_value}")
                future_indices = future_mask[future_mask].index

                # Get the last 12 months (or less) of historical data for trend analysis
                if len(data) >= 12:
                    recent_data = data.iloc[-12:].copy()
                else:
                    recent_data = data.copy()

                # Calculate recent trend using the last 12 months (increased from 6) to better capture annual patterns
                if len(recent_data) >= 12:
                    very_recent_data = recent_data.iloc[-12:].copy()
                else:
                    very_recent_data = recent_data.copy()

                # Also look at the most recent 3 months for very short-term trends
                if len(recent_data) >= 3:
                    ultra_recent_data = recent_data.iloc[-3:].copy()
                else:
                    ultra_recent_data = recent_data.copy()

                # Handle holes in the data by focusing on dates with actual forecasts
                if 'has_forecast' in very_recent_data.columns:
                    # If we have the has_forecast column, use it to filter out dates without forecasts
                    very_recent_data_filtered = very_recent_data[very_recent_data['has_forecast'] == 1].copy()
                    ultra_recent_data_filtered = ultra_recent_data[ultra_recent_data['has_forecast'] == 1].copy()
                    print(f"Filtered recent data using has_forecast column: {len(very_recent_data_filtered)} out of {len(very_recent_data)} points")
                else:
                    # Otherwise, filter out zero values which represent "no forecast"
                    very_recent_data_filtered = very_recent_data[very_recent_data[self.target_column] > 0].copy()
                    ultra_recent_data_filtered = ultra_recent_data[ultra_recent_data[self.target_column] > 0].copy()
                    print(f"Filtered recent data to remove zeros (treating as no forecast): {len(very_recent_data_filtered)} out of {len(very_recent_data)} points")

                # Calculate trends for different time periods
                very_recent_trend = 0
                ultra_recent_trend = 0

                # Calculate 12-month trend if we have enough data
                if len(very_recent_data_filtered) >= 2:
                    # Reset index for proper trend calculation
                    very_recent_data_filtered = very_recent_data_filtered.reset_index(drop=True)
                    very_recent_values = very_recent_data_filtered[self.target_column].values
                    very_recent_trend = np.polyfit(np.arange(len(very_recent_values)), very_recent_values, 1)[0]
                    print(f"12-month trend (filtered data): {very_recent_trend:.2f} units per month")
                elif len(very_recent_data) >= 3:
                    # Fall back to all data if filtered data is insufficient
                    very_recent_values = very_recent_data[self.target_column].values
                    very_recent_trend = np.polyfit(np.arange(len(very_recent_values)), very_recent_values, 1)[0]
                    print(f"12-month trend (all data): {very_recent_trend:.2f} units per month")

                # Calculate 3-month trend if we have enough data
                if len(ultra_recent_data_filtered) >= 2:
                    # Reset index for proper trend calculation
                    ultra_recent_data_filtered = ultra_recent_data_filtered.reset_index(drop=True)
                    ultra_recent_values = ultra_recent_data_filtered[self.target_column].values
                    ultra_recent_trend = np.polyfit(np.arange(len(ultra_recent_values)), ultra_recent_values, 1)[0]
                    print(f"3-month trend (filtered data): {ultra_recent_trend:.2f} units per month")
                elif len(ultra_recent_data) >= 2:
                    # Fall back to all data if filtered data is insufficient
                    ultra_recent_values = ultra_recent_data[self.target_column].values
                    ultra_recent_trend = np.polyfit(np.arange(len(ultra_recent_values)), ultra_recent_values, 1)[0]
                    print(f"3-month trend (all data): {ultra_recent_trend:.2f} units per month")

                # Calculate a weighted average of the trends, giving more weight to the most recent data
                if ultra_recent_trend != 0 and very_recent_trend != 0:
                    # If both trends are available, use a weighted average (70% ultra-recent, 30% very-recent)
                    weighted_trend = (0.7 * ultra_recent_trend) + (0.3 * very_recent_trend)
                    print(f"Weighted trend (70% 3-month, 30% 12-month): {weighted_trend:.2f} units per month")
                    very_recent_trend = weighted_trend

                # Calculate longer-term trend
                if len(recent_data) >= 3:
                    recent_values = recent_data[self.target_column].values
                    recent_trend = np.polyfit(np.arange(len(recent_values)), recent_values, 1)[0]
                    print(f"Recent trend (last 12 months): {recent_trend:.2f} units per month")
                else:
                    recent_trend = 0

                # Determine if there's a strong upward trend in recent data
                strong_upward_trend = (very_recent_trend > 0 and recent_trend > 0) or very_recent_trend > 1000

                # Check if the first prediction is significantly lower than the last historical value
                first_prediction = results_df.loc[future_indices[0], 'prediction']
                drop_ratio = first_prediction / last_historical_value if last_historical_value > 0 else 1.0
                significant_drop = drop_ratio < 0.8  # More than 20% drop

                print(f"First prediction: {first_prediction}, Drop ratio: {drop_ratio:.2f}")

                # Apply pattern-preserving correction to future predictions
                print("Applying pattern-preserving correction to future predictions...")

                # Extract seasonal patterns from historical data with enhanced pattern recognition
                if len(data) >= 12:  # Need at least 1 year for basic seasonal patterns
                    try:
                        # Get monthly seasonal factors with more sophisticated approach
                        monthly_data = data.copy()
                        monthly_data['date'] = pd.to_datetime(monthly_data[self.date_column])
                        monthly_data['month'] = monthly_data['date'].dt.month
                        monthly_data['year'] = monthly_data['date'].dt.year

                        # Calculate monthly patterns with more weight to recent years
                        monthly_patterns = {}
                        years = sorted(monthly_data['year'].unique())

                        # If we have multiple years, weight recent years more heavily
                        if len(years) > 1:
                            print(f"Calculating weighted seasonal patterns across {len(years)} years")
                            # Calculate weights for each year with stronger emphasis on recent years
                            # Exponential weighting to give much more importance to recent years
                            max_weight = 3.0  # Maximum weight for most recent year
                            year_weights = {year: 1 + (max_weight - 1) * (i / (len(years) - 1))**2 for i, year in enumerate(years)}
                            print(f"Year weights: {year_weights}")

                            # Calculate weighted monthly factors
                            weighted_monthly_values = {month: 0 for month in range(1, 13)}
                            weighted_counts = {month: 0 for month in range(1, 13)}

                            # Collect weighted values for each month
                            for year in years:
                                year_data = monthly_data[monthly_data['year'] == year]
                                for month in range(1, 13):
                                    month_data = year_data[year_data['month'] == month]
                                    if len(month_data) > 0 and month_data[self.target_column].sum() > 0:
                                        # Only include months with actual data (not zeros)
                                        weight = year_weights[year]
                                        weighted_monthly_values[month] += month_data[self.target_column].mean() * weight
                                        weighted_counts[month] += weight

                            # Calculate final weighted averages
                            monthly_avgs = {}
                            for month in range(1, 13):
                                if weighted_counts[month] > 0:
                                    monthly_avgs[month] = weighted_monthly_values[month] / weighted_counts[month]
                                else:
                                    # If no data for this month, use overall average
                                    monthly_avgs[month] = monthly_data[self.target_column].mean()

                            # Convert to Series and normalize
                            monthly_factors = pd.Series(monthly_avgs)
                        else:
                            # If only one year, use simple monthly averages
                            monthly_factors = monthly_data.groupby('month')[self.target_column].mean()

                        # Handle months with zero values specially
                        for month in range(1, 13):
                            if month not in monthly_factors or monthly_factors[month] == 0:
                                # For months with no data or zeros, use the median of non-zero months
                                non_zero_factors = monthly_factors[monthly_factors > 0]
                                if len(non_zero_factors) > 0:
                                    monthly_factors[month] = non_zero_factors.median()
                                else:
                                    monthly_factors[month] = 1.0

                        # Normalize factors
                        if monthly_factors.mean() > 0:
                            monthly_factors = monthly_factors / monthly_factors.mean()
                        else:
                            monthly_factors = pd.Series([1.0] * 12, index=range(1, 13))

                        # Enhance seasonal factors based on pattern detection
                        # Identify months with consistently high or low values
                        high_months = []
                        low_months = []
                        zero_months = []

                        # Calculate overall statistics for comparison
                        overall_mean = monthly_data[self.target_column].mean()
                        overall_median = monthly_data[self.target_column].median()
                        overall_std = monthly_data[self.target_column].std()

                        # Calculate month-to-month ratios for all adjacent months
                        month_to_month_ratios = {}
                        for year in years:
                            year_data = monthly_data[monthly_data['year'] == year].sort_values('month')
                            if len(year_data) >= 2:
                                for i in range(len(year_data) - 1):
                                    current_month = year_data.iloc[i]['month']
                                    next_month = year_data.iloc[i+1]['month']
                                    current_val = year_data.iloc[i][self.target_column]
                                    next_val = year_data.iloc[i+1][self.target_column]

                                    # Only calculate ratio if both values are non-zero
                                    if current_val > 0 and next_val > 0:
                                        ratio = next_val / current_val
                                        key = (current_month, next_month)
                                        if key not in month_to_month_ratios:
                                            month_to_month_ratios[key] = []
                                        month_to_month_ratios[key].append(ratio)

                        # Calculate median ratios for each month pair
                        median_month_ratios = {}
                        for key, ratios in month_to_month_ratios.items():
                            if len(ratios) >= 2:
                                median_month_ratios[key] = np.median(ratios)

                        # Analyze each month in detail
                        for month in range(1, 13):
                            month_data = monthly_data[monthly_data['month'] == month]
                            if len(month_data) >= 2:  # Need at least 2 data points
                                # Get statistics for this month
                                month_mean = month_data[self.target_column].mean()
                                month_median = month_data[self.target_column].median()
                                month_std = month_data[self.target_column].std()
                                month_cv = month_std / month_mean if month_mean > 0 else 0

                                # Check for consistent zeros or very low values
                                zero_ratio = (month_data[self.target_column] == 0).mean()
                                low_ratio = (month_data[self.target_column] < overall_mean * 0.2).mean()

                                if zero_ratio > 0.4:  # Lowered threshold from 0.5 to 0.4
                                    zero_months.append(month)
                                    # Set seasonal factor low for consistent zero months
                                    monthly_factors[month] = 0.2  # Lower factor for more aggressive zero prediction
                                    print(f"Month {month} typically has zero values ({zero_ratio:.1%} of the time)")
                                elif low_ratio > 0.6:  # If most values are very low
                                    low_months.append(month)
                                    # Reduce the seasonal factor for consistently low months
                                    monthly_factors[month] *= 0.7  # More aggressive reduction
                                    print(f"Month {month} typically has very low values ({low_ratio:.1%} of the time)")
                                elif month_mean > overall_mean * 1.3:  # If month is significantly above average
                                    high_months.append(month)
                                    # Calculate how much higher this month is compared to overall
                                    ratio = month_mean / overall_mean
                                    # Apply a more aggressive amplification for high months
                                    amplification = 1.0 + ((ratio - 1.0) * 1.2)  # 20% boost to the difference
                                    monthly_factors[month] *= amplification
                                    print(f"Month {month} typically has high values (ratio: {ratio:.2f}, amplified to: {amplification:.2f})")
                                elif month_mean < overall_mean * 0.7:  # If month is significantly below average
                                    low_months.append(month)
                                    # Calculate how much lower this month is compared to overall
                                    ratio = month_mean / overall_mean
                                    # Apply a more aggressive reduction for low months
                                    reduction = ratio * 0.9  # 10% additional reduction
                                    monthly_factors[month] *= reduction
                                    print(f"Month {month} typically has low values (ratio: {ratio:.2f}, reduced to: {reduction:.2f})")

                        # Re-normalize after adjustments
                        if monthly_factors.mean() > 0:
                            monthly_factors = monthly_factors / monthly_factors.mean()

                        print(f"Extracted enhanced seasonal factors from historical data")
                        print(f"High-value months: {high_months}")
                        print(f"Low-value months: {low_months}")
                        print(f"Zero-value months: {zero_months}")
                    except Exception as e:
                        print(f"Error extracting seasonal patterns: {str(e)}")
                        # Default to no seasonality if extraction fails
                        monthly_factors = pd.Series([1.0] * 12, index=range(1, 13))
                        print("Using default monthly factors (no seasonality)")
                else:
                    # Default to no seasonality if not enough data
                    monthly_factors = pd.Series([1.0] * 12, index=range(1, 13))
                    print("Not enough historical data for reliable seasonal patterns")

                # Enhanced multi-scale pattern analysis
                print("\nPerforming enhanced multi-scale pattern analysis...")

                # Prepare historical data for trajectory analysis
                hist_data = data.copy()
                hist_data[self.date_column] = pd.to_datetime(hist_data[self.date_column])
                hist_data = hist_data.sort_values(by=self.date_column)

                # Calculate moving averages to smooth out noise
                if len(hist_data) >= 6:
                    hist_data['ma3'] = hist_data[self.target_column].rolling(window=3, min_periods=1).mean()
                    hist_data['ma6'] = hist_data[self.target_column].rolling(window=6, min_periods=1).mean()
                    hist_data['ma12'] = hist_data[self.target_column].rolling(window=12, min_periods=1).mean()

                # Calculate rate of change at different time scales
                if len(hist_data) >= 4:
                    hist_data['roc3'] = hist_data[self.target_column].pct_change(periods=3)
                    if len(hist_data) >= 7:
                        hist_data['roc6'] = hist_data[self.target_column].pct_change(periods=6)
                        if len(hist_data) >= 13:
                            hist_data['roc12'] = hist_data[self.target_column].pct_change(periods=12)

                # Detect change points using moving standard deviation
                if len(hist_data) >= 6:
                    hist_data['std3'] = hist_data[self.target_column].rolling(window=3).std()
                    hist_data['std_ratio'] = hist_data['std3'] / hist_data[self.target_column].mean()
                    # Identify potential change points where volatility spikes
                    change_points = hist_data[hist_data['std_ratio'] > hist_data['std_ratio'].mean() + hist_data['std_ratio'].std()]
                    if len(change_points) > 0:
                        print(f"Detected {len(change_points)} potential change points in the data")
                        # Get the most recent change point
                        last_change_point = change_points.iloc[-1][self.date_column]
                        print(f"Most recent change point: {last_change_point.strftime('%Y-%m-%d')}")
                        # Calculate months since last change point
                        last_date = hist_data[self.date_column].max()
                        months_since_change = (last_date.year - last_change_point.year) * 12 + (last_date.month - last_change_point.month)
                        print(f"Months since last change point: {months_since_change}")
                    else:
                        print("No significant change points detected")
                        months_since_change = 36  # Assume stable pattern if no change points
                else:
                    months_since_change = 0

                # Multi-scale segmentation for pattern detection
                # Define different time scales for analysis
                time_scales = []

                if len(hist_data) >= 36:  # If we have 3+ years of data
                    time_scales.append(("long-term", 3, len(hist_data) // 3))
                if len(hist_data) >= 24:  # If we have 2+ years of data
                    time_scales.append(("medium-term", 2, len(hist_data) // 2))
                if len(hist_data) >= 12:  # If we have 1+ year of data
                    time_scales.append(("short-term", 4, 3))  # Last quarter, last 3 months
                if len(hist_data) >= 6:   # If we have 6+ months of data
                    time_scales.append(("very-short-term", 6, 1))  # Last month

                # Store trends at different time scales
                trends = {}

                # Analyze each time scale
                for scale_name, num_segments, recent_segment_size in time_scales:
                    print(f"\nAnalyzing {scale_name} patterns:")

                    if scale_name == "very-short-term":
                        # For very short term, just look at the last few months
                        recent_data = hist_data.iloc[-recent_segment_size:]
                        if len(recent_data) >= 2:  # Need at least 2 points for trend
                            recent_trend = np.polyfit(np.arange(len(recent_data)), recent_data[self.target_column].values, 1)[0]
                            print(f"  Very recent trend (last month): {recent_trend:.2f} units/month")
                            trends["very_recent"] = recent_trend

                    elif scale_name == "short-term":
                        # For short term, focus on recent months
                        recent_data = hist_data.iloc[-recent_segment_size:]
                        if len(recent_data) >= 2:
                            recent_trend = np.polyfit(np.arange(len(recent_data)), recent_data[self.target_column].values, 1)[0]
                            print(f"  Recent trend (last quarter): {recent_trend:.2f} units/month")
                            trends["recent"] = recent_trend

                        # Also look at last 6 months if available
                        if len(hist_data) >= 6:
                            last_6m_data = hist_data.iloc[-6:]
                            last_6m_trend = np.polyfit(np.arange(len(last_6m_data)), last_6m_data[self.target_column].values, 1)[0]
                            print(f"  Last 6 months trend: {last_6m_trend:.2f} units/month")
                            trends["last_6m"] = last_6m_trend

                    else:  # medium-term and long-term
                        # Divide into segments
                        segment_size = len(hist_data) // num_segments

                        # Calculate trends for each segment
                        segment_trends = []
                        for i in range(num_segments):
                            if i < num_segments - 1:
                                segment_data = hist_data.iloc[i*segment_size:(i+1)*segment_size]
                            else:
                                segment_data = hist_data.iloc[i*segment_size:]  # Last segment takes all remaining

                            if len(segment_data) >= 2:  # Need at least 2 points for trend
                                segment_trend = np.polyfit(np.arange(len(segment_data)), segment_data[self.target_column].values, 1)[0]
                                segment_trends.append(segment_trend)
                                print(f"  Segment {i+1} trend: {segment_trend:.2f} units/month")

                        # Store trends for this time scale
                        if scale_name == "medium-term":
                            if len(segment_trends) >= 2:
                                trends["medium_early"] = segment_trends[0]
                                trends["medium_recent"] = segment_trends[-1]
                        elif scale_name == "long-term":
                            if len(segment_trends) >= 3:
                                trends["long_early"] = segment_trends[0]
                                trends["long_middle"] = segment_trends[1]
                                trends["long_recent"] = segment_trends[-1]

                # Calculate overall trend
                overall_trend = np.polyfit(np.arange(len(hist_data)), hist_data[self.target_column].values, 1)[0]
                print(f"\nOverall trend: {overall_trend:.2f} units/month")
                trends["overall"] = overall_trend

                # Get very recent trend (last 6 months or less)
                if len(hist_data) >= 6:
                    very_recent_data = hist_data.iloc[-6:]
                else:
                    very_recent_data = hist_data

                very_recent_trend = np.polyfit(np.arange(len(very_recent_data)), very_recent_data[self.target_column].values, 1)[0]
                print(f"Very recent trend (last 6 months): {very_recent_trend:.2f} units/month")

                # Enhanced pattern detection using multi-scale analysis
                print("\nPerforming enhanced pattern detection...")

                # Initialize pattern variables
                pattern_change = "unknown"
                pattern_confidence = 0.0

                # Check if we have enough trend data for analysis
                if len(trends) >= 3:
                    # Get trends at different time scales
                    recent_trend = trends.get("recent", trends.get("last_6m", very_recent_trend))

                    # Determine if we have long-term data
                    if "long_early" in trends and "long_middle" in trends and "long_recent" in trends:
                        # Use long-term trends for pattern detection
                        early_trend = trends["long_early"]
                        middle_trend = trends["long_middle"]
                        recent_trend_long = trends["long_recent"]

                        # Detect pattern changes using long-term data
                        if early_trend < 0 and middle_trend > 0 and recent_trend_long > 0:
                            pattern_change = "recovery"
                            pattern_confidence = 0.8
                            print("Pattern detected: RECOVERY (negative to positive trend)")
                            print(f"Confidence: {pattern_confidence:.2f}")

                        elif early_trend > 0 and middle_trend > 0 and recent_trend_long > 0:
                            if recent_trend_long > middle_trend * 1.5:
                                pattern_change = "acceleration"
                                pattern_confidence = 0.9
                                print("Pattern detected: ACCELERATION (increasing positive trend)")
                                print(f"Confidence: {pattern_confidence:.2f}")
                            elif recent_trend_long < middle_trend * 0.5:
                                pattern_change = "deceleration"
                                pattern_confidence = 0.8
                                print("Pattern detected: DECELERATION (decreasing positive trend)")
                                print(f"Confidence: {pattern_confidence:.2f}")
                            else:
                                pattern_change = "steady_growth"
                                pattern_confidence = 0.85
                                print("Pattern detected: STEADY GROWTH (consistent positive trend)")
                                print(f"Confidence: {pattern_confidence:.2f}")

                        elif early_trend > 0 and recent_trend_long < 0:
                            pattern_change = "decline"
                            pattern_confidence = 0.8
                            print("Pattern detected: DECLINE (positive to negative trend)")
                            print(f"Confidence: {pattern_confidence:.2f}")

                        elif early_trend < 0 and middle_trend < 0 and recent_trend_long < 0:
                            if recent_trend_long < middle_trend * 1.5:  # More negative
                                pattern_change = "accelerating_decline"
                                pattern_confidence = 0.8
                                print("Pattern detected: ACCELERATING DECLINE (increasing negative trend)")
                                print(f"Confidence: {pattern_confidence:.2f}")
                            else:
                                pattern_change = "steady_decline"
                                pattern_confidence = 0.85
                                print("Pattern detected: STEADY DECLINE (consistent negative trend)")
                                print(f"Confidence: {pattern_confidence:.2f}")

                    # Check for recent pattern changes that might override long-term patterns
                    if "medium_early" in trends and "medium_recent" in trends:
                        medium_early = trends["medium_early"]
                        medium_recent = trends["medium_recent"]

                        # Detect recent reversals
                        if medium_early < 0 and medium_recent > 1000:  # Strong recent upturn
                            pattern_change = "recent_upturn"
                            pattern_confidence = 0.9
                            print("Pattern detected: RECENT UPTURN (negative to strong positive)")
                            print(f"Confidence: {pattern_confidence:.2f}")

                        elif medium_early > 1000 and medium_recent < 0:  # Strong recent downturn
                            pattern_change = "recent_downturn"
                            pattern_confidence = 0.9
                            print("Pattern detected: RECENT DOWNTURN (positive to negative)")
                            print(f"Confidence: {pattern_confidence:.2f}")

                    # Check for very recent changes that might override medium-term patterns
                    if "last_6m" in trends:
                        last_6m = trends["last_6m"]

                        # Detect very recent changes
                        if pattern_change in ["decline", "steady_decline", "accelerating_decline"] and last_6m > 1000:
                            pattern_change = "reversal_up"
                            pattern_confidence = 0.95
                            print("Pattern detected: REVERSAL UP (decline to recent growth)")
                            print(f"Confidence: {pattern_confidence:.2f}")

                        elif pattern_change in ["steady_growth", "acceleration"] and last_6m < -1000:
                            pattern_change = "reversal_down"
                            pattern_confidence = 0.95
                            print("Pattern detected: REVERSAL DOWN (growth to recent decline)")
                            print(f"Confidence: {pattern_confidence:.2f}")

                    # Check for stability patterns
                    if abs(recent_trend) < 100:  # Very small recent trend
                        pattern_change = "stability"
                        pattern_confidence = 0.9
                        print("Pattern detected: STABILITY (minimal trend)")
                        print(f"Confidence: {pattern_confidence:.2f}")

                # If we don't have enough data for multi-scale analysis
                else:
                    # Simple pattern detection based on overall and recent trends
                    overall_trend = trends.get("overall", 0)

                    if overall_trend > 1000:
                        pattern_change = "strong_growth"
                        pattern_confidence = 0.7
                        print("Pattern detected: STRONG GROWTH")
                        print(f"Confidence: {pattern_confidence:.2f}")
                    elif overall_trend > 100:
                        pattern_change = "moderate_growth"
                        pattern_confidence = 0.7
                        print("Pattern detected: MODERATE GROWTH")
                        print(f"Confidence: {pattern_confidence:.2f}")
                    elif overall_trend > 0:
                        pattern_change = "slow_growth"
                        pattern_confidence = 0.7
                        print("Pattern detected: SLOW GROWTH")
                        print(f"Confidence: {pattern_confidence:.2f}")
                    elif overall_trend > -100:
                        pattern_change = "stability"
                        pattern_confidence = 0.7
                        print("Pattern detected: STABILITY")
                        print(f"Confidence: {pattern_confidence:.2f}")
                    else:
                        pattern_change = "decline"
                        pattern_confidence = 0.7
                        print("Pattern detected: DECLINE")
                        print(f"Confidence: {pattern_confidence:.2f}")

                # Enhanced rate of change analysis
                print("\nAnalyzing rate of change...")

                # Use the most recent trend for rate of change analysis
                rate_trend = trends.get("last_6m", trends.get("recent", very_recent_trend))

                # Calculate normalized rate (relative to data magnitude)
                if len(hist_data) > 0 and hist_data[self.target_column].mean() > 0:
                    mean_value = hist_data[self.target_column].mean()
                    normalized_rate = abs(rate_trend) / mean_value
                    print(f"Normalized rate of change: {normalized_rate:.4f} (relative to mean value)")

                    # Categorize based on normalized rate
                    if normalized_rate < 0.01:  # Less than 1% change per month
                        rate_category = "stable"
                        rate_confidence = 0.9
                        print("Rate of change: STABLE (minimal change)")
                    elif normalized_rate < 0.05:  # 1-5% change per month
                        rate_category = "slow"
                        rate_confidence = 0.85
                        print("Rate of change: SLOW (moderate change)")
                    elif normalized_rate < 0.15:  # 5-15% change per month
                        rate_category = "moderate"
                        rate_confidence = 0.8
                        print("Rate of change: MODERATE (significant change)")
                    else:  # More than 15% change per month
                        rate_category = "fast"
                        rate_confidence = 0.75
                        print("Rate of change: FAST (rapid change)")
                else:
                    # Fallback to absolute values if we can't normalize
                    if abs(rate_trend) < 100:
                        rate_category = "stable"
                        rate_confidence = 0.7
                        print("Rate of change: STABLE (minimal change)")
                    elif abs(rate_trend) < 1000:
                        rate_category = "slow"
                        rate_confidence = 0.7
                        print("Rate of change: SLOW (moderate change)")
                    elif abs(rate_trend) < 3000:
                        rate_category = "moderate"
                        rate_confidence = 0.7
                        print("Rate of change: MODERATE (significant change)")
                    else:
                        rate_category = "fast"
                        rate_confidence = 0.7
                        print("Rate of change: FAST (rapid change)")

                # Enhanced volatility analysis
                print("\nAnalyzing volatility patterns...")

                # Calculate multiple volatility metrics
                if len(hist_data) >= 6:
                    # Recent volatility (last 6 months or all data if less)
                    recent_data = hist_data.iloc[-min(6, len(hist_data)):]
                    recent_volatility = recent_data[self.target_column].std() / recent_data[self.target_column].mean() if recent_data[self.target_column].mean() > 0 else 0

                    # Calculate coefficient of variation at different time scales
                    if len(hist_data) >= 12:
                        annual_data = hist_data.iloc[-12:]
                        annual_volatility = annual_data[self.target_column].std() / annual_data[self.target_column].mean() if annual_data[self.target_column].mean() > 0 else 0
                        print(f"Annual volatility (CV): {annual_volatility:.4f}")
                    else:
                        annual_volatility = recent_volatility

                    # Calculate month-to-month changes
                    monthly_changes = hist_data[self.target_column].pct_change().dropna()
                    if len(monthly_changes) > 0:
                        avg_monthly_change = monthly_changes.abs().mean()
                        print(f"Average monthly change: {avg_monthly_change:.4f} (proportion)")
                    else:
                        avg_monthly_change = 0

                    # Determine volatility category based on multiple metrics
                    print(f"Recent volatility (CV): {recent_volatility:.4f}")

                    # Weighted volatility score
                    volatility_score = (0.5 * recent_volatility + 0.3 * annual_volatility + 0.2 * avg_monthly_change)
                    print(f"Weighted volatility score: {volatility_score:.4f}")

                    if volatility_score < 0.1:
                        volatility_category = "low"
                        volatility_confidence = 0.9
                        print("Volatility: LOW (consistent values)")
                    elif volatility_score < 0.25:
                        volatility_category = "medium"
                        volatility_confidence = 0.85
                        print("Volatility: MEDIUM (moderate fluctuations)")
                    else:
                        volatility_category = "high"
                        volatility_confidence = 0.8
                        print("Volatility: HIGH (large fluctuations)")
                else:
                    # Simple volatility for limited data
                    recent_volatility = hist_data[self.target_column].std() / hist_data[self.target_column].mean() if hist_data[self.target_column].mean() > 0 else 0
                    print(f"Overall volatility: {recent_volatility:.4f}")

                    if recent_volatility < 0.15:
                        volatility_category = "low"
                        volatility_confidence = 0.7
                        print("Volatility: LOW")
                    elif recent_volatility < 0.3:
                        volatility_category = "medium"
                        volatility_confidence = 0.7
                        print("Volatility: MEDIUM")
                    else:
                        volatility_category = "high"
                        volatility_confidence = 0.7
                        print("Volatility: HIGH")

                    # Simple pattern detection
                    if recent_trend > 1000:
                        pattern_change = "strong_growth"
                        print("Pattern detected: STRONG GROWTH")
                    elif recent_trend > 100:
                        pattern_change = "moderate_growth"
                        print("Pattern detected: MODERATE GROWTH")
                    elif recent_trend > 0:
                        pattern_change = "slow_growth"
                        print("Pattern detected: SLOW GROWTH")
                    elif recent_trend > -100:
                        pattern_change = "stability"
                        print("Pattern detected: STABILITY")
                    else:
                        pattern_change = "decline"
                        print("Pattern detected: DECLINE")

                    # Simple rate categorization
                    if abs(recent_trend) < 100:
                        rate_category = "stable"
                        print("Rate of change: STABLE")
                    elif abs(recent_trend) < 1000:
                        rate_category = "slow"
                        print("Rate of change: SLOW")
                    else:
                        rate_category = "fast"
                        print("Rate of change: FAST")

                    # Calculate volatility
                    recent_volatility = hist_data[self.target_column].std() / hist_data[self.target_column].mean()
                    if recent_volatility < 0.2:
                        volatility_category = "low"
                        print("Volatility: LOW")
                    else:
                        volatility_category = "high"
                        print("Volatility: HIGH")

                # Adaptive trend determination based on comprehensive pattern analysis
                print("\nDetermining appropriate trend based on pattern analysis...")

                # Get the most relevant trend based on pattern confidence
                if 'pattern_change' in locals() and 'pattern_confidence' in locals():
                    # Use confidence-weighted trend selection
                    print(f"Pattern: {pattern_change} (confidence: {pattern_confidence:.2f})")

                    # Select base trend based on pattern type
                    if pattern_change in ['acceleration', 'strong_growth', 'recent_upturn', 'reversal_up']:
                        # For strong upward patterns, use recent trend with higher cap
                        base_trend = min(very_recent_trend, last_historical_value * 0.2)  # Cap at 20% of last value per month
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # Apply confidence weighting
                        confidence_factor = 0.5 + (pattern_confidence * 0.5)  # Between 0.5 and 1.0
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    elif pattern_change in ['steady_growth', 'moderate_growth']:
                        # For moderate growth, use a more conservative trend
                        base_trend = min(very_recent_trend, last_historical_value * 0.1)  # Cap at 10% of last value per month
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # Apply confidence weighting
                        confidence_factor = 0.5 + (pattern_confidence * 0.5)  # Between 0.5 and 1.0
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    elif pattern_change in ['slow_growth', 'recovery']:
                        # For slow growth, use a conservative trend
                        base_trend = min(very_recent_trend, last_historical_value * 0.05)  # Cap at 5% of last value per month
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # Apply confidence weighting
                        confidence_factor = 0.5 + (pattern_confidence * 0.5)  # Between 0.5 and 1.0
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    elif pattern_change in ['deceleration', 'reversal_down']:
                        # For decelerating growth, use a dampened trend
                        if very_recent_trend > 0:
                            base_trend = very_recent_trend * 0.5  # 50% of recent trend if positive
                        else:
                            base_trend = last_historical_value * 0.01  # Small positive trend if recent is negative
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # Apply confidence weighting
                        confidence_factor = 0.5 + (pattern_confidence * 0.5)  # Between 0.5 and 1.0
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    elif pattern_change == 'stability':
                        # For stability, use a very small trend based on last value
                        base_trend = last_historical_value * 0.005  # 0.5% monthly growth
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # Apply confidence weighting (higher confidence means closer to zero)
                        confidence_factor = 1.0 - (pattern_confidence * 0.5)  # Between 0.5 and 1.0
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    elif pattern_change in ['decline', 'steady_decline', 'recent_downturn']:
                        # For decline, allow small negative trend but prevent steep drops
                        base_trend = max(very_recent_trend, -last_historical_value * 0.03)  # Max 3% monthly decline
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # For decline patterns, higher confidence means more negative trend
                        confidence_factor = 0.5 + (pattern_confidence * 0.5)  # Between 0.5 and 1.0
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    elif pattern_change == 'accelerating_decline':
                        # For accelerating decline, allow more negative trend
                        base_trend = max(very_recent_trend, -last_historical_value * 0.05)  # Max 5% monthly decline
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # Apply confidence weighting
                        confidence_factor = 0.5 + (pattern_confidence * 0.5)  # Between 0.5 and 1.0
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    else:  # unknown or other patterns
                        # For unknown patterns, use a conservative approach
                        if very_recent_trend > 0:
                            base_trend = min(very_recent_trend, last_historical_value * 0.05)  # Cap at 5% growth
                        else:
                            base_trend = max(very_recent_trend, -last_historical_value * 0.02)  # Cap at 2% decline
                        print(f"Base trend for {pattern_change}: {base_trend:.2f} units/month")

                        # Lower confidence for unknown patterns
                        confidence_factor = 0.5  # Fixed at 0.5 for unknown patterns
                        effective_trend = base_trend * confidence_factor
                        print(f"Confidence-adjusted trend: {effective_trend:.2f} units/month (factor: {confidence_factor:.2f})")

                    # Adjust trend based on rate of change and volatility if available
                    if 'rate_category' in locals() and 'volatility_category' in locals():
                        # Apply rate-based adjustment
                        if rate_category == 'fast' and effective_trend > 0:
                            rate_factor = 1.2  # Boost positive trend for fast rate of change
                        elif rate_category == 'fast' and effective_trend < 0:
                            rate_factor = 1.1  # Slightly boost negative trend for fast rate of change
                        elif rate_category == 'stable':
                            rate_factor = 0.8  # Dampen trend for stable rate of change
                        else:  # moderate or slow
                            rate_factor = 1.0  # No adjustment

                        # Apply volatility-based adjustment
                        if volatility_category == 'high':
                            vol_factor = 0.8  # Dampen trend for high volatility
                        elif volatility_category == 'low':
                            vol_factor = 1.1  # Slightly boost trend for low volatility
                        else:  # medium
                            vol_factor = 1.0  # No adjustment

                        # Apply combined adjustment
                        adjusted_trend = effective_trend * rate_factor * vol_factor
                        print(f"Rate adjustment factor: {rate_factor:.2f}")
                        print(f"Volatility adjustment factor: {vol_factor:.2f}")
                        print(f"Final adjusted trend: {adjusted_trend:.2f} units/month")
                        effective_trend = adjusted_trend

                else:  # If pattern analysis wasn't performed
                    # Fall back to simple logic
                    if very_recent_trend > 0:
                        # Cap the trend to avoid explosive growth
                        effective_trend = min(very_recent_trend, last_historical_value * 0.1)  # Cap at 10% of last value
                    else:
                        # If recent trend is negative or flat, use a small positive trend
                        effective_trend = max(very_recent_trend, last_historical_value * 0.005)  # At least 0.5% growth
                    print(f"Using fallback trend: {effective_trend:.2f} units/month")

                print(f"Base trend: {effective_trend:.2f} units per month")

                # Get the model's prediction for the first future point
                first_prediction = results_df.loc[future_indices[0], 'prediction']

                # Calculate a reasonable starting value (blend of last historical and model's first prediction)
                if first_prediction < last_historical_value * 0.9:
                    # If model predicts a big drop, use 95% of last historical value
                    starting_value = last_historical_value * 0.95
                    print(f"Using 95% of last historical value as starting point: {starting_value:.2f}")
                else:
                    # Otherwise, use a blend of model prediction and last historical value
                    starting_value = 0.7 * last_historical_value + 0.3 * first_prediction
                    print(f"Using blended starting value: {starting_value:.2f}")

                # Apply corrections to all future predictions
                for i, idx in enumerate(future_indices):
                    # Get the month for this prediction for seasonal adjustment
                    pred_month = pd.to_datetime(results_df.loc[idx, self.date_column]).month
                    seasonal_factor = monthly_factors.get(pred_month, 1.0)

                    # Get the original model prediction
                    original_prediction = results_df.loc[idx, 'prediction']

                    # Calculate pattern-preserving prediction
                    if i == 0:
                        # First month uses the starting value with seasonal adjustment
                        pattern_prediction = starting_value * seasonal_factor
                    else:
                        # Later months build on previous prediction with trend and seasonal adjustment
                        prev_idx = future_indices[i-1]
                        prev_prediction = results_df.loc[prev_idx, 'prediction']

                        # Apply trend with seasonal adjustment and pattern-based modifications

                        # Determine appropriate dampening based on pattern and volatility
                        if 'pattern_change' in locals() and 'volatility_category' in locals():
                            # For acceleration patterns, use less dampening (allow faster growth)
                            if pattern_change in ['acceleration', 'strong_growth']:
                                # High volatility needs more dampening to prevent instability
                                if volatility_category == 'high':
                                    dampening = max(0.8, 1.0 - (i * 0.015))  # Slower reduction
                                else:
                                    dampening = max(0.85, 1.0 - (i * 0.01))  # Very slow reduction

                            # For deceleration patterns, use more dampening (slow down faster)
                            elif pattern_change in ['deceleration', 'decline']:
                                dampening = max(0.6, 1.0 - (i * 0.03))  # Faster reduction

                            # For stability patterns, use moderate dampening
                            elif pattern_change in ['stability', 'slow_growth']:
                                dampening = max(0.7, 1.0 - (i * 0.02))  # Moderate reduction

                            # For all other patterns, use standard dampening
                            else:
                                dampening = max(0.7, 1.0 - (i * 0.02))  # Standard reduction
                        else:
                            # Default dampening if pattern analysis wasn't performed
                            dampening = max(0.7, 1.0 - (i * 0.02))  # Standard reduction

                        # Calculate the seasonally-adjusted trend prediction with enhanced pattern respect
                        # First, get the base trend prediction
                        base_trend_prediction = prev_prediction + (effective_trend * dampening)

                        # Apply seasonal adjustment relative to previous month
                        prev_month = pd.to_datetime(results_df.loc[prev_idx, self.date_column]).month
                        prev_seasonal = monthly_factors.get(prev_month, 1.0)

                        # Apply seasonal factor more strongly to better respect historical patterns
                        if prev_seasonal > 0:
                            seasonal_adjustment = seasonal_factor / prev_seasonal

                            # Limit extreme seasonal adjustments to prevent unrealistic jumps
                            seasonal_adjustment = max(0.5, min(2.0, seasonal_adjustment))

                            # Apply seasonal adjustment with moderation
                            seasonal_power = 1.2  # Reduced from 1.5 to prevent extreme amplification
                            amplified_seasonal = np.sign(seasonal_adjustment - 1.0) * abs(seasonal_adjustment - 1.0) ** seasonal_power + 1.0

                            # Apply additional constraint to prevent unrealistic jumps
                            max_allowed_multiplier = 1.5  # Maximum 50% increase from previous month
                            if amplified_seasonal > max_allowed_multiplier:
                                print(f"Limiting seasonal adjustment from {amplified_seasonal:.2f} to {max_allowed_multiplier:.2f} to prevent unrealistic jump")
                                amplified_seasonal = max_allowed_multiplier

                            pattern_prediction = base_trend_prediction * amplified_seasonal
                        else:
                            pattern_prediction = base_trend_prediction

                        # Look for repeating patterns in historical data for this specific month
                        if data is not None and len(data) > 0:
                            # Get historical data for this month
                            hist_data_with_month = data.copy()

                            # Ensure date column is datetime type
                            if not pd.api.types.is_datetime64_any_dtype(hist_data_with_month[self.date_column]):
                                hist_data_with_month['date'] = pd.to_datetime(hist_data_with_month[self.date_column])
                            else:
                                hist_data_with_month['date'] = hist_data_with_month[self.date_column]

                            # Extract month
                            hist_data_with_month['month'] = hist_data_with_month['date'].dt.month
                            month_hist_data = hist_data_with_month[hist_data_with_month['month'] == pred_month]

                            # If we have enough data points for this month, analyze the pattern
                            if len(month_hist_data) >= 2:
                                # Calculate the average ratio of this month to the previous month in historical data
                                ratios = []
                                for year in sorted(hist_data_with_month['date'].dt.year.unique()):
                                    # Get this month's data for the current year
                                    this_month_data = hist_data_with_month[(hist_data_with_month['date'].dt.year == year) &
                                                                          (hist_data_with_month['month'] == pred_month)]
                                    # Get previous month's data for the current year
                                    prev_month_data = hist_data_with_month[(hist_data_with_month['date'].dt.year == year) &
                                                                          (hist_data_with_month['month'] == prev_month)]

                                    # If we have data for both months, calculate the ratio
                                    if len(this_month_data) > 0 and len(prev_month_data) > 0:
                                        this_val = this_month_data[self.target_column].values[0]
                                        prev_val = prev_month_data[self.target_column].values[0]

                                        # Only include non-zero values
                                        if prev_val > 0 and this_val > 0:
                                            ratio = this_val / prev_val
                                            ratios.append(ratio)

                                # If we have enough ratios, use them to adjust the prediction
                                if len(ratios) >= 2:
                                    # Calculate the median ratio to avoid outliers
                                    median_ratio = np.median(ratios)

                                    # Also calculate the mean of the most recent 2 years' ratios if available
                                    recent_ratios = ratios[-2:] if len(ratios) >= 2 else ratios
                                    recent_ratio = np.mean(recent_ratios)

                                    # Weight the recent ratio more heavily (70% recent, 30% median)
                                    weighted_ratio = 0.7 * recent_ratio + 0.3 * median_ratio
                                    print(f"Month {pred_month} ratios - Median: {median_ratio:.2f}, Recent: {recent_ratio:.2f}, Weighted: {weighted_ratio:.2f}")

                                    # Limit extreme ratios to prevent unrealistic jumps, but with wider bounds
                                    # Allow more variation but still prevent extreme values
                                    weighted_ratio = max(0.3, min(3.0, weighted_ratio))

                                    # Blend the seasonal prediction with the historical pattern-based prediction
                                    historical_pattern_prediction = prev_prediction * weighted_ratio

                                    # Apply additional constraint to prevent unrealistic jumps
                                    # Allow larger jumps for months that historically have high variation
                                    month_variation = np.std(ratios) / np.mean(ratios) if np.mean(ratios) > 0 else 0
                                    max_multiplier = 1.5 + (month_variation * 2)  # Base 1.5x plus adjustment for variation
                                    max_allowed = prev_prediction * max_multiplier

                                    if historical_pattern_prediction > max_allowed:
                                        print(f"Limiting historical pattern prediction from {historical_pattern_prediction:.2f} to {max_allowed:.2f} (multiplier: {max_multiplier:.2f})")
                                        historical_pattern_prediction = max_allowed

                                    # Use a weighted blend with more emphasis on historical patterns
                                    # Increased from 30% to 50% to better capture month-to-month patterns
                                    pattern_prediction = 0.5 * pattern_prediction + 0.5 * historical_pattern_prediction
                                    print(f"Applied historical month-to-month pattern for month {pred_month} (weighted ratio: {weighted_ratio:.2f})")

                    # Blend original model prediction with pattern-preserving prediction
                    # Adjust blending based on detected patterns and volatility

                    if 'pattern_change' in locals() and 'volatility_category' in locals():
                        # For highly volatile data, trust the pattern-based prediction more
                        if volatility_category == 'high':
                            # High volatility needs more pattern guidance
                            if i < 3:
                                blend_factor = 0.99  # 99% pattern-based for first 3 months
                            elif i < 6:
                                blend_factor = 0.98  # 98% pattern-based for months 4-6
                            elif i < 12:
                                blend_factor = 0.95  # 95% pattern-based for months 7-12
                            else:
                                blend_factor = 0.90  # 90% pattern-based for later months

                        # For stable patterns, we can trust the model more in later months
                        elif pattern_change in ['stability', 'steady_growth', 'slow_growth']:
                            if i < 3:
                                blend_factor = 0.98  # 98% pattern-based for first 3 months
                            elif i < 6:
                                blend_factor = 0.95  # 95% pattern-based for months 4-6
                            elif i < 12:
                                blend_factor = 0.9   # 90% pattern-based for months 7-12
                            else:
                                blend_factor = 0.85  # 85% pattern-based for later months

                        # For changing patterns, rely more on pattern-based prediction
                        elif pattern_change in ['acceleration', 'deceleration', 'recovery', 'decline']:
                            if i < 3:
                                blend_factor = 0.99  # 99% pattern-based for first 3 months
                            elif i < 6:
                                blend_factor = 0.98  # 98% pattern-based for months 4-6
                            elif i < 12:
                                blend_factor = 0.95  # 95% pattern-based for months 7-12
                            else:
                                blend_factor = 0.9   # 90% pattern-based for later months

                        # Default blending for other patterns
                        else:
                            if i < 3:
                                blend_factor = 0.97  # 97% pattern-based for first 3 months
                            elif i < 6:
                                blend_factor = 0.95  # 95% pattern-based for months 4-6
                            elif i < 12:
                                blend_factor = 0.9   # 90% pattern-based for months 7-12
                            else:
                                blend_factor = 0.85  # 85% pattern-based for later months

                    # Default blending if pattern analysis wasn't performed
                    else:
                        if i < 3:
                            blend_factor = 0.97  # 97% pattern-based for first 3 months
                        elif i < 6:
                            blend_factor = 0.95  # 95% pattern-based for months 4-6
                        elif i < 12:
                            blend_factor = 0.9   # 90% pattern-based for months 7-12
                        else:
                            blend_factor = 0.85  # 85% pattern-based for later months

                    # Calculate blended prediction
                    blended_prediction = (
                        (1 - blend_factor) * original_prediction +
                        blend_factor * pattern_prediction
                    )

                    # Apply floor for early predictions (never drop below 90% of last historical value in first 6 months)
                    if i < 6 and blended_prediction < last_historical_value * 0.9:
                        blended_prediction = last_historical_value * 0.9

                    # Apply ceiling to prevent unrealistic predictions (never exceed 5x the maximum historical value)
                    if data is not None and len(data) > 0:
                        max_historical = data[self.target_column].max()
                        if max_historical > 0:
                            max_allowed = max_historical * 5.0  # Maximum 5x the highest historical value
                            if blended_prediction > max_allowed:
                                print(f"Limiting prediction from {blended_prediction:.2f} to {max_allowed:.2f} (5x max historical value)")
                                blended_prediction = max_allowed

                    # For upward trends, apply a gentle correction to prevent excessive downward predictions
                    # This is more balanced than forcing predictions to always increase
                    if strong_upward_trend and i > 0 and i < 24:  # Extended to first two years of predictions
                        prev_idx = future_indices[i-1]
                        prev_prediction = results_df.loc[prev_idx, 'prediction']

                        # If current prediction is significantly lower than previous (more than 5% drop)
                        # and we're in an upward trend, apply a gentle correction
                        if blended_prediction < prev_prediction * 0.95:
                            # Limit the drop to 5% maximum
                            blended_prediction = max(blended_prediction, prev_prediction * 0.95)
                            print(f"Applied gentle correction to limit drop at month {i+1}")

                    # Special handling for products with holes in recent data
                    # If we have the has_forecast column, check for patterns of holes
                    if 'has_forecast' in pred_feature_df.columns and i > 0:
                        # Get the month of this prediction
                        # Ensure date is datetime type
                        date_val = results_df.loc[idx, self.date_column]
                        if not isinstance(date_val, pd.Timestamp):
                            date_val = pd.to_datetime(date_val)
                        pred_month = date_val.month

                        # Check if this month typically has no forecast in historical data
                        if data is not None and len(data) > 0:
                            data_with_dates = data.copy()

                            # Ensure date column is datetime type
                            if not pd.api.types.is_datetime64_any_dtype(data_with_dates[self.date_column]):
                                data_with_dates['date'] = pd.to_datetime(data_with_dates[self.date_column])
                            else:
                                data_with_dates['date'] = data_with_dates[self.date_column]

                            data_with_dates['month'] = data_with_dates['date'].dt.month

                            # Look at the last 2-3 years of data
                            recent_years = 3
                            if len(data_with_dates) >= 12 * recent_years:
                                recent_data = data_with_dates.iloc[-12 * recent_years:].copy()
                            else:
                                recent_data = data_with_dates.copy()

                            # Check if this month typically has no forecasts
                            month_data = recent_data[recent_data['month'] == pred_month]
                            if len(month_data) > 0:
                                # Check for no forecasts using has_forecast column if available
                                if 'has_forecast' in month_data.columns:
                                    no_forecast_ratio = (month_data['has_forecast'] == 0).mean()
                                else:
                                    # Otherwise use zero quantity as indicator of no forecast
                                    no_forecast_ratio = (month_data[self.target_column] == 0).mean()

                                # Calculate how many consecutive years this month had no forecast
                                consecutive_years_no_forecast = 0
                                if len(month_data) > 0:
                                    # Sort by year in descending order (most recent first)
                                    month_data_sorted = month_data.sort_values('date', ascending=False)
                                    for _, row in month_data_sorted.iterrows():
                                        has_forecast = row['has_forecast'] if 'has_forecast' in month_data.columns else (row[self.target_column] > 0)
                                        if has_forecast:
                                            break
                                        consecutive_years_no_forecast += 1

                                # Calculate additional statistics for better zero detection
                                month_mean = month_data[self.target_column].mean() if len(month_data) > 0 else 0
                                month_median = month_data[self.target_column].median() if len(month_data) > 0 else 0
                                overall_mean = recent_data[self.target_column].mean() if len(recent_data) > 0 else 1  # Avoid division by zero

                                # Enhanced zero detection logic
                                # If this month typically has no forecasts (>15% of the time), adjust prediction
                                # Lowered threshold from 20% to 15% to be even more sensitive
                                if (no_forecast_ratio > 0.15 or
                                    consecutive_years_no_forecast >= 1 or
                                    (month_mean < overall_mean * 0.1 and month_median == 0)):
                                    print(f"Month {pred_month} typically has no forecast ({no_forecast_ratio:.1%} of the time)")
                                    print(f"Consecutive years with no forecast for month {pred_month}: {consecutive_years_no_forecast}")
                                    print(f"Month mean: {month_mean:.2f}, Overall mean: {overall_mean:.2f}, Ratio: {(month_mean/overall_mean):.2f}")

                                    # More aggressive zero detection
                                    # If we have consistent pattern of zeros or very low values
                                    if consecutive_years_no_forecast >= 1 or month_median == 0 or no_forecast_ratio > 0.3:
                                        blended_prediction = 0
                                        print(f"Set prediction to zero for month {pred_month} due to strong pattern of no forecasts")
                                    # If prediction is high, reduce it significantly
                                    elif blended_prediction > last_historical_value * 0.15:  # Lowered threshold from 0.2 to 0.15
                                        blended_prediction *= 0.15  # Reduce by 85% (more aggressive than before)
                                        print(f"Reduced prediction for month {pred_month} due to historical pattern of no forecasts")
                                    # For very low predictions, set to exactly zero to indicate no forecast
                                    elif blended_prediction < last_historical_value * 0.3:  # Keep threshold at 0.3
                                        blended_prediction = 0
                                        print(f"Set prediction to zero for month {pred_month} due to strong pattern of no forecasts")

                                # Detect pattern of non-zero values after zero months
                                if i > 0:  # Not the first prediction month
                                    prev_idx = future_indices[i-1]
                                    prev_month = pd.to_datetime(results_df.loc[prev_idx, self.date_column]).month
                                    prev_prediction = results_df.loc[prev_idx, 'prediction']

                                    # If previous month was predicted as zero
                                    if prev_prediction == 0:
                                        # Check historical pattern of this month after previous month=0
                                        non_zero_after_zero = []

                                        for year in sorted(recent_data['date'].dt.year.unique()):
                                            prev_month_data = recent_data[(recent_data['date'].dt.year == year) &
                                                                         (recent_data['date'].dt.month == prev_month)]
                                            this_month_data = recent_data[(recent_data['date'].dt.year == year) &
                                                                         (recent_data['date'].dt.month == pred_month)]

                                            if len(prev_month_data) > 0 and len(this_month_data) > 0:
                                                prev_val = prev_month_data[self.target_column].values[0]
                                                this_val = this_month_data[self.target_column].values[0]

                                                # If previous month was zero and this month was non-zero
                                                if prev_val == 0 and this_val > 0:
                                                    non_zero_after_zero.append(this_val)

                                        # If we have a pattern of non-zero values after zero
                                        if len(non_zero_after_zero) >= 1:
                                            median_val = np.median(non_zero_after_zero)
                                            if median_val > 0:
                                                print(f"Detected pattern of non-zero values for month {pred_month} after zero in month {prev_month}")
                                                print(f"Historical values after zero: {non_zero_after_zero}")
                                                print(f"Median value: {median_val}")

                                                # If our current prediction is zero but history suggests non-zero
                                                if blended_prediction == 0 and median_val > 0:
                                                    blended_prediction = median_val
                                                    print(f"Adjusted prediction for month {pred_month} to {median_val} based on historical pattern")
                                # which should be sufficient with our improved thresholds

                    # Store the final prediction
                    results_df.loc[idx, 'prediction'] = blended_prediction

        # Round predictions to integers
        results_df['prediction'] = results_df['prediction'].round().astype(int)

        # Ensure predictions are not negative
        results_df['prediction'] = results_df['prediction'].clip(lower=0)

        # Generate uncertainty intervals if requested
        if return_uncertainty:
            # For Random Forest, we can use the prediction intervals from the trees
            lower_quantile, upper_quantile = self.uncertainty_quantiles

            # Get predictions from all trees
            predictions = np.array([tree.predict(X_pred_scaled) for tree in self.model.estimators_])

            # Calculate quantiles
            lower_bound_scaled = np.quantile(predictions, lower_quantile, axis=0)
            upper_bound_scaled = np.quantile(predictions, upper_quantile, axis=0)

            # Inverse transform to get bounds in original scale
            lower_bound = self.scaler_y.inverse_transform(lower_bound_scaled.reshape(-1, 1)).flatten()
            upper_bound = self.scaler_y.inverse_transform(upper_bound_scaled.reshape(-1, 1)).flatten()

            # Add to results dataframe
            results_df['lower_bound'] = lower_bound
            results_df['upper_bound'] = upper_bound

            # Apply post-processing to uncertainty bounds for future predictions
            if data is not None and len(data) > 0:
                last_historical_date = pd.to_datetime(data[self.date_column]).max()
                future_mask = pd.to_datetime(results_df[self.date_column]) > last_historical_date

                if future_mask.any():
                    print("Adjusting uncertainty bounds for future predictions...")

                    # Set uncertainty bounds directly based on prediction value
                    future_indices = future_mask[future_mask].index
                    for i, idx in enumerate(future_indices):
                        # Get the prediction value
                        pred_value = results_df.loc[idx, 'prediction']

                        # For the first 3 months, use narrow uncertainty bounds
                        if i < 3:
                            # 10% below, 15% above
                            results_df.loc[idx, 'lower_bound'] = pred_value * 0.9
                            results_df.loc[idx, 'upper_bound'] = pred_value * 1.15
                        # For months 4-6, use medium uncertainty bounds
                        elif i < 6:
                            # 15% below, 20% above
                            results_df.loc[idx, 'lower_bound'] = pred_value * 0.85
                            results_df.loc[idx, 'upper_bound'] = pred_value * 1.2
                        # For months 7-12, use wider uncertainty bounds
                        elif i < 12:
                            # 20% below, 30% above
                            results_df.loc[idx, 'lower_bound'] = pred_value * 0.8
                            results_df.loc[idx, 'upper_bound'] = pred_value * 1.3
                        # For beyond 12 months, use very wide uncertainty bounds
                        else:
                            # 25% below, 40% above
                            results_df.loc[idx, 'lower_bound'] = pred_value * 0.75
                            results_df.loc[idx, 'upper_bound'] = pred_value * 1.4

            # Round bounds to integers
            results_df['lower_bound'] = results_df['lower_bound'].round().astype(int)
            results_df['upper_bound'] = results_df['upper_bound'].round().astype(int)

            # Ensure lower bound is not negative
            results_df['lower_bound'] = results_df['lower_bound'].clip(lower=0)

        return results_df

    def _preprocess_data(self, data, external_data=None):
        """
        Preprocess the input data.

        Parameters:
        -----------
        data : pandas.DataFrame
            Raw time series data
        external_data : dict of pandas.DataFrame
            Dictionary of external data sources

        Returns:
        --------
        pandas.DataFrame
            Preprocessed data
        """
        # Make a copy of the data to avoid modifying the original
        df = data.copy()

        # Ensure date column is datetime
        df[self.date_column] = pd.to_datetime(df[self.date_column])

        # Sort by date
        df = df.sort_values(by=self.date_column)

        # Create a complete date range (monthly, on the 25th)
        if len(df) > 0:
            start_date = df[self.date_column].min()
            end_date = df[self.date_column].max()

            # Create a complete date range with the 25th of each month
            date_range = pd.date_range(
                start=start_date.replace(day=25),
                end=end_date.replace(day=25),
                freq='MS'  # Month start
            ).map(lambda x: x.replace(day=25))

            # Create a complete dataframe with all dates
            complete_df = pd.DataFrame({self.date_column: date_range})

            # Merge with original data, filling missing quantities with 0
            df = pd.merge(complete_df, df, on=self.date_column, how='left')

            # Add a column to indicate whether a date has an actual value or was added during the merge
            # 1 = original data, 0 = added during merge (no forecast)
            df['has_forecast'] = df[self.target_column].notna().astype(int)

            # Fill missing values with zeros
            df[self.target_column] = df[self.target_column].fillna(0)

        # Handle missing values in other columns
        for col in df.columns:
            if col not in [self.date_column, self.target_column]:
                # For boolean columns, fill with False
                if df[col].dtype == bool:
                    df[col] = df[col].fillna(False)
                # For numeric columns, use forward fill then backward fill
                elif pd.api.types.is_numeric_dtype(df[col]):
                    df[col] = df[col].fillna(method='ffill').fillna(method='bfill')

        # Merge external data if provided
        if external_data is not None:
            # Process each external data source
            for source_name, source_df in external_data.items():
                try:
                    # Check if source_df is a DataFrame and has data
                    if not isinstance(source_df, pd.DataFrame):
                        print(f"Warning: {source_name} is not a DataFrame, skipping")
                        continue

                    if len(source_df) == 0:
                        print(f"Warning: {source_name} has no data, skipping")
                        continue

                    if 'date' not in source_df.columns:
                        print(f"Warning: {source_name} has no date column, skipping")
                        continue

                    # Make a copy and ensure date column is datetime
                    source_df = source_df.copy()
                    source_df['date'] = pd.to_datetime(source_df['date'])
                except Exception as e:
                    print(f"Error processing {source_name}: {str(e)}, skipping")
                    continue

                # Exchange rates
                if 'exchange_rate' in source_name:
                    # Get the closest exchange rate for each date in our data
                    for date in df[self.date_column]:
                        closest_date = source_df['date'][source_df['date'] <= date].max()
                        if pd.notna(closest_date):
                            rates = source_df[source_df['date'] == closest_date]
                            if not rates.empty:
                                # Create a column with the source name
                                col_name = source_name.replace('_exchange_rate', '_rate')
                                df.loc[df[self.date_column] == date, col_name] = rates['exchange_rate'].values[0]

                # PMI data
                elif 'pmi' in source_name:
                    # Get the closest PMI value for each date in our data
                    for date in df[self.date_column]:
                        closest_date = source_df['date'][source_df['date'] <= date].max()
                        if pd.notna(closest_date):
                            pmi_data = source_df[source_df['date'] == closest_date]
                            if not pmi_data.empty:
                                df.loc[df[self.date_column] == date, f'{source_name}'] = pmi_data['pmi'].values[0]

                # Industrial production data
                elif 'industrial_production' in source_name:
                    # **FIX 2: Enhanced feature engineering for industrial production**
                    # Create a temporary dataframe with the source data
                    temp_df = source_df.copy()
                    temp_df = temp_df.rename(columns={'date': self.date_column})

                    # Create a feature name prefix
                    prefix = f'{source_name}'

                    # Merge the data with our main dataframe using merge_asof for efficiency
                    df = pd.merge_asof(df.sort_values(self.date_column),
                                      temp_df.sort_values(self.date_column),
                                      on=self.date_column,
                                      direction='backward',
                                      suffixes=('', '_temp'))

                    # Rename the column
                    df = df.rename(columns={'industrial_production': prefix})

                    # Add lag features (3, 6, 12 months)
                    for lag in [3, 6, 12]:
                        temp_df_lagged = temp_df.copy()
                        temp_df_lagged[self.date_column] = temp_df_lagged[self.date_column] + pd.DateOffset(months=lag)
                        temp_df_lagged = temp_df_lagged.rename(columns={'industrial_production': f'{prefix}_lag{lag}m'})

                        df = pd.merge_asof(df.sort_values(self.date_column),
                                          temp_df_lagged.sort_values(self.date_column),
                                          on=self.date_column,
                                          direction='backward',
                                          suffixes=('', '_temp'))

                    # Add trend indicators (3, 6, 12 month changes)
                    # Remove duplicates before calculations to avoid reindex errors
                    df = df.loc[:, ~df.columns.duplicated()]

                    if f'{prefix}_lag3m' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend3m'] = df[prefix] - df[f'{prefix}_lag3m']
                            df[f'{prefix}_pct3m'] = (df[prefix] / (df[f'{prefix}_lag3m'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag3m: {str(e)}")
                    if f'{prefix}_lag6m' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend6m'] = df[prefix] - df[f'{prefix}_lag6m']
                            df[f'{prefix}_pct6m'] = (df[prefix] / (df[f'{prefix}_lag6m'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag6m: {str(e)}")
                    if f'{prefix}_lag12m' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend12m'] = df[prefix] - df[f'{prefix}_lag12m']
                            # Add year-over-year percent change
                            df[f'{prefix}_yoy_pct'] = (df[prefix] / (df[f'{prefix}_lag12m'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag12m: {str(e)}")

                    # Add rolling averages (3, 6, 12 months)
                    temp_df['rolling_avg_3m'] = temp_df['industrial_production'].rolling(window=3, min_periods=1).mean()
                    temp_df['rolling_avg_6m'] = temp_df['industrial_production'].rolling(window=6, min_periods=1).mean()
                    temp_df['rolling_avg_12m'] = temp_df['industrial_production'].rolling(window=12, min_periods=1).mean()

                    for window in ['rolling_avg_3m', 'rolling_avg_6m', 'rolling_avg_12m']:
                        temp_df_rolling = temp_df[[self.date_column, window]].copy()
                        temp_df_rolling = temp_df_rolling.rename(columns={window: f'{prefix}_{window}'})

                        df = pd.merge_asof(df.sort_values(self.date_column),
                                          temp_df_rolling.sort_values(self.date_column),
                                          on=self.date_column,
                                          direction='backward',
                                          suffixes=('', '_temp'))

                    # Add seasonal adjustment (deviation from 12-month average)
                    if f'{prefix}_rolling_avg_12m' in df.columns:
                        df[f'{prefix}_seasonal'] = (df[prefix] / df[f'{prefix}_rolling_avg_12m'] - 1) * 100

                    # Add momentum indicators
                    if f'{prefix}_rolling_avg_3m' in df.columns and f'{prefix}_rolling_avg_6m' in df.columns:
                        df[f'{prefix}_momentum'] = df[f'{prefix}_rolling_avg_3m'] / df[f'{prefix}_rolling_avg_6m'] - 1

                # Automotive production data
                elif 'automotive_production' in source_name:
                    # **FIX 2: Enhanced feature engineering for automotive production**
                    # Create a temporary dataframe with the source data
                    temp_df = source_df.copy()
                    temp_df = temp_df.rename(columns={'date': self.date_column})

                    # Create a feature name prefix
                    prefix = f'{source_name}'

                    # Merge the data with our main dataframe using merge_asof for efficiency
                    df = pd.merge_asof(df.sort_values(self.date_column),
                                      temp_df.sort_values(self.date_column),
                                      on=self.date_column,
                                      direction='backward',
                                      suffixes=('', '_temp'))

                    # Rename the column
                    df = df.rename(columns={'automotive_production': prefix})

                    # Add lag features (3, 6, 12 months)
                    for lag in [3, 6, 12]:
                        temp_df_lagged = temp_df.copy()
                        temp_df_lagged[self.date_column] = temp_df_lagged[self.date_column] + pd.DateOffset(months=lag)
                        temp_df_lagged = temp_df_lagged.rename(columns={'automotive_production': f'{prefix}_lag{lag}m'})

                        df = pd.merge_asof(df.sort_values(self.date_column),
                                          temp_df_lagged.sort_values(self.date_column),
                                          on=self.date_column,
                                          direction='backward',
                                          suffixes=('', '_temp'))

                    # Add trend indicators (3, 6, 12 month changes)
                    # Remove duplicates before calculations to avoid reindex errors
                    df = df.loc[:, ~df.columns.duplicated()]

                    if f'{prefix}_lag3m' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend3m'] = df[prefix] - df[f'{prefix}_lag3m']
                            df[f'{prefix}_pct3m'] = (df[prefix] / (df[f'{prefix}_lag3m'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag3m: {str(e)}")
                    if f'{prefix}_lag6m' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend6m'] = df[prefix] - df[f'{prefix}_lag6m']
                            df[f'{prefix}_pct6m'] = (df[prefix] / (df[f'{prefix}_lag6m'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag6m: {str(e)}")
                    if f'{prefix}_lag12m' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend12m'] = df[prefix] - df[f'{prefix}_lag12m']
                            # Add year-over-year percent change
                            df[f'{prefix}_yoy_pct'] = (df[prefix] / (df[f'{prefix}_lag12m'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag12m: {str(e)}")

                    # Add rolling averages (3, 6, 12 months)
                    temp_df['rolling_avg_3m'] = temp_df['automotive_production'].rolling(window=3, min_periods=1).mean()
                    temp_df['rolling_avg_6m'] = temp_df['automotive_production'].rolling(window=6, min_periods=1).mean()
                    temp_df['rolling_avg_12m'] = temp_df['automotive_production'].rolling(window=12, min_periods=1).mean()

                    for window in ['rolling_avg_3m', 'rolling_avg_6m', 'rolling_avg_12m']:
                        temp_df_rolling = temp_df[[self.date_column, window]].copy()
                        temp_df_rolling = temp_df_rolling.rename(columns={window: f'{prefix}_{window}'})

                        df = pd.merge_asof(df.sort_values(self.date_column),
                                          temp_df_rolling.sort_values(self.date_column),
                                          on=self.date_column,
                                          direction='backward',
                                          suffixes=('', '_temp'))

                    # Add seasonal adjustment (deviation from 12-month average)
                    if f'{prefix}_rolling_avg_12m' in df.columns:
                        df[f'{prefix}_seasonal'] = (df[prefix] / df[f'{prefix}_rolling_avg_12m'] - 1) * 100

                    # Add momentum indicators
                    if f'{prefix}_rolling_avg_3m' in df.columns and f'{prefix}_rolling_avg_6m' in df.columns:
                        df[f'{prefix}_momentum'] = df[f'{prefix}_rolling_avg_3m'] / df[f'{prefix}_rolling_avg_6m'] - 1

                # Manufacturing value added data
                elif 'manufacturing' in source_name:
                    # **FIX 2: Enhanced feature engineering for manufacturing data**
                    # Create a temporary dataframe with the source data
                    temp_df = source_df.copy()
                    temp_df = temp_df.rename(columns={'date': self.date_column})

                    # Create a feature name prefix
                    prefix = f'{source_name}'

                    # Remove conflicting columns before merge
                    conflicting_cols = [col for col in temp_df.columns if col in df.columns and col != self.date_column]
                    if conflicting_cols:
                        temp_df = temp_df.drop(columns=conflicting_cols)

                    # Merge the data with our main dataframe using merge_asof for efficiency
                    df = pd.merge_asof(df.sort_values(self.date_column),
                                      temp_df.sort_values(self.date_column),
                                      on=self.date_column,
                                      direction='backward',
                                      suffixes=('', '_temp'))

                    # Rename the column
                    if 'manufacturing_value_added' in df.columns:
                        df = df.rename(columns={'manufacturing_value_added': prefix})

                    # Add lag features (1, 2, 3 years since this is annual data)
                    for lag in [1, 2, 3]:
                        temp_df_lagged = source_df.copy()
                        temp_df_lagged = temp_df_lagged.rename(columns={'date': self.date_column})
                        temp_df_lagged[self.date_column] = temp_df_lagged[self.date_column] + pd.DateOffset(years=lag)
                        temp_df_lagged = temp_df_lagged.rename(columns={'manufacturing_value_added': f'{prefix}_lag{lag}y'})

                        # Remove conflicting columns before merge
                        conflicting_cols = [col for col in temp_df_lagged.columns if col in df.columns and col != self.date_column]
                        if conflicting_cols:
                            temp_df_lagged = temp_df_lagged.drop(columns=conflicting_cols)

                        df = pd.merge_asof(df.sort_values(self.date_column),
                                          temp_df_lagged.sort_values(self.date_column),
                                          on=self.date_column,
                                          direction='backward',
                                          suffixes=('', '_temp'))

                    # Add trend indicators (1, 2, 3 year changes)
                    # Remove duplicates before calculations to avoid reindex errors
                    df = df.loc[:, ~df.columns.duplicated()]

                    if f'{prefix}_lag1y' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend1y'] = df[prefix] - df[f'{prefix}_lag1y']
                            df[f'{prefix}_pct1y'] = (df[prefix] / (df[f'{prefix}_lag1y'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag1y: {str(e)}")
                    if f'{prefix}_lag2y' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend2y'] = df[prefix] - df[f'{prefix}_lag2y']
                            df[f'{prefix}_pct2y'] = (df[prefix] / (df[f'{prefix}_lag2y'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag2y: {str(e)}")
                    if f'{prefix}_lag3y' in df.columns and prefix in df.columns:
                        try:
                            df[f'{prefix}_trend3y'] = df[prefix] - df[f'{prefix}_lag3y']
                            # Add 3-year percent change
                            df[f'{prefix}_3y_pct'] = (df[prefix] / (df[f'{prefix}_lag3y'] + 1e-6) - 1) * 100
                        except Exception as e:
                            print(f"Warning: Could not calculate trend for {prefix}_lag3y: {str(e)}")

                # Holiday data (Ramadan, Golden Week)
                elif any(holiday in source_name for holiday in ['ramadan', 'golden_week']):
                    # Create holiday flag column
                    holiday_col = f'is_{source_name.split("_")[0].lower()}'
                    if holiday_col not in df.columns:
                        df[holiday_col] = False

                    # Set flag to True for dates in the holiday period
                    for _, row in source_df.iterrows():
                        holiday_date = row['date']
                        matching_dates = df[self.date_column].apply(lambda x:
                            abs((x - holiday_date).days) <= 15)  # Within 15 days
                        df.loc[matching_dates, holiday_col] = True

            # **FIX 2: Add interaction features between external data sources**
            # First, remove any duplicate columns
            df = df.loc[:, ~df.columns.duplicated()]

            # Create interaction features between different countries' data
            jp_industrial = [col for col in df.columns if 'jp_industrial_production' in col and not col.endswith('_lag3m') and not col.endswith('_lag6m') and not col.endswith('_lag12m')]
            id_industrial = [col for col in df.columns if 'id_industrial_production' in col and not col.endswith('_lag3m') and not col.endswith('_lag6m') and not col.endswith('_lag12m')]
            jp_automotive = [col for col in df.columns if 'jp_automotive_production' in col and not col.endswith('_lag3m') and not col.endswith('_lag6m') and not col.endswith('_lag12m')]
            id_automotive = [col for col in df.columns if 'id_automotive_production' in col and not col.endswith('_lag3m') and not col.endswith('_lag6m') and not col.endswith('_lag12m')]

            # Create cross-country industrial production ratio
            if jp_industrial and id_industrial:
                jp_col = jp_industrial[0]
                id_col = id_industrial[0]
                df['jp_id_industrial_ratio'] = df[jp_col] / (df[id_col] + 1e-6)  # Add small value to avoid division by zero
                df['jp_id_industrial_diff'] = df[jp_col] - df[id_col]
                print("Added Japan-Indonesia industrial production interaction features")

            # Create cross-country automotive production ratio
            if jp_automotive and id_automotive:
                jp_col = jp_automotive[0]
                id_col = id_automotive[0]
                df['jp_id_automotive_ratio'] = df[jp_col] / (df[id_col] + 1e-6)
                df['jp_id_automotive_diff'] = df[jp_col] - df[id_col]
                print("Added Japan-Indonesia automotive production interaction features")

            # Create cross-sector interaction features (industrial vs automotive)
            if jp_industrial and jp_automotive:
                jp_ind_col = jp_industrial[0]
                jp_auto_col = jp_automotive[0]
                df['jp_industrial_automotive_ratio'] = df[jp_ind_col] / (df[jp_auto_col] + 1e-6)
                df['jp_industrial_automotive_diff'] = df[jp_ind_col] - df[jp_auto_col]
                print("Added Japan industrial-automotive interaction features")

            if id_industrial and id_automotive:
                id_ind_col = id_industrial[0]
                id_auto_col = id_automotive[0]
                df['id_industrial_automotive_ratio'] = df[id_ind_col] / (df[id_auto_col] + 1e-6)
                df['id_industrial_automotive_diff'] = df[id_ind_col] - df[id_auto_col]
                print("Added Indonesia industrial-automotive interaction features")

            # Create composite economic indicators
            all_industrial = jp_industrial + id_industrial
            all_automotive = jp_automotive + id_automotive

            if all_industrial:
                # Create weighted average of industrial production
                weights = [0.7, 0.3] if len(all_industrial) == 2 else [1.0/len(all_industrial)] * len(all_industrial)
                df['composite_industrial'] = sum(df[col] * weight for col, weight in zip(all_industrial, weights))
                print("Added composite industrial production indicator")

            if all_automotive:
                # Create weighted average of automotive production
                weights = [0.7, 0.3] if len(all_automotive) == 2 else [1.0/len(all_automotive)] * len(all_automotive)
                df['composite_automotive'] = sum(df[col] * weight for col, weight in zip(all_automotive, weights))
                print("Added composite automotive production indicator")

            # Create overall economic health indicator
            if all_industrial and all_automotive:
                df['economic_health_indicator'] = (df['composite_industrial'] + df['composite_automotive']) / 2
                print("Added overall economic health indicator")

            # Log the external features added
            external_cols = [col for col in df.columns if col not in [self.date_column, self.target_column]
                            and col not in ['year', 'month', 'day', 'dayofweek', 'quarter']]
            if external_cols:
                print(f"Added {len(external_cols)} external features: {', '.join(external_cols[:10])}{'...' if len(external_cols) > 10 else ''}")

        # Final cleanup: remove any duplicate columns that might have been created
        df = df.loc[:, ~df.columns.duplicated()]

        # Remove any temporary columns created during merges
        temp_columns = [col for col in df.columns if col.endswith('_temp')]
        if temp_columns:
            df = df.drop(columns=temp_columns)
            print(f"Removed {len(temp_columns)} temporary columns")

        return df

    def _engineer_features(self, data):
        """
        Engineer features from the preprocessed data.

        Parameters:
        -----------
        data : pandas.DataFrame
            Preprocessed time series data

        Returns:
        --------
        pandas.DataFrame
            Data with engineered features
        """
        # Make a copy of the data to avoid modifying the original
        df = data.copy()

        # Extract date components
        df['year'] = df[self.date_column].dt.year
        df['month'] = df[self.date_column].dt.month
        df['quarter'] = df[self.date_column].dt.quarter

        # Create cyclical features for month and quarter
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['quarter_sin'] = np.sin(2 * np.pi * df['quarter'] / 4)
        df['quarter_cos'] = np.cos(2 * np.pi * df['quarter'] / 4)

        # Create time index (months since start)
        min_date = df[self.date_column].min()
        df['time_idx'] = df[self.date_column].apply(lambda x:
            12 * (x.year - min_date.year) + (x.month - min_date.month))

        # Create lag features
        for lag in range(1, self.max_lag + 1):
            df[f'quantity_lag_{lag}'] = df[self.target_column].shift(lag)

        # Create rolling window features if we have enough data
        if len(df) >= self.max_lag + 1:
            # Rolling mean
            for window in [3, 6, 12]:
                if len(df) >= window:
                    df[f'rolling_mean_{window}'] = df[self.target_column].shift(1).rolling(window=window).mean()

            # Rolling standard deviation for volatility
            for window in [3, 6, 12]:
                if len(df) >= window:
                    df[f'rolling_std_{window}'] = df[self.target_column].shift(1).rolling(window=window).std()

            # Year-over-year change
            if len(df) >= 13:  # Need at least 13 months for YoY
                df['yoy_change'] = df[self.target_column] / df[self.target_column].shift(12) - 1

            # Month-over-month change
            df['mom_change'] = df[self.target_column] / df[self.target_column].shift(1) - 1

        # Add enhanced trend features
        df['trend'] = np.arange(len(df))
        df['trend_squared'] = df['trend'] ** 2
        df['trend_cubed'] = df['trend'] ** 3  # Add cubic trend for more flexibility

        # Add log trend for capturing diminishing growth
        df['log_trend'] = np.log1p(df['trend'])

        # Add weighted trend features if trend_weight > 1
        if self.trend_weight > 1:
            df['weighted_trend'] = df['trend'] * self.trend_weight
            df['weighted_trend_squared'] = df['trend_squared'] * self.trend_weight

        # Add enhanced seasonal features
        if self.feature_engineering_level in ['intermediate', 'advanced']:
            # Create month-based seasonal indicators
            for month in range(1, 13):
                df[f'is_month_{month}'] = (df['month'] == month).astype(int)

            # Detect seasonality if we have enough data
            if len(df) >= 24:  # Need at least 2 years for seasonal decomposition
                try:
                    # Fill any remaining NaNs for decomposition
                    temp_series = df[self.target_column].fillna(method='ffill').fillna(method='bfill')

                    # Only perform decomposition if we have sufficient non-zero data
                    if temp_series.sum() > 0 and len(temp_series.dropna()) >= 24:
                        # Perform seasonal decomposition
                        decomposition = seasonal_decompose(temp_series, model='additive', period=12)
                        # Add seasonal component as a feature
                        df['seasonal'] = decomposition.seasonal
                        # Add trend component as a feature
                        df['decomp_trend'] = decomposition.trend

                        # Apply seasonal weighting if specified
                        if self.seasonal_weight > 1:
                            df['weighted_seasonal'] = df['seasonal'] * self.seasonal_weight
                    else:
                        # Create simple seasonal proxy if decomposition data is insufficient
                        month_means = df.groupby('month')[self.target_column].transform('mean')
                        df['seasonal'] = month_means / (month_means.mean() + 1e-6)
                        df['decomp_trend'] = df['trend']  # Use regular trend as fallback

                        # Apply seasonal weighting if specified
                        if self.seasonal_weight > 1:
                            df['weighted_seasonal'] = df['seasonal'] * self.seasonal_weight
                except Exception as e:
                    print(f"Seasonal decomposition failed: {str(e)}")
                    # If decomposition fails, create a simple seasonal proxy
                    month_means = df.groupby('month')[self.target_column].transform('mean')
                    df['seasonal'] = month_means / (month_means.mean() + 1e-6)
                    df['decomp_trend'] = df['trend']  # Use regular trend as fallback

                    # Apply seasonal weighting if specified
                    if self.seasonal_weight > 1:
                        df['weighted_seasonal'] = df['seasonal'] * self.seasonal_weight
            else:
                # For datasets with less than 24 months, create simple seasonal features
                month_means = df.groupby('month')[self.target_column].transform('mean')
                df['seasonal'] = month_means / (month_means.mean() + 1e-6)
                df['decomp_trend'] = df['trend']  # Use regular trend as fallback

                # Apply seasonal weighting if specified
                if self.seasonal_weight > 1:
                    df['weighted_seasonal'] = df['seasonal'] * self.seasonal_weight

            # Add quarter-based seasonal indicators
            for quarter in range(1, 5):
                df[f'is_quarter_{quarter}'] = (df['quarter'] == quarter).astype(int)

        # Add interaction features if we have advanced feature engineering
        if self.feature_engineering_level == 'advanced':
            # Interaction between month and trend
            for month in range(1, 13):
                df[f'month_{month}_trend'] = (df['month'] == month).astype(int) * df['trend']

            # Interaction between holidays and trend
            for holiday in ['is_ramadhan', 'is_golden_week']:
                if holiday in df.columns:
                    # Fill NaN values with False before converting to int
                    df[f'{holiday}_trend'] = df[holiday].fillna(False).astype(int) * df['trend']

        # Fill NaN values and handle infinities
        # For numeric columns, use forward fill then backward fill
        for col in df.columns:
            if col not in [self.date_column, self.target_column]:
                if pd.api.types.is_numeric_dtype(df[col]):
                    # Replace infinities with NaN first
                    df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                    # Then fill NaN values
                    df[col] = df[col].fillna(method='ffill').fillna(method='bfill').fillna(0)
                    # Clip extremely large values to prevent numerical issues
                    df[col] = df[col].clip(-1e9, 1e9)

        # Store feature columns for later use
        self.feature_columns = [col for col in df.columns if col not in [self.date_column, self.target_column]]

        return df

    def _discover_causal_relationships(self, data):
        """
        Discover causal relationships between features.

        Parameters:
        -----------
        data : pandas.DataFrame
            Data with engineered features

        Returns:
        --------
        networkx.DiGraph
            Directed graph representing causal relationships
        """
        # Create a directed graph to represent causal relationships
        G = nx.DiGraph()

        # Add all feature columns as nodes
        for col in self.feature_columns:
            G.add_node(col)

        # Add target as a node
        G.add_node(self.target_column)

        # Prepare data for Granger causality tests
        # We need to ensure the data is stationary for valid tests
        test_data = data.copy()

        # For each potential causal relationship, perform Granger causality test
        causal_pairs = []

        # Test if features Granger-cause the target
        for feature in self.feature_columns:
            # Skip non-numeric features
            if not pd.api.types.is_numeric_dtype(test_data[feature]):
                continue

            # Prepare data for this test
            test_pair = pd.DataFrame({
                'y': test_data[self.target_column],
                'x': test_data[feature]
            }).dropna()

            if len(test_pair) <= self.max_lag + 1:
                continue  # Not enough data for testing

            # Perform Granger causality test
            try:
                test_results = grangercausalitytests(
                    test_pair[['y', 'x']],
                    maxlag=self.max_lag,
                    verbose=False
                )

                # Check if any lag shows significant causality
                significant_causality = False
                best_pvalue = 1.0
                best_lag = 0

                for lag, result in test_results.items():
                    # Get p-value from F-test
                    pvalue = result[0]['ssr_ftest'][1]
                    if pvalue < best_pvalue:
                        best_pvalue = pvalue
                        best_lag = lag

                    if pvalue < self.causality_alpha:
                        significant_causality = True

                if significant_causality:
                    causal_pairs.append((feature, self.target_column, best_pvalue, best_lag))
            except:
                # Skip if test fails
                continue

        # Test if features Granger-cause other features
        if self.feature_engineering_level == 'advanced':
            for feature1 in self.feature_columns:
                for feature2 in self.feature_columns:
                    # Skip self-causation and non-numeric features
                    if feature1 == feature2 or \
                       not pd.api.types.is_numeric_dtype(test_data[feature1]) or \
                       not pd.api.types.is_numeric_dtype(test_data[feature2]):
                        continue

                    # Prepare data for this test
                    test_pair = pd.DataFrame({
                        'y': test_data[feature2],
                        'x': test_data[feature1]
                    }).dropna()

                    if len(test_pair) <= self.max_lag + 1:
                        continue  # Not enough data for testing

                    # Perform Granger causality test
                    try:
                        test_results = grangercausalitytests(
                            test_pair[['y', 'x']],
                            maxlag=self.max_lag,
                            verbose=False
                        )

                        # Check if any lag shows significant causality
                        significant_causality = False
                        best_pvalue = 1.0
                        best_lag = 0

                        for lag, result in test_results.items():
                            # Get p-value from F-test
                            pvalue = result[0]['ssr_ftest'][1]
                            if pvalue < best_pvalue:
                                best_pvalue = pvalue
                                best_lag = lag

                            if pvalue < self.causality_alpha:
                                significant_causality = True

                        if significant_causality:
                            causal_pairs.append((feature1, feature2, best_pvalue, best_lag))
                    except:
                        # Skip if test fails
                        continue

        # Add edges to the graph for causal relationships
        for cause, effect, pvalue, lag in causal_pairs:
            # Edge weight is 1 - p-value (stronger causality = higher weight)
            G.add_edge(cause, effect, weight=1-pvalue, lag=lag)

        return G

    def _create_causal_features(self, data):
        """
        Create features based on discovered causal relationships.

        Parameters:
        -----------
        data : pandas.DataFrame
            Data with engineered features

        Returns:
        --------
        pandas.DataFrame
            Data with causal features
        """
        print("Starting causal feature creation...")
        # If no causal graph exists, return the original data
        if self.causal_graph is None:
            print("No causal graph found, skipping causal feature creation.")
            return data

        # Make a copy of the data to avoid modifying the original
        df = data.copy()

        # Get direct causes of the target
        direct_causes = [node for node in self.causal_graph.predecessors(self.target_column)]
        print(f"Found {len(direct_causes)} direct causes of the target.")

        # Create interaction features between direct causes (simplified)
        if len(direct_causes) > 1 and len(direct_causes) < 10:  # Limit to avoid combinatorial explosion
            for i, cause1 in enumerate(direct_causes[:5]):  # Limit to top 5 causes
                for cause2 in direct_causes[i+1:5]:  # Limit to top 5 causes
                    # Skip if either cause is non-numeric
                    if not pd.api.types.is_numeric_dtype(df[cause1]) or \
                       not pd.api.types.is_numeric_dtype(df[cause2]):
                        continue

                    # Create interaction feature
                    interaction_name = f"interaction_{cause1}_{cause2}"
                    df[interaction_name] = df[cause1] * df[cause2]
            print("Created interaction features.")

        # Create lagged features based on causal relationships (simplified)
        edge_count = 0
        for u, v, attrs in list(self.causal_graph.edges(data=True))[:10]:  # Limit to top 10 edges
            if v == self.target_column and 'lag' in attrs:
                lag = attrs['lag']
                # Create a feature that represents the specific lag that was found to be causal
                lag_feature_name = f"{u}_causal_lag_{lag}"
                df[lag_feature_name] = df[u].shift(lag)
                edge_count += 1
        print(f"Created {edge_count} lagged features.")

        # Create weighted features based on causal strength (simplified)
        edge_count = 0
        for u, v, attrs in list(self.causal_graph.edges(data=True))[:10]:  # Limit to top 10 edges
            if v == self.target_column and 'weight' in attrs:
                weight = attrs['weight']
                # Create a feature weighted by causal strength
                weighted_feature_name = f"{u}_causal_weighted"
                df[weighted_feature_name] = df[u] * weight
                edge_count += 1
        print(f"Created {edge_count} weighted features.")

        # Skip polynomial and indirect path features for simplicity
        print("Skipping polynomial and indirect path features for simplicity.")

        # Fill NaN values and handle infinities
        # For numeric columns, use forward fill then backward fill
        for col in df.columns:
            if col not in [self.date_column, self.target_column]:
                if pd.api.types.is_numeric_dtype(df[col]):
                    # Replace infinities with NaN first
                    df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                    # Then fill NaN values
                    df[col] = df[col].fillna(method='ffill').fillna(method='bfill').fillna(0)
                    # Clip extremely large values to prevent numerical issues
                    df[col] = df[col].clip(-1e9, 1e9)
        print("Filled NaN values and handled infinities in features.")

        # Update feature columns - exclude non-numeric columns
        excluded_columns = [self.date_column, self.target_column, 'customer_name', 'product_name']
        self.feature_columns = []

        for col in df.columns:
            if col not in excluded_columns:
                # Only include numeric columns
                if pd.api.types.is_numeric_dtype(df[col]):
                    self.feature_columns.append(col)
                else:
                    print(f"Excluding non-numeric column: {col}")

        print(f"Final feature count: {len(self.feature_columns)}")

        return df

    def _train_model(self, data):
        """
        Train the predictive model.

        Parameters:
        -----------
        data : pandas.DataFrame
            Data with causal features

        Returns:
        --------
        None
        """
        print("Starting model training...")
        # Make a copy of the data to avoid modifying the original
        df = data.copy()

        # Prepare features and target
        X = df[self.feature_columns].copy()
        y = df[self.target_column]

        # Check if we have the has_forecast column
        has_forecast_column = 'has_forecast' in df.columns
        if has_forecast_column:
            print("Found 'has_forecast' column - will use it to distinguish between zero and missing forecasts")
            # Add has_forecast as a feature if it exists
            X['has_forecast'] = df['has_forecast']
            # Add to feature columns
            if 'has_forecast' not in self.feature_columns:
                self.feature_columns.append('has_forecast')

        print(f"Training with {len(X)} samples and {len(self.feature_columns)} features.")

        # Scale features and target for better model performance
        print("Scaling features and target...")
        X_scaled = self.scaler_X.fit_transform(X)
        y_scaled = self.scaler_y.fit_transform(y.values.reshape(-1, 1)).flatten()

        # Initialize and train the model
        print("Initializing Random Forest model...")
        self.model = RandomForestRegressor(
            n_estimators=self.n_estimators,
            min_samples_leaf=self.min_samples_leaf,
            max_depth=self.max_depth,  # Add max_depth to prevent overfitting
            random_state=self.random_state,
            n_jobs=-1  # Use all available cores
        )

        # Train the model with sample weighting if balance_historical_future is True
        print("Training the model...")
        if self.balance_historical_future and len(X) > 10:
            # Create sample weights that emphasize more recent data and long-term trends
            sample_weights = np.ones(len(X))

            # Special handling for zero values if has_forecast column exists
            if has_forecast_column:
                # Count samples with and without forecasts
                forecast_count = df['has_forecast'].sum()
                no_forecast_count = len(df) - forecast_count
                print(f"Training with {forecast_count} samples with forecasts and {no_forecast_count} samples without forecasts")

                # Give more weight to samples with actual forecasts
                forecast_mask = df['has_forecast'] == 1
                sample_weights[forecast_mask] *= 2.0

                # Give less weight to samples without forecasts (zeros from missing data)
                no_forecast_mask = df['has_forecast'] == 0
                sample_weights[no_forecast_mask] *= 0.5

                print(f"Applied weights: {2.0} for samples with forecasts, {0.5} for samples without forecasts")

            # Calculate time-based weights (more recent data gets much higher weight)
            time_indices = np.arange(len(X))

            # Get dates for determining recency
            # Ensure date column is datetime type
            dates = pd.to_datetime(df[self.date_column])
            last_date = dates.max()

            # Create recency weights with special emphasis on the last year and last 6 months
            recency_weights = np.ones(len(X))

            # Calculate days difference for each date from the last date
            # Create a Series to use the .dt accessor
            date_series = pd.Series(dates)
            days_diff = (last_date - date_series).dt.days.values

            # Last 3 months (approximately 90 days) get 5x weight - INCREASED WEIGHT
            three_month_mask = days_diff <= 90
            if three_month_mask.any():
                print(f"Giving 5x weight to the last 3 months ({three_month_mask.sum()} data points)")
                recency_weights[three_month_mask] = 5.0

            # Last 4-6 months (approximately 90-180 days) get 4x weight - INCREASED WEIGHT
            six_month_mask = (days_diff > 90) & (days_diff <= 180)
            if six_month_mask.any():
                print(f"Giving 4x weight to the 4-6 months before prediction ({six_month_mask.sum()} data points)")
                recency_weights[six_month_mask] = 4.0

            # Last 7-12 months (approximately 180-365 days) get 3x weight - INCREASED WEIGHT
            year_mask = (days_diff > 180) & (days_diff <= 365)
            if year_mask.any():
                print(f"Giving 3x weight to the 7-12 months before prediction ({year_mask.sum()} data points)")
                recency_weights[year_mask] = 3.0

            # Last 13-24 months (approximately 1-2 years) get 2x weight - ADDED NEW WEIGHT TIER
            two_year_mask = (days_diff > 365) & (days_diff <= 730)
            if two_year_mask.any():
                print(f"Giving 2x weight to the 13-24 months before prediction ({two_year_mask.sum()} data points)")
                recency_weights[two_year_mask] = 2.0

            # Also keep the exponential time weighting for overall recency
            # Exponential increase in weights for more recent data
            time_weights = np.exp(3 * time_indices / len(X))  # Exponential increase from 1 to ~20
            # Normalize weights to be between 1 and 5 (reduced from 1-10 to avoid overwhelming other weights)
            time_weights = 1 + 4 * (time_weights - time_weights.min()) / (time_weights.max() - time_weights.min())

            # Combine both weighting approaches
            combined_time_weights = time_weights * recency_weights
            print(f"Time weights range: {combined_time_weights.min():.2f} to {combined_time_weights.max():.2f}")

            # Apply time weights
            sample_weights *= combined_time_weights

            # Emphasize data points that represent significant changes
            if 'mom_change' in X.columns:
                change_values = X['mom_change'].abs().fillna(0).values
                # Normalize change values to [0, 1] range
                if change_values.max() > 0:
                    change_values = change_values / change_values.max()
                # Apply change weights
                sample_weights *= (1 + change_values)

            print(f"Using sample weights ranging from {sample_weights.min():.2f} to {sample_weights.max():.2f}")
            self.model.fit(X_scaled, y_scaled, sample_weight=sample_weights)
        else:
            self.model.fit(X_scaled, y_scaled)

        print("Model training complete!")

        # Store feature importances
        print("Calculating feature importances...")
        self.feature_importances = dict(zip(self.feature_columns, self.model.feature_importances_))

        # Sort feature importances in descending order
        self.feature_importances = {k: v for k, v in sorted(
            self.feature_importances.items(),
            key=lambda item: item[1],
            reverse=True
        )}

        # Print top 5 features
        print("Top 5 important features:")
        for i, (feature, importance) in enumerate(list(self.feature_importances.items())[:5]):
            print(f"  {i+1}. {feature}: {importance:.4f}")

    def plot_causal_graph(self, figsize=(8, 6)):
        """
        Plot the discovered causal graph.

        Parameters:
        -----------
        figsize : tuple
            Figure size

        Returns:
        --------
        matplotlib.figure.Figure
            Figure with causal graph
        """
        if self.causal_graph is None:
            raise ValueError("Causal graph not available. Call fit() first.")

        plt.figure(figsize=figsize)

        # Create a copy of the graph for visualization
        G = self.causal_graph.copy()

        # Get edge weights for line thickness
        edge_weights = [G[u][v]['weight'] * 3 for u, v in G.edges()]

        # Get node sizes based on feature importance
        node_sizes = []
        for node in G.nodes():
            if node == self.target_column:
                node_sizes.append(2000)  # Make target node larger
            elif node in self.feature_importances:
                # Scale feature importance to node size
                node_sizes.append(1000 * self.feature_importances[node] + 500)
            else:
                node_sizes.append(500)  # Default size

        # Create a layout for the graph
        pos = nx.spring_layout(G, seed=self.random_state)

        # Draw the graph
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes, alpha=0.8,
                              node_color='lightblue', edgecolors='black')

        # Draw edges with varying thickness based on weight
        nx.draw_networkx_edges(G, pos, width=edge_weights, alpha=0.7,
                              edge_color='gray', arrows=True, arrowsize=15)

        # Draw labels
        nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif')

        # Add edge labels for lag information
        edge_labels = {}
        for u, v, data in G.edges(data=True):
            if 'lag' in data:
                edge_labels[(u, v)] = f"lag={data['lag']}"

        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)

        plt.title('Causal Graph: Relationships Between Features', fontsize=16)
        plt.axis('off')
        plt.tight_layout()

        return plt.gcf()

    def plot_feature_importance(self, top_n=10, figsize=(8, 4)):
        """
        Plot feature importance.

        Parameters:
        -----------
        top_n : int
            Number of top features to show
        figsize : tuple
            Figure size

        Returns:
        --------
        matplotlib.figure.Figure
            Figure with feature importance
        """
        if self.feature_importances is None:
            raise ValueError("Feature importances not available. Call fit() first.")

        plt.figure(figsize=figsize)

        # Get top N features
        top_features = list(self.feature_importances.keys())[:top_n]
        top_importances = [self.feature_importances[feature] for feature in top_features]

        # Create horizontal bar plot
        bars = plt.barh(range(len(top_features)), top_importances, align='center', alpha=0.8)

        # Add feature names as y-tick labels
        plt.yticks(range(len(top_features)), top_features)

        # Add values to the end of each bar
        for i, v in enumerate(top_importances):
            plt.text(v + 0.01, i, f"{v:.3f}", va='center')

        # Add labels and title
        plt.xlabel('Importance')
        plt.title('Top Feature Importances', fontsize=16)
        plt.tight_layout()

        # Color bars based on whether they are causal features
        if self.causal_graph is not None:
            direct_causes = list(self.causal_graph.predecessors(self.target_column))
            for i, feature in enumerate(top_features):
                if feature in direct_causes:
                    bars[i].set_color('orange')  # Causal features in orange
                else:
                    bars[i].set_color('skyblue')  # Non-causal features in blue

            # Add legend
            from matplotlib.patches import Patch
            legend_elements = [
                Patch(facecolor='orange', label='Causal Feature'),
                Patch(facecolor='skyblue', label='Non-Causal Feature')
            ]
            plt.legend(handles=legend_elements, loc='lower right')

        return plt.gcf()

    def plot_predictions(self, predictions, historical_data=None, figsize=(8, 4)):
        """
        Plot predictions with uncertainty intervals.

        Parameters:
        -----------
        predictions : pandas.DataFrame
            DataFrame with predictions from predict()
        historical_data : pandas.DataFrame
            Optional historical data to show alongside predictions
        figsize : tuple
            Figure size

        Returns:
        --------
        matplotlib.figure.Figure
            Figure with predictions plot
        """
        plt.figure(figsize=figsize)

        # Plot predictions
        plt.plot(predictions[self.date_column], predictions['prediction'],
                 marker='o', linestyle='-', color='blue', label='Prediction')

        # Plot uncertainty intervals if available
        if 'lower_bound' in predictions.columns and 'upper_bound' in predictions.columns:
            plt.fill_between(predictions[self.date_column],
                            predictions['lower_bound'],
                            predictions['upper_bound'],
                            alpha=0.2, color='blue', label='Uncertainty Interval')

        # Plot historical data if provided
        if historical_data is not None:
            hist_df = historical_data.copy()
            hist_df[self.date_column] = pd.to_datetime(hist_df[self.date_column])
            plt.plot(hist_df[self.date_column], hist_df[self.target_column],
                     marker='x', linestyle='-', color='green', label='Historical Data')

        # Format the plot
        plt.title('Time Series Forecast with TCI-fix', fontsize=16)
        plt.xlabel('Date')
        plt.ylabel('Quantity')
        plt.grid(True, alpha=0.3)
        plt.legend()

        # Format x-axis to show dates nicely
        plt.gcf().autofmt_xdate()

        # Add annotations for key points
        max_pred_idx = predictions['prediction'].argmax()
        plt.annotate(f"Max: {predictions['prediction'].iloc[max_pred_idx]}",
                    xy=(predictions[self.date_column].iloc[max_pred_idx],
                        predictions['prediction'].iloc[max_pred_idx]),
                    xytext=(10, 10), textcoords='offset points',
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=.2'))

        plt.tight_layout()

        return plt.gcf()

    def save(self, filepath):
        """
        Save the model to a file.

        Parameters:
        -----------
        filepath : str
            Path to save the model

        Returns:
        --------
        None
        """
        with open(filepath, 'wb') as f:
            pickle.dump(self, f)

    @classmethod
    def load(cls, filepath):
        """
        Load a model from a file.

        Parameters:
        -----------
        filepath : str
            Path to the saved model

        Returns:
        --------
        TCIFixPredictor
            Loaded model
        """
        with open(filepath, 'rb') as f:
            return pickle.load(f)
