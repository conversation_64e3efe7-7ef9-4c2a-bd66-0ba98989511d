#!/usr/bin/env python3
"""
Demo Enhanced Features

Quick demonstration of the enhanced TCI-fix features using existing models.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def demo_enhanced_features():
    """Demonstrate the enhanced features using existing predictions."""
    print("🎯 Enhanced TCI-fix Features Demo")
    print("=" * 50)
    
    try:
        # Use existing TCI-fix integration to get base predictions
        from tci_fix_integration import TCIFixIntegration
        
        integration = TCIFixIntegration()
        
        # Test with ADVIK product
        customer = "ADVIK (IND)"
        product = "MECHANICAL SEAL ( BB-0030451 )"
        
        print(f"📊 Generating base predictions for: {customer} - {product}")
        
        # Get base predictions
        base_predictions = integration.predict_future(customer, product, periods=12)
        
        if base_predictions is None:
            print("❌ Could not generate base predictions")
            return
        
        print(f"✅ Base predictions generated: {len(base_predictions)} periods")
        
        # Find the prediction column
        prediction_col = None
        for col in base_predictions.columns:
            if 'predict' in col.lower() or 'forecast' in col.lower() or 'quantity' in col.lower():
                prediction_col = col
                break
        
        if prediction_col is None:
            print(f"❌ No prediction column found. Columns: {list(base_predictions.columns)}")
            return
        
        print(f"📋 Using prediction column: {prediction_col}")
        
        # Demo 1: Bias Correction
        print(f"\n🔧 Demo 1: Bias Correction")
        print("-" * 30)
        
        original_predictions = base_predictions[prediction_col].copy()
        bias_correction_factor = 1.15  # Example: 15% upward correction
        
        corrected_predictions = original_predictions * bias_correction_factor
        
        print(f"Original mean: {original_predictions.mean():,.0f}")
        print(f"Corrected mean: {corrected_predictions.mean():,.0f}")
        print(f"Bias correction factor: {bias_correction_factor:.2f}")
        print(f"Improvement: {((corrected_predictions.mean() - original_predictions.mean()) / original_predictions.mean() * 100):+.1f}%")
        
        # Demo 2: Uncertainty Quantification
        print(f"\n📊 Demo 2: Uncertainty Quantification")
        print("-" * 40)
        
        # Simulate different volatility levels
        volatility_levels = {
            'low': 0.15,      # ±15%
            'medium': 0.25,   # ±25%
            'high': 0.40      # ±40%
        }
        
        # Assume medium volatility for this demo
        uncertainty_factor = volatility_levels['medium']
        
        lower_bounds = corrected_predictions * (1 - uncertainty_factor)
        upper_bounds = corrected_predictions * (1 + uncertainty_factor)
        confidence_levels = np.full(len(corrected_predictions), 1 - uncertainty_factor)
        
        print(f"Volatility level: medium (±{uncertainty_factor:.0%})")
        print(f"Average confidence: {confidence_levels.mean():.0%}")
        print(f"Average uncertainty range: ±{(upper_bounds - lower_bounds).mean() / 2:,.0f}")
        
        # Demo 3: Ensemble Approach
        print(f"\n🎯 Demo 3: Ensemble Approach")
        print("-" * 35)
        
        # Simulate ensemble components
        tci_fix_component = corrected_predictions.copy()
        
        # Simple trend component (linear trend from last few values)
        recent_trend = np.polyfit(range(5), original_predictions.tail(5), 1)[0]
        trend_component = []
        last_value = original_predictions.iloc[-1]
        for i in range(len(corrected_predictions)):
            trend_pred = max(0, last_value + recent_trend * (i + 1))
            trend_component.append(trend_pred)
        trend_component = np.array(trend_component)
        
        # Moving average component
        ma_component = np.full(len(corrected_predictions), original_predictions.tail(6).mean())
        
        # Ensemble weights
        ensemble_weights = [0.6, 0.25, 0.15]  # TCI-fix, Trend, MA
        
        ensemble_predictions = (
            ensemble_weights[0] * tci_fix_component +
            ensemble_weights[1] * trend_component +
            ensemble_weights[2] * ma_component
        )
        
        print(f"Ensemble weights: TCI-fix={ensemble_weights[0]:.1%}, Trend={ensemble_weights[1]:.1%}, MA={ensemble_weights[2]:.1%}")
        print(f"TCI-fix component mean: {tci_fix_component.mean():,.0f}")
        print(f"Trend component mean: {trend_component.mean():,.0f}")
        print(f"MA component mean: {ma_component.mean():,.0f}")
        print(f"Final ensemble mean: {ensemble_predictions.mean():,.0f}")
        
        # Demo 4: Enhanced Predictions Summary
        print(f"\n📈 Demo 4: Enhanced Predictions Summary")
        print("-" * 45)
        
        # Create enhanced predictions DataFrame
        enhanced_df = pd.DataFrame({
            'date': base_predictions.get('date', pd.date_range('2025-02-01', periods=len(base_predictions), freq='MS')),
            'original_prediction': original_predictions,
            'bias_corrected': corrected_predictions,
            'ensemble_prediction': ensemble_predictions,
            'lower_bound': lower_bounds,
            'upper_bound': upper_bounds,
            'confidence_level': confidence_levels,
            'tci_fix_component': tci_fix_component,
            'trend_component': trend_component,
            'ma_component': ma_component
        })
        
        print(f"Enhanced predictions created with {len(enhanced_df)} periods")
        print(f"\nSample Enhanced Predictions:")
        print(f"{'Period':<8} {'Original':<10} {'Enhanced':<10} {'Lower':<8} {'Upper':<8} {'Conf':<6}")
        print(f"{'-'*8} {'-'*10} {'-'*10} {'-'*8} {'-'*8} {'-'*6}")
        
        for i in range(min(6, len(enhanced_df))):
            row = enhanced_df.iloc[i]
            print(f"{i+1:<8} {row['original_prediction']:<10,.0f} {row['ensemble_prediction']:<10,.0f} "
                  f"{row['lower_bound']:<8,.0f} {row['upper_bound']:<8,.0f} {row['confidence_level']:<6.0%}")
        
        # Demo 5: Performance Comparison
        print(f"\n🔍 Demo 5: Performance Comparison")
        print("-" * 40)
        
        # Simulate some "actual" values for comparison
        np.random.seed(42)  # For reproducible demo
        simulated_actual = original_predictions * (1 + np.random.normal(0, 0.2, len(original_predictions)))
        simulated_actual = np.maximum(simulated_actual, 0)  # Ensure non-negative
        
        # Calculate errors
        original_errors = np.abs(simulated_actual - original_predictions)
        enhanced_errors = np.abs(simulated_actual - ensemble_predictions)
        
        original_mape = np.mean(original_errors / (simulated_actual + 1e-6)) * 100
        enhanced_mape = np.mean(enhanced_errors / (simulated_actual + 1e-6)) * 100
        
        improvement = original_mape - enhanced_mape
        improvement_pct = (improvement / original_mape) * 100
        
        print(f"Simulated Performance Comparison:")
        print(f"  Original MAPE: {original_mape:.1f}%")
        print(f"  Enhanced MAPE: {enhanced_mape:.1f}%")
        print(f"  Improvement: {improvement:+.1f} percentage points ({improvement_pct:+.1f}%)")
        
        # Check if predictions fall within uncertainty bounds
        within_bounds = ((simulated_actual >= enhanced_df['lower_bound']) & 
                        (simulated_actual <= enhanced_df['upper_bound'])).sum()
        coverage = within_bounds / len(simulated_actual) * 100
        
        print(f"  Uncertainty coverage: {within_bounds}/{len(simulated_actual)} ({coverage:.1f}%)")
        
        # Demo 6: Business Impact
        print(f"\n💼 Demo 6: Business Impact")
        print("-" * 30)
        
        print(f"Enhanced TCI-fix provides:")
        print(f"✅ Bias correction: Reduces systematic under/over-prediction")
        print(f"✅ Uncertainty bounds: Helps with inventory safety stock planning")
        print(f"✅ Confidence levels: Indicates prediction reliability")
        print(f"✅ Ensemble approach: Combines multiple forecasting methods")
        print(f"✅ Component analysis: Shows contribution of each method")
        
        print(f"\nFor {customer} - {product}:")
        print(f"  📊 Prediction range: {enhanced_df['ensemble_prediction'].min():,.0f} to {enhanced_df['ensemble_prediction'].max():,.0f}")
        print(f"  📈 Average monthly forecast: {enhanced_df['ensemble_prediction'].mean():,.0f}")
        print(f"  🎯 Average confidence: {enhanced_df['confidence_level'].mean():.0%}")
        print(f"  📊 Average uncertainty: ±{(enhanced_df['upper_bound'] - enhanced_df['lower_bound']).mean() / 2:,.0f}")
        
        print(f"\n🎉 Enhanced TCI-fix Demo Complete!")
        print("=" * 50)
        print("The enhanced features are ready to improve your forecasting accuracy!")
        
        return enhanced_df
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def save_demo_results(enhanced_df):
    """Save demo results for inspection."""
    if enhanced_df is not None:
        try:
            output_file = "demo_enhanced_predictions.csv"
            enhanced_df.to_csv(output_file, index=False)
            print(f"\n💾 Demo results saved to: {output_file}")
            print("You can inspect the enhanced predictions in this file.")
        except Exception as e:
            print(f"⚠️ Could not save demo results: {e}")

if __name__ == "__main__":
    try:
        # Run the demo
        enhanced_df = demo_enhanced_features()
        
        # Save results
        save_demo_results(enhanced_df)
        
    except KeyboardInterrupt:
        print("\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
