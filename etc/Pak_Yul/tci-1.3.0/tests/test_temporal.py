"""
Tests for temporal module.

This module contains tests for the temporal capabilities in the TCI framework.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np
import networkx as nx
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any

from tci.temporal import (
    TimeSeriesCausalDiscovery,
    GrangerCausalityTest,
    TransferEntropyAnalysis,
    CCM,
    PCMCI,
    DynamicCausalModel,
    TemporalCausalGraph,
    StateSpaceModel,
    VectorAutoregression,
    TemporalCausalReasoning,
    TemporalDSeparation,
    TemporalDoOperator,
    TemporalCounterfactual,
    CounterfactualPredictor,
    InterventionalPredictor,
    TemporalCausalForecaster,
    CausalTimeSeriesModel
)


class TestTimeSeriesCausalDiscovery:
    """Tests for time-series causal discovery."""
    
    def test_granger_causality_test(self):
        """Test GrangerCausalityTest."""
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create Granger causality test
        granger = GrangerCausalityTest(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=2,
            significance_level=0.05
        )
        
        # Discover causal relationships
        G = granger.discover(data)
        
        # Check edges
        assert G.has_edge('X', 'Y')
        assert G.has_edge('Y', 'Z')
        assert not G.has_edge('Z', 'X')
        
        # Get optimal lags
        optimal_lags = granger.get_optimal_lag(data)
        
        # Check optimal lags
        assert optimal_lags[('X', 'Y')] == 1
        assert optimal_lags[('Y', 'Z')] == 1
    
    def test_transfer_entropy_analysis(self):
        """Test TransferEntropyAnalysis."""
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create Transfer Entropy Analysis
        te = TransferEntropyAnalysis(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=2,
            significance_level=0.05,
            n_bins=5,
            n_shuffles=10
        )
        
        # Discover causal relationships
        G = te.discover(data)
        
        # Check edges (note: transfer entropy may be less reliable with small samples)
        # We're just checking that the method runs without errors
        assert isinstance(G, nx.DiGraph)
    
    def test_ccm(self):
        """Test CCM."""
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create CCM
        ccm = CCM(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=2,
            significance_level=0.05,
            embedding_dimension=2,
            library_sizes=[10, 20, 40, 80],
            n_bootstraps=5
        )
        
        # Discover causal relationships
        G = ccm.discover(data)
        
        # Check that the method runs without errors
        assert isinstance(G, nx.DiGraph)
    
    def test_pcmci(self):
        """Test PCMCI."""
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create PCMCI
        pcmci = PCMCI(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=2,
            significance_level=0.05,
            independence_test='parcorr'
        )
        
        # Discover causal relationships
        G = pcmci.discover(data)
        
        # Check that the method runs without errors
        assert isinstance(G, nx.DiGraph)
class TestDynamicCausalModels:
    """Tests for dynamic causal models."""
    
    def test_temporal_causal_graph(self):
        """Test TemporalCausalGraph."""
        # Create temporal causal graph
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        max_lag = 2
        
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        tcg.add_edge('X', 'Z', 0, 2, 0.3)
        
        # Check edges
        assert tcg.has_edge('X', 'Y', 1, 2)
        assert tcg.has_edge('Y', 'Z', 1, 2)
        assert tcg.has_edge('X', 'Z', 0, 2)
        assert not tcg.has_edge('Z', 'X', 1, 2)
        
        # Check edge weights
        assert tcg.get_edge_weight('X', 'Y', 1, 2) == 0.5
        assert tcg.get_edge_weight('Y', 'Z', 1, 2) == 0.7
        assert tcg.get_edge_weight('X', 'Z', 0, 2) == 0.3
        
        # Remove edge
        tcg.remove_edge('X', 'Z', 0, 2)
        assert not tcg.has_edge('X', 'Z', 0, 2)
        
        # Get parents
        parents = tcg.get_parents('Y', 2)
        assert ('X', 1) in parents
        
        # Get children
        children = tcg.get_children('Y', 1)
        assert ('Z', 2) in children
        
        # Get adjacency tensor
        adj_tensor = tcg.get_adjacency_tensor()
        assert adj_tensor.shape == (max_lag + 1, n_variables, max_lag + 1, n_variables)
        assert adj_tensor[1, 0, 2, 1] == 0.5  # X_1 -> Y_2
        assert adj_tensor[1, 1, 2, 2] == 0.7  # Y_1 -> Z_2
        
        # Get summary graph
        summary_graph = tcg.get_summary_graph()
        assert summary_graph.has_edge('X', 'Y')
        assert summary_graph.has_edge('Y', 'Z')
        
        # Convert to NetworkX
        G = tcg.to_networkx()
        assert G.has_edge('X_1', 'Y_2')
        assert G.has_edge('Y_1', 'Z_2')
        
        # Create from NetworkX
        tcg2 = TemporalCausalGraph.from_networkx(G)
        assert tcg2.has_edge('X', 'Y', 1, 2)
        assert tcg2.has_edge('Y', 'Z', 1, 2)
    
    def test_dynamic_causal_model(self):
        """Test DynamicCausalModel."""
        # Skip if torch is not available
        try:
            import torch
        except ImportError:
            pytest.skip("PyTorch not available")
        
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        max_lag = 2
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create dynamic causal model
        model = DynamicCausalModel(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag,
            hidden_dims=[10, 5],
            learning_rate=0.01,
            device='cpu'
        )
        
        # Fit model
        history = model.fit(
            data=data,
            n_epochs=5,
            batch_size=32,
            validation_split=0.2,
            verbose=False
        )
        
        # Check history
        assert 'loss' in history
        assert 'val_loss' in history
        assert len(history['loss']) == 5
        
        # Predict
        predictions = model.predict(data)
        assert predictions.shape == (n_samples - max_lag, n_variables)
        
        # Forecast
        forecast = model.forecast(data, n_steps=5)
        assert forecast.shape == (5, n_variables)
        
        # Get causal graph
        causal_graph = model.get_causal_graph()
        assert isinstance(causal_graph, TemporalCausalGraph)
        
        # Get causal effects
        causal_effects = model.get_causal_effects()
        assert causal_effects.shape == (max_lag, n_variables, n_variables)
    
    def test_state_space_model(self):
        """Test StateSpaceModel."""
        # Create synthetic dataset
        n_states = 2
        n_observables = 3
        state_names = ['S1', 'S2']
        observable_names = ['O1', 'O2', 'O3']
        n_samples = 100
        
        # Generate data
        np.random.seed(42)
        
        # State transition matrix
        A = np.array([[0.8, 0.2], [0.3, 0.7]])
        
        # Observation matrix
        C = np.array([[1.0, 0.0], [0.0, 1.0], [0.5, 0.5]])
        
        # Generate states
        states = np.zeros((n_samples, n_states))
        states[0] = np.random.randn(n_states)
        
        for t in range(1, n_samples):
            states[t] = A @ states[t-1] + 0.1 * np.random.randn(n_states)
        
        # Generate observations
        observations = np.zeros((n_samples, n_observables))
        
        for t in range(n_samples):
            observations[t] = C @ states[t] + 0.1 * np.random.randn(n_observables)
        
        # Create state space model
        model = StateSpaceModel(
            n_states=n_states,
            n_observables=n_observables,
            state_names=state_names,
            observable_names=observable_names
        )
        
        # Set parameters
        model.set_state_transition_matrix(A)
        model.set_observation_matrix(C)
        model.set_process_noise_covariance(0.1 * np.eye(n_states))
        model.set_observation_noise_covariance(0.1 * np.eye(n_observables))
        
        # Set state
        model.set_state(states[0])
        
        # Predict state
        state_predictions = model.predict_state(n_steps=5)
        assert state_predictions.shape == (5, n_states)
        
        # Predict observables
        observable_predictions = model.predict_observables(n_steps=5)
        assert observable_predictions.shape == (5, n_observables)
        
        # Fit model
        history = model.fit(observations, n_iterations=2, tol=1e-6)
        
        # Check history
        assert 'log_likelihood' in history
        
        # Filter
        filtered_states, filtered_covariances = model.filter(observations)
        assert filtered_states.shape == (n_samples, n_states)
        assert filtered_covariances.shape == (n_samples, n_states, n_states)
        
        # Smooth
        smoothed_states, smoothed_covariances = model.smooth(observations)
        assert smoothed_states.shape == (n_samples, n_states)
        assert smoothed_covariances.shape == (n_samples, n_states, n_states)
        
        # Get causal graph
        causal_graph = model.get_causal_graph()
        assert isinstance(causal_graph, nx.DiGraph)
    
    def test_vector_autoregression(self):
        """Test VectorAutoregression."""
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        max_lag = 2
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create VAR model
        model = VectorAutoregression(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Fit model
        results = model.fit(data, trend='c')
        
        # Check results
        assert 'aic' in results
        assert 'bic' in results
        
        # Predict
        predictions = model.predict(data, n_steps=5)
        assert predictions.shape == (5, n_variables)
        
        # Simulate
        simulations = model.simulate(n_steps=10, seed=42)
        assert simulations.shape == (10, n_variables)
        
        # Impulse response
        irf = model.impulse_response(n_steps=10)
        assert irf.shape == (10, n_variables, n_variables)
        
        # Forecast error variance decomposition
        fevd = model.forecast_error_variance_decomposition(n_steps=10)
        assert fevd.shape == (10, n_variables, n_variables)
        
        # Granger causality
        granger_results = model.granger_causality()
        assert isinstance(granger_results, dict)
        
        # Get causal graph
        causal_graph = model.get_causal_graph()
        assert isinstance(causal_graph, nx.DiGraph)
        
        # Get causal effects
        causal_effects = model.get_causal_effects()
        assert causal_effects.shape == (max_lag, n_variables, n_variables)
class TestTemporalCausalReasoning:
    """Tests for temporal causal reasoning."""
    
    def test_temporal_causal_reasoning(self):
        """Test TemporalCausalReasoning."""
        # Create temporal causal graph
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        max_lag = 2
        
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        tcg.add_edge('X', 'Z', 0, 2, 0.3)
        
        # Create temporal causal reasoning
        tcr = TemporalCausalReasoning(tcg)
        
        # Get causal effect
        effect = tcr.get_causal_effect('X', 'Z', 0, 2)
        assert effect == 0.3
        
        # Get causal paths
        paths = tcr.get_causal_paths('X', 'Z', 0, 2)
        assert len(paths) == 1
        assert paths[0] == ['X_0', 'Z_2']
        
        # Check d-separation
        assert not tcr.is_d_separated('X', 'Z', 0, 2)
        assert tcr.is_d_separated('X', 'Z', 0, 2, [('Y', 1)])
        
        # Get Markov blanket
        markov_blanket = tcr.get_markov_blanket('Y', 1)
        assert ('Z', 2) in markov_blanket
        
        # Get ancestors
        ancestors = tcr.get_ancestors('Z', 2)
        assert ('X', 0) in ancestors
        assert ('Y', 1) in ancestors
        
        # Get descendants
        descendants = tcr.get_descendants('X', 0)
        assert ('Z', 2) in descendants
    
    def test_temporal_d_separation(self):
        """Test TemporalDSeparation."""
        # Create temporal causal graph
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        max_lag = 2
        
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        tcg.add_edge('X', 'Z', 0, 2, 0.3)
        
        # Create temporal d-separation
        tds = TemporalDSeparation(tcg)
        
        # Check d-separation
        assert not tds.is_d_separated([('X', 0)], [('Z', 2)])
        assert tds.is_d_separated([('X', 0)], [('Z', 2)], [('Y', 1)])
        
        # Find minimal separator
        separator = tds.find_minimal_separator([('X', 0)], [('Z', 2)])
        assert ('Y', 1) in separator
        
        # Get all d-separations
        d_separations = tds.get_all_d_separations()
        assert isinstance(d_separations, list)
        
        # Create synthetic dataset
        n_samples = 200
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Test d-separation
        is_d_separated, p_value = tds.test_d_separation(
            [('X', 0)], [('Z', 2)], [('Y', 1)],
            data=data, variable_names=variable_names
        )
        
        assert isinstance(is_d_separated, bool)
        assert isinstance(p_value, float)
    
    def test_temporal_do_operator(self):
        """Test TemporalDoOperator."""
        # Create temporal causal graph
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        max_lag = 2
        
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        tcg.add_edge('X', 'Z', 0, 2, 0.3)
        
        # Create temporal do-operator
        tdo = TemporalDoOperator(tcg)
        
        # Apply do-operator
        modified_graph = tdo.do('X', 0)
        
        # Check edges
        assert not modified_graph.has_edge('Y', 'X', 1, 0)
        assert not modified_graph.has_edge('Z', 'X', 1, 0)
        
        # Apply multiple do-operators
        modified_graph = tdo.do_multiple([('X', 0, None), ('Y', 1, None)])
        
        # Check edges
        assert not modified_graph.has_edge('Y', 'X', 1, 0)
        assert not modified_graph.has_edge('Z', 'X', 1, 0)
        assert not modified_graph.has_edge('X', 'Y', 0, 1)
        assert not modified_graph.has_edge('Z', 'Y', 0, 1)
        
        # Get backdoor adjustment set
        adjustment_set = tdo.get_backdoor_adjustment_set('X', 'Z', 0, 2)
        assert isinstance(adjustment_set, list)
        
        # Get frontdoor adjustment set
        adjustment_set = tdo.get_frontdoor_adjustment_set('X', 'Z', 0, 2)
        assert isinstance(adjustment_set, list)
    
    def test_temporal_counterfactual(self):
        """Test TemporalCounterfactual."""
        # Skip if torch is not available
        try:
            import torch
        except ImportError:
            pytest.skip("PyTorch not available")
        
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        max_lag = 2
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create temporal causal graph
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        
        # Create dynamic causal model
        model = DynamicCausalModel(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag,
            hidden_dims=[10, 5],
            learning_rate=0.01,
            device='cpu'
        )
        
        # Fit model
        model.fit(
            data=data,
            n_epochs=2,
            batch_size=32,
            validation_split=0.2,
            verbose=False
        )
        
        # Create temporal counterfactual
        tc = TemporalCounterfactual(tcg, model)
        
        # Compute counterfactual
        counterfactual_data = tc.counterfactual(data, 'X', 0, 1.0)
        assert isinstance(counterfactual_data, np.ndarray)
        
        # Compute multiple counterfactuals
        counterfactual_data = tc.multiple_counterfactuals(data, [('X', 0, 1.0), ('Y', 1, 0.5)])
        assert isinstance(counterfactual_data, np.ndarray)
class TestCounterfactualPrediction:
    """Tests for counterfactual prediction."""
    
    def test_counterfactual_predictor(self):
        """Test CounterfactualPredictor."""
        # Skip if torch is not available
        try:
            import torch
        except ImportError:
            pytest.skip("PyTorch not available")
        
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        max_lag = 2
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create temporal causal graph
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        
        # Create dynamic causal model
        model = DynamicCausalModel(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag,
            hidden_dims=[10, 5],
            learning_rate=0.01,
            device='cpu'
        )
        
        # Fit model
        model.fit(
            data=data,
            n_epochs=2,
            batch_size=32,
            validation_split=0.2,
            verbose=False
        )
        
        # Create counterfactual predictor
        predictor = CounterfactualPredictor(tcg, model)
        
        # Predict counterfactual
        predictions = predictor.predict(data, [('X', 0, 1.0)], ['Z'], n_steps=1)
        assert isinstance(predictions, np.ndarray)
        
        # Compare interventions
        results = predictor.compare_interventions(
            data,
            [[('X', 0, 1.0)], [('Y', 1, 0.5)]],
            ['Z'],
            n_steps=1
        )
        assert isinstance(results, dict)
        
        # Find optimal intervention
        best_var, best_val, best_pred = predictor.optimal_intervention(
            data,
            'Z',
            0.5,
            ['X', 'Y'],
            [0.1, 0.5, 1.0],
            time_lag=1,
            n_steps=1
        )
        assert best_var in ['X', 'Y']
        assert best_val in [0.1, 0.5, 1.0]
        assert isinstance(best_pred, float)
        
        # Compute intervention effect size
        effect_size = predictor.intervention_effect_size(data, ('X', 0, 1.0), 'Z', n_steps=1)
        assert isinstance(effect_size, float)
    
    def test_interventional_predictor(self):
        """Test InterventionalPredictor."""
        # Skip if torch is not available
        try:
            import torch
        except ImportError:
            pytest.skip("PyTorch not available")
        
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        max_lag = 2
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create temporal causal graph
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        
        # Create dynamic causal model
        model = DynamicCausalModel(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag,
            hidden_dims=[10, 5],
            learning_rate=0.01,
            device='cpu'
        )
        
        # Fit model
        model.fit(
            data=data,
            n_epochs=2,
            batch_size=32,
            validation_split=0.2,
            verbose=False
        )
        
        # Create interventional predictor
        predictor = InterventionalPredictor(tcg, model)
        
        # Predict intervention
        predictions = predictor.predict(data, [('X', 0, 1.0)], ['Z'], n_steps=1)
        assert isinstance(predictions, np.ndarray)
        
        # Compare interventions
        results = predictor.compare_interventions(
            data,
            [[('X', 0, 1.0)], [('Y', 1, 0.5)]],
            ['Z'],
            n_steps=1
        )
        assert isinstance(results, dict)
        
        # Find optimal intervention
        best_var, best_val, best_pred = predictor.optimal_intervention(
            data,
            'Z',
            0.5,
            ['X', 'Y'],
            [0.1, 0.5, 1.0],
            time_lag=1,
            n_steps=1
        )
        assert best_var in ['X', 'Y']
        assert best_val in [0.1, 0.5, 1.0]
        assert isinstance(best_pred, float)
        
        # Compute intervention effect size
        effect_size = predictor.intervention_effect_size(data, ('X', 0, 1.0), 'Z', n_steps=1)
        assert isinstance(effect_size, float)
        
        # Compute causal effect
        causal_effect = predictor.causal_effect(data, 'X', 'Z', [0.1, 0.5, 1.0], time_lag=1, n_steps=1)
        assert isinstance(causal_effect, dict)
    
    def test_temporal_causal_forecaster(self):
        """Test TemporalCausalForecaster."""
        # Skip if torch is not available
        try:
            import torch
        except ImportError:
            pytest.skip("PyTorch not available")
        
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        max_lag = 2
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create temporal causal graph
        tcg = TemporalCausalGraph(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag
        )
        
        # Add edges
        tcg.add_edge('X', 'Y', 1, 2, 0.5)
        tcg.add_edge('Y', 'Z', 1, 2, 0.7)
        
        # Create dynamic causal model
        model = DynamicCausalModel(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag,
            hidden_dims=[10, 5],
            learning_rate=0.01,
            device='cpu'
        )
        
        # Fit model
        model.fit(
            data=data,
            n_epochs=2,
            batch_size=32,
            validation_split=0.2,
            verbose=False
        )
        
        # Create temporal causal forecaster
        forecaster = TemporalCausalForecaster(tcg, model)
        
        # Forecast
        forecasts = forecaster.forecast(data, n_steps=5, target_variables=['Z'])
        assert isinstance(forecasts, np.ndarray)
        
        # Forecast with intervention
        forecasts = forecaster.forecast_with_intervention(data, n_steps=5, interventions=[('X', 0, 1.0)], target_variables=['Z'])
        assert isinstance(forecasts, np.ndarray)
        
        # Forecast with scenarios
        results = forecaster.forecast_with_scenarios(
            data,
            n_steps=5,
            scenarios=[[('X', 0, 1.0)], [('Y', 1, 0.5)]],
            target_variables=['Z']
        )
        assert isinstance(results, dict)
        
        # Causal forecast
        forecasts = forecaster.causal_forecast(data, n_steps=5, target_variables=['Z'])
        assert isinstance(forecasts, np.ndarray)
    
    def test_causal_time_series_model(self):
        """Test CausalTimeSeriesModel."""
        # Skip if torch is not available
        try:
            import torch
        except ImportError:
            pytest.skip("PyTorch not available")
        
        # Create synthetic dataset
        n_variables = 3
        variable_names = ['X', 'Y', 'Z']
        n_samples = 200
        max_lag = 2
        
        # Generate data with causal relationships: X -> Y -> Z
        np.random.seed(42)
        data = np.zeros((n_samples, n_variables))
        
        # Generate X
        data[:, 0] = np.random.randn(n_samples)
        
        # Generate Y with lag-1 dependency on X
        for t in range(1, n_samples):
            data[t, 1] = 0.5 * data[t-1, 0] + 0.1 * np.random.randn()
        
        # Generate Z with lag-1 dependency on Y
        for t in range(1, n_samples):
            data[t, 2] = 0.5 * data[t-1, 1] + 0.1 * np.random.randn()
        
        # Create causal time series model
        model = CausalTimeSeriesModel(
            n_variables=n_variables,
            variable_names=variable_names,
            max_lag=max_lag,
            hidden_dims=[10, 5],
            learning_rate=0.01,
            device='cpu',
            discovery_method='granger'
        )
        
        # Fit model
        history = model.fit(
            data=data,
            n_epochs=2,
            batch_size=32,
            validation_split=0.2,
            verbose=False
        )
        
        # Check history
        assert 'loss' in history
        assert 'val_loss' in history
        
        # Predict
        predictions = model.predict(data)
        assert predictions.shape == (n_samples - max_lag, n_variables)
        
        # Forecast
        forecast = model.forecast(data, n_steps=5)
        assert forecast.shape == (5, n_variables)
        
        # Get causal graph
        causal_graph = model.get_causal_graph()
        assert isinstance(causal_graph, TemporalCausalGraph)
        
        # Get causal effects
        causal_effects = model.get_causal_effects()
        assert causal_effects.shape == (max_lag, n_variables, n_variables)


if __name__ == "__main__":
    pytest.main(["-v", "test_temporal.py"])
