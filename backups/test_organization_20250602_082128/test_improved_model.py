"""
Test script for the improved TCI Premium model.
"""

import os
import sys
import logging
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import our custom models
import sys
import os

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tci_premium import TCIPremiumPredictor
from folder_data_manager import FolderDataManager

def test_improved_model():
    """
    Test the improved TCI Premium model.
    """
    logger.info("Initializing data manager")
    data_manager = FolderDataManager(dataset_folder="dataset")
    
    logger.info("Initializing TCI Premium predictor")
    tci_premium = TCIPremiumPredictor()
    
    # Use a specific customer and product
    customer = "ADVIK (IND)"
    product = "MECHANICAL SEAL ( BB-0030451 )"
    logger.info(f"Using customer: {customer}")
    logger.info(f"Using product: {product}")
    
    # Predict future values
    logger.info(f"Predicting future values for {customer} - {product}")
    try:
        forecast = tci_premium.predict_future(customer, product, periods=36)  # 3 years (36 months)
        
        if forecast is not None and not forecast.empty:
            logger.info(f"Successfully generated forecast with {len(forecast)} periods")
            logger.info(f"Forecast date range: {forecast['ds'].min()} to {forecast['ds'].max()}")
            logger.info(f"Forecast sample:\n{forecast.head()}")
            
            # Save the forecast to a CSV file
            forecast_file = f"Forecast_Advik_BB-0030451(Improved).csv"
            forecast.to_csv(forecast_file, index=False)
            logger.info(f"Saved forecast to {forecast_file}")
            
            # Load historical data
            historical_file = f"dataset/{customer}/{product}.csv"
            historical_data = pd.read_csv(historical_file)
            historical_data['ds'] = pd.to_datetime(historical_data['date'])
            historical_data['y'] = historical_data['quantity']
            
            # Create a visualization comparing historical data and forecast
            plt.figure(figsize=(12, 6))
            
            # Plot historical data
            plt.plot(historical_data['ds'], historical_data['y'], 'b-', label='Historical Data')
            
            # Plot forecast
            plt.plot(forecast['ds'], forecast['yhat'], 'r-', label='Forecast')
            
            # Add confidence intervals
            plt.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'], 
                             color='r', alpha=0.2, label='Confidence Interval')
            
            # Add labels and title
            plt.xlabel('Date')
            plt.ylabel('Quantity')
            plt.title(f'Improved Forecast for {customer} - {product}')
            plt.legend()
            plt.grid(True)
            
            # Save the visualization
            plt.savefig(f"Forecast_Advik_BB-0030451(Improved).png", dpi=300, bbox_inches='tight')
            logger.info(f"Saved visualization to Forecast_Advik_BB-0030451(Improved).png")
            
            # Compare with previous forecast
            previous_forecast_file = f"Forecast_Advik_BB-0030451(2021 Premium).csv"
            if os.path.exists(previous_forecast_file):
                previous_forecast = pd.read_csv(previous_forecast_file)
                previous_forecast['ds'] = pd.to_datetime(previous_forecast['ds'])
                
                # Create a visualization comparing previous and improved forecasts
                plt.figure(figsize=(12, 6))
                
                # Plot historical data
                plt.plot(historical_data['ds'], historical_data['y'], 'b-', label='Historical Data')
                
                # Plot previous forecast
                plt.plot(previous_forecast['ds'], previous_forecast['yhat'], 'g-', label='Previous Forecast')
                
                # Plot improved forecast
                plt.plot(forecast['ds'], forecast['yhat'], 'r-', label='Improved Forecast')
                
                # Add labels and title
                plt.xlabel('Date')
                plt.ylabel('Quantity')
                plt.title(f'Comparison of Forecasts for {customer} - {product}')
                plt.legend()
                plt.grid(True)
                
                # Save the visualization
                plt.savefig(f"Forecast_Comparison_Advik_BB-0030451.png", dpi=300, bbox_inches='tight')
                logger.info(f"Saved comparison visualization to Forecast_Comparison_Advik_BB-0030451.png")
        else:
            logger.error("Failed to generate forecast")
    except Exception as e:
        logger.error(f"Error predicting future values: {str(e)}")

if __name__ == "__main__":
    test_improved_model()
