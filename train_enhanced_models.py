#!/usr/bin/env python3
"""
Train Enhanced Models

Script to train enhanced TCI-fix models for all customer-product combinations.
"""

import os
import pandas as pd
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def train_enhanced_models():
    """Train enhanced models for all customer-product combinations."""
    
    print("🤖 Training Enhanced TCI-fix Models")
    print("=" * 50)
    
    try:
        from production_enhanced_tci_fix import ProductionEnhancedTCIFix
        from folder_data_manager import FolderDataManager
        
        # Initialize enhanced integration
        enhanced_integration = ProductionEnhancedTCIFix()
        
        # Load data to get customer-product combinations
        data_manager = FolderDataManager("dataset")
        success, message = data_manager.load_data_from_folder()
        
        if not success:
            print(f"❌ Failed to load data: {message}")
            return
        
        # Get all customer-product combinations
        combinations = []
        for key in data_manager.data.keys():
            if '_' in key:
                customer, product = key.split('_', 1)
                combinations.append((customer, product))
        
        print(f"📊 Found {len(combinations)} customer-product combinations")
        
        # Train enhanced models
        successful = 0
        failed = 0
        
        for i, (customer, product) in enumerate(combinations, 1):
            print(f"\n🔧 Training {i}/{len(combinations)}: {customer} - {product}")
            
            try:
                success, message, model_info = enhanced_integration.train_enhanced_model(
                    customer, product, force_retrain=False
                )
                
                if success:
                    print(f"✅ Success: {message}")
                    if model_info:
                        print(f"   Volatility: {model_info.get('volatility_level', 'unknown')}")
                        print(f"   Bias correction: {model_info.get('bias_correction_factor', 1.0):.3f}")
                    successful += 1
                else:
                    print(f"❌ Failed: {message}")
                    failed += 1
                    
            except Exception as e:
                print(f"❌ Error: {e}")
                failed += 1
        
        print(f"\n📊 Training Summary:")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")
        
        if successful > 0:
            print(f"\n🎉 Enhanced models ready for use!")
            print("You can now use 'Enhanced TCI-fix' in the GUI")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

def train_specific_model(customer, product):
    """Train enhanced model for a specific customer-product."""
    
    print(f"🎯 Training Enhanced Model: {customer} - {product}")
    print("-" * 50)
    
    try:
        from production_enhanced_tci_fix import ProductionEnhancedTCIFix
        
        enhanced_integration = ProductionEnhancedTCIFix()
        
        success, message, model_info = enhanced_integration.train_enhanced_model(
            customer, product, force_retrain=True
        )
        
        if success:
            print(f"✅ Success: {message}")
            if model_info:
                print(f"\n📊 Model Information:")
                print(f"   Volatility level: {model_info.get('volatility_level', 'unknown')}")
                print(f"   Bias correction factor: {model_info.get('bias_correction_factor', 1.0):.3f}")
                print(f"   Uncertainty factor: {model_info.get('uncertainty_factor', 0.25):.1%}")
                print(f"   Data points: {model_info.get('data_points', 'unknown')}")
                print(f"   Training date: {model_info.get('training_date', 'unknown')}")
        else:
            print(f"❌ Failed: {message}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 3:
        # Train specific model
        customer = sys.argv[1]
        product = sys.argv[2]
        train_specific_model(customer, product)
    else:
        # Train all models
        train_enhanced_models()
