"""
Pattern Copy Forecaster

This module implements a pattern-based forecasting approach that directly copies
historical patterns with small variations to generate forecasts.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PatternCopyForecaster:
    """
    A forecaster that uses historical patterns to generate forecasts.
    """
    
    def __init__(self, variation_factor=0.1, smoothing_window=1):
        """
        Initialize the Pattern Copy Forecaster.
        
        Args:
            variation_factor (float): Factor to control the amount of random variation.
                Higher values mean more variation. Default is 0.1 (10%).
            smoothing_window (int): Window size for smoothing transitions between patterns.
                Default is 1 (no smoothing).
        """
        self.variation_factor = variation_factor
        self.smoothing_window = smoothing_window
        self.representative_year = None
        self.monthly_patterns = None
        self.monthly_volatility = None
        self.seasonal_factors = None
        self.trend_factor = 1.0
    
    def find_representative_year(self, historical_data):
        """
        Find the most representative year in the historical data.
        
        Args:
            historical_data (pd.DataFrame): Historical data with 'ds' and 'y' columns.
            
        Returns:
            int: The most representative year.
        """
        # Convert to datetime if needed
        if not pd.api.types.is_datetime64_dtype(historical_data['ds']):
            historical_data['ds'] = pd.to_datetime(historical_data['ds'])
        
        # Group by year and month
        historical_data['year'] = historical_data['ds'].dt.year
        historical_data['month'] = historical_data['ds'].dt.month
        
        # Calculate monthly statistics for each year
        yearly_stats = {}
        years = sorted(historical_data['year'].unique())
        
        # Need at least 2 years of data
        if len(years) < 2:
            logger.warning("Not enough years of data to find representative year. Using the only year available.")
            return years[0]
        
        # Calculate monthly patterns for each year
        for year in years:
            year_data = historical_data[historical_data['year'] == year]
            
            # Skip years with less than 6 months of data
            if len(year_data) < 6:
                continue
                
            # Calculate monthly averages
            monthly_avgs = year_data.groupby('month')['y'].mean()
            
            # Calculate volatility (coefficient of variation)
            monthly_volatility = year_data.groupby('month')['y'].std() / monthly_avgs
            
            # Store stats
            yearly_stats[year] = {
                'monthly_avgs': monthly_avgs,
                'monthly_volatility': monthly_volatility,
                'completeness': len(monthly_avgs) / 12,  # Ratio of months with data
                'total': year_data['y'].sum(),
                'data': year_data
            }
        
        # Find the most recent complete year (or nearly complete)
        recent_years = [y for y in years if y >= years[-2]]  # Last 2 years
        complete_recent_years = [y for y in recent_years if yearly_stats.get(y, {}).get('completeness', 0) >= 0.75]
        
        if complete_recent_years:
            # Use the most recent complete year
            self.representative_year = max(complete_recent_years)
        else:
            # Find the year with pattern most similar to the average pattern
            all_monthly_avgs = pd.DataFrame({year: stats['monthly_avgs'] for year, stats in yearly_stats.items() 
                                           if stats['completeness'] >= 0.5})
            
            if all_monthly_avgs.empty:
                # Not enough data, use the most recent year with any data
                self.representative_year = max(yearly_stats.keys())
            else:
                # Calculate average pattern across all years
                avg_pattern = all_monthly_avgs.mean(axis=1)
                
                # Calculate correlation with average pattern
                correlations = {}
                for year in all_monthly_avgs.columns:
                    year_pattern = all_monthly_avgs[year].dropna()
                    avg_pattern_subset = avg_pattern[year_pattern.index]
                    if len(year_pattern) >= 6:  # Need at least 6 months for meaningful correlation
                        correlations[year] = year_pattern.corr(avg_pattern_subset)
                
                if correlations:
                    # Use the year with highest correlation to average pattern
                    self.representative_year = max(correlations.items(), key=lambda x: x[1])[0]
                else:
                    # Fallback to most recent year with decent data
                    self.representative_year = max([y for y in yearly_stats.keys() 
                                                 if yearly_stats[y]['completeness'] >= 0.5], default=max(yearly_stats.keys()))
        
        logger.info(f"Selected representative year: {self.representative_year}")
        
        # Store the monthly patterns and volatility for the representative year
        rep_year_stats = yearly_stats[self.representative_year]
        self.monthly_patterns = rep_year_stats['monthly_avgs']
        self.monthly_volatility = rep_year_stats['monthly_volatility'].fillna(0.1)  # Default volatility of 10%
        
        # Calculate seasonal factors (ratio to yearly average)
        yearly_avg = self.monthly_patterns.mean()
        self.seasonal_factors = self.monthly_patterns / yearly_avg
        
        # Calculate trend factor based on recent years
        if len(years) >= 2:
            recent_years = sorted(years)[-2:]  # Last 2 years
            if all(y in yearly_stats for y in recent_years):
                older_total = yearly_stats[recent_years[0]]['total']
                newer_total = yearly_stats[recent_years[1]]['total']
                if older_total > 0:
                    yearly_growth = newer_total / older_total - 1
                    # Limit extreme growth values
                    yearly_growth = max(min(yearly_growth, 0.5), -0.3)
                    self.trend_factor = 1 + yearly_growth
                    logger.info(f"Calculated trend factor: {self.trend_factor} based on years {recent_years}")
        
        return self.representative_year
    
    def infer_manufacturing_cycles(self, historical_data):
        """
        Infer manufacturing cycles from historical data patterns.
        
        Args:
            historical_data (pd.DataFrame): Historical data with 'ds' and 'y' columns.
            
        Returns:
            dict: Dictionary with inferred manufacturing cycle information.
        """
        # Convert to datetime if needed
        if not pd.api.types.is_datetime64_dtype(historical_data['ds']):
            historical_data['ds'] = pd.to_datetime(historical_data['ds'])
        
        # Add year and month columns
        historical_data['year'] = historical_data['ds'].dt.year
        historical_data['month'] = historical_data['ds'].dt.month
        
        # Calculate monthly averages
        monthly_avgs = historical_data.groupby('month')['y'].mean()
        
        # Identify peak months (months with values above 1.2 times the average)
        yearly_avg = monthly_avgs.mean()
        peak_months = monthly_avgs[monthly_avgs > yearly_avg * 1.2].index.tolist()
        
        # Identify trough months (months with values below 0.8 times the average)
        trough_months = monthly_avgs[monthly_avgs < yearly_avg * 0.8].index.tolist()
        
        # Calculate typical cycle length (distance between peaks)
        if len(peak_months) >= 2:
            peak_months = sorted(peak_months)
            cycle_lengths = []
            for i in range(1, len(peak_months)):
                cycle_lengths.append(peak_months[i] - peak_months[i-1] if peak_months[i] > peak_months[i-1] 
                                    else peak_months[i] - peak_months[i-1] + 12)
            avg_cycle_length = sum(cycle_lengths) / len(cycle_lengths)
        else:
            avg_cycle_length = 12  # Default to annual cycle
        
        # Detect if there's a strong quarterly pattern
        quarterly_pattern = False
        if len(peak_months) >= 3:
            quarters = [((m-1) // 3) + 1 for m in peak_months]
            if len(set(quarters)) <= 2:  # Peaks concentrated in 1-2 quarters
                quarterly_pattern = True
        
        # Detect if there's a strong semi-annual pattern
        semiannual_pattern = False
        if len(peak_months) >= 2:
            half_years = [1 if m <= 6 else 2 for m in peak_months]
            if len(set(half_years)) == 1:  # Peaks all in same half of year
                semiannual_pattern = True
        
        # Detect if there's a strong annual pattern
        annual_pattern = avg_cycle_length >= 11 and avg_cycle_length <= 13
        
        # Determine the dominant cycle
        if quarterly_pattern:
            dominant_cycle = "quarterly"
            cycle_length = 3
        elif semiannual_pattern:
            dominant_cycle = "semiannual"
            cycle_length = 6
        elif annual_pattern:
            dominant_cycle = "annual"
            cycle_length = 12
        else:
            dominant_cycle = "custom"
            cycle_length = avg_cycle_length
        
        # Create manufacturing cycle information
        manufacturing_cycles = {
            'peak_months': peak_months,
            'trough_months': trough_months,
            'cycle_length': cycle_length,
            'dominant_cycle': dominant_cycle,
            'quarterly_pattern': quarterly_pattern,
            'semiannual_pattern': semiannual_pattern,
            'annual_pattern': annual_pattern
        }
        
        logger.info(f"Inferred manufacturing cycles: {manufacturing_cycles}")
        return manufacturing_cycles
    
    def fit(self, historical_data):
        """
        Fit the forecaster to historical data.
        
        Args:
            historical_data (pd.DataFrame): Historical data with 'ds' and 'y' columns.
        """
        # Find representative year
        self.find_representative_year(historical_data)
        
        # Infer manufacturing cycles
        self.manufacturing_cycles = self.infer_manufacturing_cycles(historical_data)
        
        return self
    
    def predict(self, periods=12, future_ds=None):
        """
        Generate forecast for future periods.
        
        Args:
            periods (int): Number of periods to forecast.
            future_ds (pd.Series, optional): Future dates to forecast for.
                If not provided, will generate dates starting from the end of historical data.
                
        Returns:
            pd.DataFrame: Forecast with ds, yhat, yhat_lower, yhat_upper columns.
        """
        if self.monthly_patterns is None:
            raise ValueError("Forecaster has not been fit to historical data yet.")
        
        if future_ds is None:
            # Generate future dates (monthly frequency)
            last_date = datetime.now().replace(day=1)
            future_ds = pd.date_range(start=last_date, periods=periods, freq='MS')
        
        # Create forecast dataframe
        forecast = pd.DataFrame({'ds': future_ds})
        forecast['month'] = forecast['ds'].dt.month
        forecast['year'] = forecast['ds'].dt.year
        
        # Calculate years since representative year
        forecast['years_since_rep'] = forecast['year'] - self.representative_year
        
        # Generate predictions
        yhat = []
        yhat_lower = []
        yhat_upper = []
        
        for _, row in forecast.iterrows():
            month = row['month']
            years_since_rep = row['years_since_rep']
            
            # Get base value from monthly pattern
            if month in self.monthly_patterns:
                base_value = self.monthly_patterns[month]
            else:
                # Use average if month not in patterns
                base_value = self.monthly_patterns.mean()
            
            # Apply trend factor
            trend_adjusted_value = base_value * (self.trend_factor ** years_since_rep)
            
            # Apply manufacturing cycle adjustments
            cycle_adjustment = 1.0
            
            # Boost peak months
            if month in self.manufacturing_cycles['peak_months']:
                cycle_adjustment *= 1.2
            
            # Reduce trough months
            if month in self.manufacturing_cycles['trough_months']:
                cycle_adjustment *= 0.7
            
            # Apply cycle adjustment
            cycle_adjusted_value = trend_adjusted_value * cycle_adjustment
            
            # Add random variation based on historical volatility
            if month in self.monthly_volatility:
                volatility = self.monthly_volatility[month]
            else:
                volatility = 0.1  # Default volatility
                
            # Increase volatility for forecasts further in the future
            volatility = volatility * (1 + 0.1 * years_since_rep)
            
            # Generate random variation
            random_factor = np.random.normal(1.0, volatility * self.variation_factor)
            
            # Ensure random factor is within reasonable bounds
            random_factor = max(0.7, min(1.3, random_factor))
            
            # Calculate final prediction
            prediction = cycle_adjusted_value * random_factor
            
            # Calculate prediction intervals
            lower_bound = prediction * (1 - 2 * volatility)
            upper_bound = prediction * (1 + 2 * volatility)
            
            # Ensure non-negative values
            prediction = max(0, prediction)
            lower_bound = max(0, lower_bound)
            
            yhat.append(prediction)
            yhat_lower.append(lower_bound)
            yhat_upper.append(upper_bound)
        
        # Add predictions to forecast dataframe
        forecast['yhat'] = yhat
        forecast['yhat_lower'] = yhat_lower
        forecast['yhat_upper'] = yhat_upper
        
        # Apply smoothing if needed
        if self.smoothing_window > 1:
            forecast['yhat'] = forecast['yhat'].rolling(window=self.smoothing_window, center=True).mean().fillna(method='ffill').fillna(method='bfill')
            forecast['yhat_lower'] = forecast['yhat_lower'].rolling(window=self.smoothing_window, center=True).mean().fillna(method='ffill').fillna(method='bfill')
            forecast['yhat_upper'] = forecast['yhat_upper'].rolling(window=self.smoothing_window, center=True).mean().fillna(method='ffill').fillna(method='bfill')
        
        # Drop intermediate columns
        forecast = forecast.drop(['month', 'year', 'years_since_rep'], axis=1)
        
        return forecast
    
    def fit_predict(self, historical_data, periods=12, future_ds=None):
        """
        Fit the forecaster to historical data and generate forecast.
        
        Args:
            historical_data (pd.DataFrame): Historical data with 'ds' and 'y' columns.
            periods (int): Number of periods to forecast.
            future_ds (pd.Series, optional): Future dates to forecast for.
                
        Returns:
            pd.DataFrame: Forecast with ds, yhat, yhat_lower, yhat_upper columns.
        """
        self.fit(historical_data)
        return self.predict(periods, future_ds)
    
    def plot_forecast(self, historical_data, forecast, title=None, figsize=(12, 6)):
        """
        Plot historical data and forecast.
        
        Args:
            historical_data (pd.DataFrame): Historical data with 'ds' and 'y' columns.
            forecast (pd.DataFrame): Forecast with ds, yhat, yhat_lower, yhat_upper columns.
            title (str, optional): Plot title.
            figsize (tuple, optional): Figure size.
            
        Returns:
            matplotlib.figure.Figure: The figure object.
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # Plot historical data
        ax.plot(historical_data['ds'], historical_data['y'], 'b-', label='Historical Data')
        
        # Plot forecast
        ax.plot(forecast['ds'], forecast['yhat'], 'r-', label='Forecast')
        
        # Plot prediction intervals
        ax.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'], 
                        color='r', alpha=0.2, label='Prediction Interval')
        
        # Add labels and title
        ax.set_xlabel('Date')
        ax.set_ylabel('Value')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Pattern Copy Forecast')
        
        # Add legend
        ax.legend()
        
        # Add grid
        ax.grid(True, alpha=0.3)
        
        # Format x-axis dates
        fig.autofmt_xdate()
        
        return fig
