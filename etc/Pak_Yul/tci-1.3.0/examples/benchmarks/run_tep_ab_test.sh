#!/bin/bash

# Run A/B Testing for TCI with TEP Dataset
# This script runs A/B testing comparing different causal discovery methods
# on the Tennessee Eastman Process (TEP) environment.

# Set the TEP dataset path
TEP_DATASET_PATH="/home/<USER>/Ai/S3/tci/TEP_dataset"

# Set the output directory
OUTPUT_DIR="results/tep_ab_test"

# Create the output directory if it doesn't exist
mkdir -p $OUTPUT_DIR

# Run A/B test comparing PC and GES algorithms
echo "Running A/B test comparing PC and GES algorithms..."
python examples/benchmarks/tep_ab_test.py \
    --dataset-path $TEP_DATASET_PATH \
    --episodes 50 \
    --num-seeds 20 \
    --causal-method pc \
    --variant-causal-method ges \
    --output-dir $OUTPUT_DIR/pc_vs_ges \
    --gpu

# Run A/B test comparing PC and NOTEARS algorithms
echo "Running A/B test comparing PC and NOTEARS algorithms..."
python examples/benchmarks/tep_ab_test.py \
    --dataset-path $TEP_DATASET_PATH \
    --episodes 50 \
    --num-seeds 20 \
    --causal-method pc \
    --variant-causal-method notears \
    --output-dir $OUTPUT_DIR/pc_vs_notears \
    --gpu

# Run A/B test comparing PC and Correlation algorithms
echo "Running A/B test comparing PC and Correlation algorithms..."
python examples/benchmarks/tep_ab_test.py \
    --dataset-path $TEP_DATASET_PATH \
    --episodes 50 \
    --num-seeds 20 \
    --causal-method pc \
    --variant-causal-method correlation \
    --output-dir $OUTPUT_DIR/pc_vs_correlation \
    --gpu

# Run A/B test comparing PC and Sparse algorithms
echo "Running A/B test comparing PC and Sparse algorithms..."
python examples/benchmarks/tep_ab_test.py \
    --dataset-path $TEP_DATASET_PATH \
    --episodes 50 \
    --num-seeds 20 \
    --causal-method pc \
    --variant-causal-method sparse \
    --output-dir $OUTPUT_DIR/pc_vs_sparse \
    --gpu

echo "A/B testing completed. Results saved to $OUTPUT_DIR"
echo "Open the HTML reports in each subdirectory to view the results"
