version: '3'

services:
  tci:
    build: .
    image: tci:latest
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    command: python -c "import tci; print(f'TCI Framework version {tci.__version__}')"

  tci-jupyter:
    build: .
    image: tci:latest
    volumes:
      - .:/app
    ports:
      - "8888:8888"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    command: jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password=''

  tci-test:
    build: .
    image: tci:latest
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    command: pytest --cov=tci tests/

  tci-benchmark:
    build: .
    image: tci:latest
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    command: python scripts/run_benchmarks.py --all
