#!/usr/bin/env python3
"""
Retrain TCI-fix Models with External Data

This script retrains existing TCI-fix models to include the new external API data
(industrial production, automotive production, manufacturing data).

This will enable the models to use external economic factors in their predictions.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def retrain_single_model(customer, product, backup_old_model=True):
    """
    Retrain a single TCI-fix model with external data.
    
    Parameters:
    -----------
    customer : str
        Customer name
    product : str
        Product name
    backup_old_model : bool
        Whether to backup the old model before retraining
        
    Returns:
    --------
    tuple
        (success, message, model_info)
    """
    print(f"\n🔄 Retraining model: {customer} - {product}")
    print("=" * 60)
    
    try:
        # Import required modules
        from tci_fix_integration import TCIFixIntegration
        
        # Create TCI-fix integration instance
        tci_fix = TCIFixIntegration()
        
        # Check if old model exists
        old_model_path = os.path.join("models", f"tci_fix_{customer}_{product}.pkl")
        
        if os.path.exists(old_model_path) and backup_old_model:
            # Backup old model
            backup_dir = "models/backup_before_external_data"
            os.makedirs(backup_dir, exist_ok=True)
            backup_path = os.path.join(backup_dir, f"tci_fix_{customer}_{product}.pkl")
            
            import shutil
            shutil.copy2(old_model_path, backup_path)
            print(f"✅ Backed up old model to: {backup_path}")
        
        # Train new model with external data
        print("🤖 Training new model with external data...")
        success, message = tci_fix.train_model(customer, product)
        
        if success:
            print(f"✅ {message}")
            
            # Load the new model to check features
            try:
                from tci_fix import TCIFixPredictor
                new_model = TCIFixPredictor.load(old_model_path)
                
                if hasattr(new_model, 'feature_columns'):
                    total_features = len(new_model.feature_columns)
                    external_features = [f for f in new_model.feature_columns if any(keyword in f.lower() 
                                       for keyword in ['industrial', 'automotive', 'manufacturing', 'jp_', 'id_'])]
                    
                    model_info = {
                        'total_features': total_features,
                        'external_features': len(external_features),
                        'external_feature_names': external_features[:5]  # First 5 for display
                    }
                    
                    print(f"📊 New model features:")
                    print(f"   Total features: {total_features}")
                    print(f"   External features: {len(external_features)}")
                    print(f"   Sample external features: {external_features[:3]}")
                    
                    return True, message, model_info
                else:
                    return True, message, {'total_features': 'unknown', 'external_features': 'unknown'}
                    
            except Exception as e:
                print(f"⚠️ Could not analyze new model features: {e}")
                return True, message, {'total_features': 'unknown', 'external_features': 'unknown'}
        else:
            print(f"❌ {message}")
            return False, message, None
            
    except Exception as e:
        error_msg = f"Error retraining model: {str(e)}"
        print(f"❌ {error_msg}")
        return False, error_msg, None

def retrain_all_models():
    """
    Retrain all existing TCI-fix models with external data.
    """
    print("🔄 Retraining All TCI-fix Models with External Data")
    print("=" * 70)
    
    # Find all existing TCI-fix models
    models_dir = "models"
    if not os.path.exists(models_dir):
        print("❌ No models directory found")
        return
    
    model_files = [f for f in os.listdir(models_dir) if f.startswith("tci_fix_") and f.endswith(".pkl")]
    
    if not model_files:
        print("❌ No TCI-fix models found")
        return
    
    print(f"📁 Found {len(model_files)} TCI-fix models to retrain")
    
    results = []
    
    for model_file in model_files:
        # Extract customer and product from filename
        # Format: tci_fix_{customer}_{product}.pkl
        name_part = model_file.replace("tci_fix_", "").replace(".pkl", "")
        
        # Split by underscore, but be careful with customer/product names that contain underscores
        parts = name_part.split("_")
        
        # Try to find the split point between customer and product
        # This is tricky because both can contain underscores
        # We'll use a heuristic: look for common product patterns
        
        customer = None
        product = None
        
        for i in range(1, len(parts)):
            potential_customer = "_".join(parts[:i])
            potential_product = "_".join(parts[i:])
            
            # Check if this looks like a valid split
            # (This is a heuristic and might need adjustment)
            if any(keyword in potential_product.upper() for keyword in 
                   ['SEAL', 'RING', 'VALVE', 'ASSY', 'COMPACT', 'FLOATING', 'MECHANICAL']):
                customer = potential_customer
                product = potential_product
                break
        
        if customer is None or product is None:
            print(f"⚠️ Could not parse customer/product from: {model_file}")
            continue
        
        print(f"\n📋 Processing: {customer} - {product}")
        
        # Retrain the model
        success, message, model_info = retrain_single_model(customer, product)
        
        results.append({
            'customer': customer,
            'product': product,
            'success': success,
            'message': message,
            'model_info': model_info
        })
    
    # Summary
    print(f"\n📊 RETRAINING SUMMARY")
    print("=" * 70)
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"✅ Successfully retrained: {len(successful)}/{len(results)} models")
    print(f"❌ Failed to retrain: {len(failed)} models")
    
    if successful:
        print(f"\n✅ SUCCESSFUL RETRAINING:")
        for result in successful[:5]:  # Show first 5
            info = result['model_info']
            if info:
                print(f"   {result['customer']} - {result['product']}")
                print(f"     Features: {info.get('total_features', 'unknown')} total, {info.get('external_features', 'unknown')} external")
        
        if len(successful) > 5:
            print(f"   ... and {len(successful) - 5} more")
    
    if failed:
        print(f"\n❌ FAILED RETRAINING:")
        for result in failed:
            print(f"   {result['customer']} - {result['product']}: {result['message']}")
    
    return results

def retrain_specific_model():
    """
    Retrain a specific model interactively.
    """
    print("🎯 Retrain Specific TCI-fix Model")
    print("=" * 50)
    
    # List available models
    models_dir = "models"
    if not os.path.exists(models_dir):
        print("❌ No models directory found")
        return
    
    model_files = [f for f in os.listdir(models_dir) if f.startswith("tci_fix_") and f.endswith(".pkl")]
    
    if not model_files:
        print("❌ No TCI-fix models found")
        return
    
    print(f"📁 Available models:")
    for i, model_file in enumerate(model_files, 1):
        print(f"   {i}. {model_file}")
    
    # For demo purposes, let's retrain the ADVIK model
    target_file = None
    for model_file in model_files:
        if "ADVIK" in model_file and "BB-0030451" in model_file:
            target_file = model_file
            break
    
    if target_file:
        print(f"\n🎯 Retraining ADVIK model: {target_file}")
        
        # Extract customer and product
        name_part = target_file.replace("tci_fix_", "").replace(".pkl", "")
        # For ADVIK model, we know the format
        customer = "ADVIK (IND)"
        product = "MECHANICAL SEAL ( BB-0030451 )"
        
        success, message, model_info = retrain_single_model(customer, product)
        
        if success:
            print(f"\n🎉 SUCCESS!")
            print(f"The ADVIK model has been retrained with external data.")
            print(f"You can now generate new predictions that will include:")
            print(f"   - Industrial production data (Japan & Indonesia)")
            print(f"   - Automotive production data (Japan & Indonesia)")
            print(f"   - Manufacturing data (Japan & Indonesia)")
            print(f"   - Economic interaction features")
            
            if model_info:
                print(f"\nModel now has {model_info.get('external_features', 'unknown')} external features!")
        else:
            print(f"\n❌ Failed to retrain ADVIK model: {message}")
    else:
        print("❌ ADVIK model not found")

def main():
    """Main function to handle retraining options."""
    print("🔄 TCI-fix Model Retraining with External Data")
    print("=" * 70)
    print("This script will retrain your TCI-fix models to include external API data.")
    print("This will enable predictions to use economic factors like industrial")
    print("production, automotive production, and manufacturing data.\n")
    
    # Check if we're in the right directory
    if not os.path.exists("models") or not os.path.exists("tci_fix.py"):
        print("❌ Please run this script from the project root directory")
        return
    
    print("Choose an option:")
    print("1. Retrain specific model (ADVIK demo)")
    print("2. Retrain all models")
    print("3. Exit")
    
    # For automation, let's default to option 1
    choice = "1"
    
    if choice == "1":
        retrain_specific_model()
    elif choice == "2":
        retrain_all_models()
    elif choice == "3":
        print("👋 Exiting...")
        return
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Retraining interrupted by user")
    except Exception as e:
        print(f"\n❌ Retraining failed with error: {e}")
        import traceback
        traceback.print_exc()
