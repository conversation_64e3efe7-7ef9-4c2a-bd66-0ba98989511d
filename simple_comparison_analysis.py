#!/usr/bin/env python3
"""
Simple Comparison Analysis

Analyze the comparison.xlsx file to understand prediction performance patterns.
"""

import pandas as pd
import numpy as np
import os

def analyze_comparison():
    """Analyze the comparison file with robust error handling."""
    print("📊 Comparison.xlsx Analysis - Prediction Performance")
    print("=" * 70)
    
    try:
        # Read the file
        file_path = "results/comparison/Comparison.xlsx"
        df = pd.read_excel(file_path)
        
        print(f"📋 Raw data shape: {df.shape}")
        print(f"📋 Columns: {list(df.columns)}")
        
        # Clean the data
        # Check if first row has headers
        if 'Date' not in str(df.columns[0]):
            # First row might be headers
            df.columns = df.iloc[0]
            df = df.drop(0).reset_index(drop=True)
        
        # Remove empty rows
        df = df.dropna(how='all')
        
        # Standardize column names
        df.columns = ['Date', 'Quantity', 'Predicted']
        
        # Convert to numeric
        df['Quantity'] = pd.to_numeric(df['Quantity'], errors='coerce')
        df['Predicted'] = pd.to_numeric(df['Predicted'], errors='coerce')
        
        # Remove rows with missing data
        df = df.dropna()
        
        print(f"📊 Clean data: {len(df)} predictions")
        
        if len(df) == 0:
            print("❌ No valid data found")
            return
        
        # Basic statistics
        actual = df['Quantity']
        predicted = df['Predicted']
        
        print(f"\n📈 Basic Statistics:")
        print(f"   Actual range: {actual.min():,.0f} to {actual.max():,.0f}")
        print(f"   Predicted range: {predicted.min():,.0f} to {predicted.max():,.0f}")
        print(f"   Actual mean: {actual.mean():,.0f}")
        print(f"   Predicted mean: {predicted.mean():,.0f}")
        
        # Calculate errors
        errors = np.abs(actual - predicted)
        error_pcts = np.abs((actual - predicted) / actual) * 100
        
        # Remove infinite values
        error_pcts_clean = error_pcts[np.isfinite(error_pcts)]
        
        print(f"\n🎯 Prediction Accuracy:")
        print(f"   MAE (Mean Absolute Error): {errors.mean():,.0f}")
        print(f"   Median Error %: {np.median(error_pcts_clean):.1f}%")
        print(f"   Best prediction error: {error_pcts_clean.min():.1f}%")
        print(f"   Worst prediction error: {error_pcts_clean.max():.1f}%")
        
        # Quality distribution
        excellent = (error_pcts_clean < 10).sum()
        good = ((error_pcts_clean >= 10) & (error_pcts_clean < 20)).sum()
        fair = ((error_pcts_clean >= 20) & (error_pcts_clean < 30)).sum()
        poor = (error_pcts_clean >= 30).sum()
        total = len(error_pcts_clean)
        
        print(f"\n📊 Prediction Quality Distribution:")
        print(f"   🟢 Excellent (<10% error): {excellent}/{total} ({excellent/total*100:.1f}%)")
        print(f"   🟡 Good (10-20% error): {good}/{total} ({good/total*100:.1f}%)")
        print(f"   🟠 Fair (20-30% error): {fair}/{total} ({fair/total*100:.1f}%)")
        print(f"   🔴 Poor (>30% error): {poor}/{total} ({poor/total*100:.1f}%)")
        
        # Show some examples
        print(f"\n📋 Sample Predictions:")
        for i in range(min(10, len(df))):
            actual_val = actual.iloc[i]
            pred_val = predicted.iloc[i]
            error_pct = abs((actual_val - pred_val) / actual_val) * 100
            status = "🟢" if error_pct < 10 else "🟡" if error_pct < 20 else "🟠" if error_pct < 30 else "🔴"
            print(f"   {status} Actual: {actual_val:6,.0f}, Predicted: {pred_val:6,.0f}, Error: {error_pct:5.1f}%")
        
        # Analyze patterns
        analyze_patterns(df)
        
        # Provide recommendations
        provide_recommendations(error_pcts_clean, actual, predicted)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def analyze_patterns(df):
    """Analyze patterns in the prediction data."""
    print(f"\n🔍 Pattern Analysis:")
    
    actual = df['Quantity']
    predicted = df['Predicted']
    
    # Trend analysis
    if len(df) > 1:
        actual_trend = np.polyfit(range(len(actual)), actual, 1)[0]
        predicted_trend = np.polyfit(range(len(predicted)), predicted, 1)[0]
        
        print(f"   📈 Trends:")
        print(f"      Actual trend: {actual_trend:+,.0f} units/period")
        print(f"      Predicted trend: {predicted_trend:+,.0f} units/period")
        
        if abs(actual_trend) > 100:  # Significant trend
            trend_accuracy = 1 - abs(actual_trend - predicted_trend) / abs(actual_trend)
            print(f"      Trend capture accuracy: {trend_accuracy:.1%}")
    
    # Volatility analysis
    actual_cv = actual.std() / actual.mean()
    predicted_cv = predicted.std() / predicted.mean()
    
    print(f"   📊 Volatility:")
    print(f"      Actual volatility (CV): {actual_cv:.2f}")
    print(f"      Predicted volatility (CV): {predicted_cv:.2f}")
    
    if actual_cv > 0.5:
        print(f"      ⚠️ High volatility product - challenging to predict")
    
    # Bias analysis
    bias = (predicted - actual).mean()
    bias_pct = bias / actual.mean() * 100
    
    print(f"   🎯 Bias Analysis:")
    print(f"      Average bias: {bias:+,.0f} units ({bias_pct:+.1f}%)")
    
    if abs(bias_pct) > 10:
        if bias_pct > 0:
            print(f"      ⚠️ Model tends to OVER-predict")
        else:
            print(f"      ⚠️ Model tends to UNDER-predict")

def provide_recommendations(error_pcts, actual, predicted):
    """Provide specific recommendations based on the analysis."""
    print(f"\n💡 IMPROVEMENT RECOMMENDATIONS")
    print("=" * 50)
    
    # Overall performance assessment
    excellent_pct = (error_pcts < 10).sum() / len(error_pcts) * 100
    poor_pct = (error_pcts >= 30).sum() / len(error_pcts) * 100
    median_error = np.median(error_pcts)
    
    print(f"🎯 Current Performance:")
    print(f"   - {excellent_pct:.1f}% excellent predictions")
    print(f"   - {poor_pct:.1f}% poor predictions")
    print(f"   - {median_error:.1f}% median error")
    
    print(f"\n🔧 Priority Improvements:")
    
    # Priority 1: If too many poor predictions
    if poor_pct > 25:
        print(f"1. 🚨 HIGH PRIORITY - Reduce Poor Predictions ({poor_pct:.1f}%)")
        print(f"   - Target: <20% poor predictions")
        print(f"   - Actions:")
        print(f"     • Add more training data")
        print(f"     • Improve data quality (remove outliers)")
        print(f"     • Add product-specific features")
        print(f"     • Consider ensemble methods")
    
    # Priority 2: If not enough excellent predictions
    if excellent_pct < 40:
        print(f"2. 📈 MEDIUM PRIORITY - Increase Excellent Predictions ({excellent_pct:.1f}%)")
        print(f"   - Target: >40% excellent predictions")
        print(f"   - Actions:")
        print(f"     • Optimize hyperparameters")
        print(f"     • Enhance feature engineering")
        print(f"     • Add external data sources")
        print(f"     • Implement uncertainty quantification")
    
    # Priority 3: If median error is high
    if median_error > 20:
        print(f"3. 🎯 MEDIUM PRIORITY - Reduce Overall Error ({median_error:.1f}%)")
        print(f"   - Target: <15% median error")
        print(f"   - Actions:")
        print(f"     • Review model architecture")
        print(f"     • Add cross-validation")
        print(f"     • Implement adaptive learning")
        print(f"     • Consider different algorithms")
    
    # Specific technical recommendations
    print(f"\n🛠️ Technical Improvements:")
    
    # Volatility-based recommendations
    actual_cv = actual.std() / actual.mean()
    if actual_cv > 0.5:
        print(f"   📊 High Volatility Product (CV={actual_cv:.2f}):")
        print(f"     • Use uncertainty quantification")
        print(f"     • Implement regime-switching models")
        print(f"     • Add volatility-specific features")
        print(f"     • Consider ensemble of multiple models")
    
    # Bias-based recommendations
    bias_pct = ((predicted - actual).mean() / actual.mean()) * 100
    if abs(bias_pct) > 10:
        print(f"   🎯 Systematic Bias ({bias_pct:+.1f}%):")
        if bias_pct > 0:
            print(f"     • Model over-predicts - add regularization")
            print(f"     • Check for data leakage")
            print(f"     • Reduce model complexity")
        else:
            print(f"     • Model under-predicts - add more features")
            print(f"     • Increase model capacity")
            print(f"     • Check for missing patterns")
    
    # Advanced recommendations
    print(f"\n🚀 Advanced Techniques:")
    print(f"   • Multi-task learning across similar products")
    print(f"   • Transfer learning from high-performing models")
    print(f"   • Hierarchical forecasting (customer → product)")
    print(f"   • Dynamic model selection based on recent performance")
    print(f"   • Ensemble of TCI-fix + Prophet + ARIMA")
    print(f"   • Real-time model updating with new data")
    
    # Success criteria
    print(f"\n🎯 Success Targets:")
    print(f"   • >50% excellent predictions (<10% error)")
    print(f"   • <15% poor predictions (>30% error)")
    print(f"   • <15% median error")
    print(f"   • <5% systematic bias")

if __name__ == "__main__":
    analyze_comparison()
