"""
Checkpointing utilities for the TCI framework.

This module provides utilities for saving and loading checkpoints during training,
allowing for resuming training from a saved state.
"""

import os
import time
import json
import torch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
import glob
import shutil

from tci.exceptions import ModelError


class CheckpointManager:
    """
    Manager for model checkpoints.
    
    This class provides functionality for saving, loading, and managing model checkpoints,
    allowing for resuming training from a saved state.
    
    Attributes
    ----------
    checkpoint_dir : str
        Directory to save checkpoints
    max_to_keep : int
        Maximum number of checkpoints to keep
    keep_best : bool
        Whether to keep the best checkpoint
    metric_name : str
        Name of the metric to use for determining the best checkpoint
    metric_mode : str
        Mode for the metric ('max' or 'min')
    checkpoints : List[Dict[str, Any]]
        List of checkpoint metadata
    best_metric : Optional[float]
        Best metric value
    best_checkpoint : Optional[str]
        Path to the best checkpoint
    """
    
    def __init__(self, checkpoint_dir: str, max_to_keep: int = 5, keep_best: bool = True,
                metric_name: str = 'reward', metric_mode: str = 'max'):
        """
        Initialize the checkpoint manager.
        
        Parameters
        ----------
        checkpoint_dir : str
            Directory to save checkpoints
        max_to_keep : int, optional
            Maximum number of checkpoints to keep, by default 5
        keep_best : bool, optional
            Whether to keep the best checkpoint, by default True
        metric_name : str, optional
            Name of the metric to use for determining the best checkpoint, by default 'reward'
        metric_mode : str, optional
            Mode for the metric ('max' or 'min'), by default 'max'
        """
        self.checkpoint_dir = checkpoint_dir
        self.max_to_keep = max_to_keep
        self.keep_best = keep_best
        self.metric_name = metric_name
        self.metric_mode = metric_mode
        
        # Create checkpoint directory if it doesn't exist
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Initialize checkpoint list
        self.checkpoints = []
        
        # Initialize best metric
        self.best_metric = None
        self.best_checkpoint = None
        
        # Load existing checkpoints
        self._load_checkpoint_metadata()
    
    def _load_checkpoint_metadata(self):
        """
        Load metadata for existing checkpoints.
        """
        # Check if metadata file exists
        metadata_path = os.path.join(self.checkpoint_dir, 'checkpoint_metadata.json')
        if os.path.isfile(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                self.checkpoints = metadata.get('checkpoints', [])
                self.best_metric = metadata.get('best_metric')
                self.best_checkpoint = metadata.get('best_checkpoint')
            except Exception as e:
                print(f"Error loading checkpoint metadata: {e}")
                self.checkpoints = []
                self.best_metric = None
                self.best_checkpoint = None
    
    def _save_checkpoint_metadata(self):
        """
        Save metadata for existing checkpoints.
        """
        metadata_path = os.path.join(self.checkpoint_dir, 'checkpoint_metadata.json')
        
        metadata = {
            'checkpoints': self.checkpoints,
            'best_metric': self.best_metric,
            'best_checkpoint': self.best_checkpoint
        }
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def _is_better(self, metric_value: float) -> bool:
        """
        Check if a metric value is better than the current best.
        
        Parameters
        ----------
        metric_value : float
            Metric value to check
        
        Returns
        -------
        bool
            Whether the metric value is better than the current best
        """
        if self.best_metric is None:
            return True
        
        if self.metric_mode == 'max':
            return metric_value > self.best_metric
        else:
            return metric_value < self.best_metric
    
    def save_checkpoint(self, model: Any, optimizer: Optional[Any] = None,
                       metrics: Optional[Dict[str, Any]] = None, step: int = 0,
                       extra_data: Optional[Dict[str, Any]] = None) -> str:
        """
        Save a checkpoint.
        
        Parameters
        ----------
        model : Any
            Model to save
        optimizer : Optional[Any], optional
            Optimizer to save, by default None
        metrics : Optional[Dict[str, Any]], optional
            Metrics to save, by default None
        step : int, optional
            Current step, by default 0
        extra_data : Optional[Dict[str, Any]], optional
            Extra data to save, by default None
        
        Returns
        -------
        str
            Path to the saved checkpoint
        """
        # Create checkpoint filename
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        checkpoint_name = f"checkpoint_{timestamp}_step{step}.pt"
        checkpoint_path = os.path.join(self.checkpoint_dir, checkpoint_name)
        
        # Create checkpoint data
        checkpoint_data = {
            'model_state_dict': model.state_dict(),
            'step': step,
            'timestamp': timestamp
        }
        
        # Add optimizer state if provided
        if optimizer is not None:
            checkpoint_data['optimizer_state_dict'] = optimizer.state_dict()
        
        # Add metrics if provided
        if metrics is not None:
            checkpoint_data['metrics'] = metrics
        
        # Add extra data if provided
        if extra_data is not None:
            checkpoint_data['extra_data'] = extra_data
        
        # Save checkpoint
        torch.save(checkpoint_data, checkpoint_path)
        
        # Update checkpoint list
        checkpoint_info = {
            'path': checkpoint_path,
            'step': step,
            'timestamp': timestamp,
            'metrics': metrics
        }
        
        self.checkpoints.append(checkpoint_info)
        
        # Update best checkpoint if needed
        if metrics is not None and self.metric_name in metrics:
            metric_value = metrics[self.metric_name]
            if self._is_better(metric_value):
                self.best_metric = metric_value
                self.best_checkpoint = checkpoint_path
                
                # Create a copy of the best checkpoint if keep_best is True
                if self.keep_best:
                    best_path = os.path.join(self.checkpoint_dir, 'best_checkpoint.pt')
                    shutil.copy(checkpoint_path, best_path)
        
        # Remove old checkpoints if needed
        self._cleanup_old_checkpoints()
        
        # Save metadata
        self._save_checkpoint_metadata()
        
        return checkpoint_path
    
    def _cleanup_old_checkpoints(self):
        """
        Remove old checkpoints if there are more than max_to_keep.
        """
        if len(self.checkpoints) <= self.max_to_keep:
            return
        
        # Sort checkpoints by step
        sorted_checkpoints = sorted(self.checkpoints, key=lambda x: x['step'])
        
        # Remove oldest checkpoints
        checkpoints_to_remove = sorted_checkpoints[:len(sorted_checkpoints) - self.max_to_keep]
        
        for checkpoint in checkpoints_to_remove:
            # Skip best checkpoint if keep_best is True
            if self.keep_best and checkpoint['path'] == self.best_checkpoint:
                continue
            
            # Remove checkpoint file
            if os.path.isfile(checkpoint['path']):
                os.remove(checkpoint['path'])
            
            # Remove from checkpoint list
            self.checkpoints.remove(checkpoint)
    
    def load_checkpoint(self, model: Any, optimizer: Optional[Any] = None,
                       checkpoint_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load a checkpoint.
        
        Parameters
        ----------
        model : Any
            Model to load the checkpoint into
        optimizer : Optional[Any], optional
            Optimizer to load the checkpoint into, by default None
        checkpoint_path : Optional[str], optional
            Path to the checkpoint to load, by default None (load latest)
        
        Returns
        -------
        Dict[str, Any]
            Checkpoint data
        
        Raises
        ------
        ModelError
            If the checkpoint cannot be loaded
        """
        # Determine checkpoint path
        if checkpoint_path is None:
            # Load latest checkpoint
            if not self.checkpoints:
                raise ModelError("No checkpoints found")
            
            # Sort checkpoints by step
            sorted_checkpoints = sorted(self.checkpoints, key=lambda x: x['step'], reverse=True)
            checkpoint_path = sorted_checkpoints[0]['path']
        
        # Check if checkpoint exists
        if not os.path.isfile(checkpoint_path):
            raise ModelError(f"Checkpoint not found: {checkpoint_path}")
        
        # Load checkpoint
        try:
            checkpoint = torch.load(checkpoint_path, map_location=torch.device('cpu'))
        except Exception as e:
            raise ModelError(f"Error loading checkpoint: {e}")
        
        # Load model state
        try:
            model.load_state_dict(checkpoint['model_state_dict'])
        except Exception as e:
            raise ModelError(f"Error loading model state: {e}")
        
        # Load optimizer state if provided
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            try:
                optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            except Exception as e:
                print(f"Warning: Error loading optimizer state: {e}")
        
        return checkpoint
    
    def load_best_checkpoint(self, model: Any, optimizer: Optional[Any] = None) -> Dict[str, Any]:
        """
        Load the best checkpoint.
        
        Parameters
        ----------
        model : Any
            Model to load the checkpoint into
        optimizer : Optional[Any], optional
            Optimizer to load the checkpoint into, by default None
        
        Returns
        -------
        Dict[str, Any]
            Checkpoint data
        
        Raises
        ------
        ModelError
            If the best checkpoint cannot be loaded
        """
        if self.best_checkpoint is None:
            raise ModelError("No best checkpoint found")
        
        return self.load_checkpoint(model, optimizer, self.best_checkpoint)
    
    def get_latest_checkpoint(self) -> Optional[str]:
        """
        Get the path to the latest checkpoint.
        
        Returns
        -------
        Optional[str]
            Path to the latest checkpoint, or None if no checkpoints exist
        """
        if not self.checkpoints:
            return None
        
        # Sort checkpoints by step
        sorted_checkpoints = sorted(self.checkpoints, key=lambda x: x['step'], reverse=True)
        return sorted_checkpoints[0]['path']
    
    def get_best_checkpoint(self) -> Optional[str]:
        """
        Get the path to the best checkpoint.
        
        Returns
        -------
        Optional[str]
            Path to the best checkpoint, or None if no best checkpoint exists
        """
        return self.best_checkpoint
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """
        Get a list of all checkpoints.
        
        Returns
        -------
        List[Dict[str, Any]]
            List of checkpoint metadata
        """
        return self.checkpoints


def save_checkpoint(model: Any, checkpoint_path: str, optimizer: Optional[Any] = None,
                  metrics: Optional[Dict[str, Any]] = None, step: int = 0,
                  extra_data: Optional[Dict[str, Any]] = None) -> None:
    """
    Save a checkpoint.
    
    Parameters
    ----------
    model : Any
        Model to save
    checkpoint_path : str
        Path to save the checkpoint
    optimizer : Optional[Any], optional
        Optimizer to save, by default None
    metrics : Optional[Dict[str, Any]], optional
        Metrics to save, by default None
    step : int, optional
        Current step, by default 0
    extra_data : Optional[Dict[str, Any]], optional
        Extra data to save, by default None
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(checkpoint_path)), exist_ok=True)
    
    # Create checkpoint data
    checkpoint_data = {
        'model_state_dict': model.state_dict(),
        'step': step,
        'timestamp': time.time()
    }
    
    # Add optimizer state if provided
    if optimizer is not None:
        checkpoint_data['optimizer_state_dict'] = optimizer.state_dict()
    
    # Add metrics if provided
    if metrics is not None:
        checkpoint_data['metrics'] = metrics
    
    # Add extra data if provided
    if extra_data is not None:
        checkpoint_data['extra_data'] = extra_data
    
    # Save checkpoint
    torch.save(checkpoint_data, checkpoint_path)


def load_checkpoint(model: Any, checkpoint_path: str, optimizer: Optional[Any] = None) -> Dict[str, Any]:
    """
    Load a checkpoint.
    
    Parameters
    ----------
    model : Any
        Model to load the checkpoint into
    checkpoint_path : str
        Path to the checkpoint to load
    optimizer : Optional[Any], optional
        Optimizer to load the checkpoint into, by default None
    
    Returns
    -------
    Dict[str, Any]
        Checkpoint data
    
    Raises
    ------
    ModelError
        If the checkpoint cannot be loaded
    """
    # Check if checkpoint exists
    if not os.path.isfile(checkpoint_path):
        raise ModelError(f"Checkpoint not found: {checkpoint_path}")
    
    # Load checkpoint
    try:
        checkpoint = torch.load(checkpoint_path, map_location=torch.device('cpu'))
    except Exception as e:
        raise ModelError(f"Error loading checkpoint: {e}")
    
    # Load model state
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
    except Exception as e:
        raise ModelError(f"Error loading model state: {e}")
    
    # Load optimizer state if provided
    if optimizer is not None and 'optimizer_state_dict' in checkpoint:
        try:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        except Exception as e:
            print(f"Warning: Error loading optimizer state: {e}")
    
    return checkpoint


def find_latest_checkpoint(checkpoint_dir: str) -> Optional[str]:
    """
    Find the latest checkpoint in a directory.
    
    Parameters
    ----------
    checkpoint_dir : str
        Directory to search for checkpoints
    
    Returns
    -------
    Optional[str]
        Path to the latest checkpoint, or None if no checkpoints exist
    """
    # Check if directory exists
    if not os.path.isdir(checkpoint_dir):
        return None
    
    # Find all checkpoint files
    checkpoint_files = glob.glob(os.path.join(checkpoint_dir, 'checkpoint_*.pt'))
    
    if not checkpoint_files:
        return None
    
    # Sort by modification time (newest first)
    checkpoint_files.sort(key=os.path.getmtime, reverse=True)
    
    return checkpoint_files[0]
