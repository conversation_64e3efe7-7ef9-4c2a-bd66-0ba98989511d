#!/usr/bin/env python3
"""
Implement Enhanced TCI-fix System

This script implements the enhanced TCI-fix system in production by:
1. Creating enhanced integration module
2. Updating GUI to support enhanced models
3. Migrating existing models to enhanced versions
4. Testing the implementation
"""

import os
import shutil
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def implement_enhanced_system():
    """Implement the enhanced TCI-fix system in production."""
    print("🚀 Implementing Enhanced TCI-fix System")
    print("=" * 60)
    
    try:
        # Step 1: Create production-ready enhanced integration
        print("\n📦 Step 1: Creating Production Enhanced Integration")
        create_production_enhanced_integration()
        
        # Step 2: Update model integration to support enhanced models
        print("\n🔧 Step 2: Updating Model Integration")
        update_model_integration()
        
        # Step 3: Create enhanced model training script
        print("\n🤖 Step 3: Creating Enhanced Model Training")
        create_enhanced_training_script()
        
        # Step 4: Update GUI to support enhanced models
        print("\n🖥️ Step 4: Updating GUI")
        update_gui_for_enhanced_models()
        
        # Step 5: Test the implementation
        print("\n🧪 Step 5: Testing Implementation")
        test_enhanced_implementation()
        
        print("\n🎉 Enhanced TCI-fix Implementation Complete!")
        print("=" * 60)
        print("✅ Enhanced integration created")
        print("✅ Model integration updated")
        print("✅ Training scripts ready")
        print("✅ GUI updated")
        print("✅ Implementation tested")
        
        print("\n📋 Next Steps:")
        print("1. Train enhanced models: python train_enhanced_models.py")
        print("2. Test predictions: Use GUI with 'Enhanced TCI-fix' option")
        print("3. Compare results: Check enhanced vs original predictions")
        
    except Exception as e:
        print(f"❌ Implementation failed: {e}")
        import traceback
        traceback.print_exc()

def create_production_enhanced_integration():
    """Create production-ready enhanced integration."""
    
    enhanced_integration_code = '''#!/usr/bin/env python3
"""
Production Enhanced TCI-fix Integration

Production-ready integration for enhanced TCI-fix with bias correction,
uncertainty quantification, and ensemble approach.
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import pickle

from tci_fix_integration import TCIFixIntegration, load_external_data
from folder_data_manager import FolderDataManager

class ProductionEnhancedTCIFix:
    """
    Production-ready enhanced TCI-fix integration.
    
    Features:
    - Bias correction based on recent performance
    - Uncertainty quantification with confidence bounds
    - Ensemble approach combining multiple methods
    - Volatility-aware modeling
    - Production-ready error handling
    """
    
    def __init__(self):
        """Initialize the production enhanced TCI-fix."""
        self.logger = logging.getLogger(__name__)
        self.base_integration = TCIFixIntegration()
        self.data_manager = FolderDataManager("dataset")
        
        # Enhanced model parameters
        self.bias_correction_enabled = True
        self.uncertainty_quantification_enabled = True
        self.ensemble_enabled = True
        
        # Performance tracking
        self.performance_log = []
    
    def train_enhanced_model(self, customer, product, force_retrain=False):
        """
        Train an enhanced model for a customer-product combination.
        
        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name
        force_retrain : bool
            Whether to force retraining
            
        Returns:
        --------
        tuple
            (success, message, model_info)
        """
        try:
            self.logger.info(f"Training enhanced model for {customer} - {product}")
            
            # First train the base TCI-fix model
            success, message = self.base_integration.train_model(customer, product, force_retrain)
            
            if not success:
                return False, f"Base model training failed: {message}", None
            
            # Load data for enhanced analysis
            success, load_message = self.data_manager.load_data_from_folder()
            if not success:
                return False, f"Data loading failed: {load_message}", None
            
            key = f"{customer}_{product}"
            if key not in self.data_manager.data:
                return False, f"No data found for {customer} - {product}", None
            
            data = self.data_manager.data[key].copy()
            
            # Calculate enhanced model parameters
            enhanced_params = self._calculate_enhanced_parameters(data)
            
            # Save enhanced parameters
            enhanced_model_path = self._get_enhanced_model_path(customer, product)
            os.makedirs(os.path.dirname(enhanced_model_path), exist_ok=True)
            
            with open(enhanced_model_path, 'wb') as f:
                pickle.dump(enhanced_params, f)
            
            self.logger.info(f"Enhanced model parameters saved: {enhanced_model_path}")
            
            return True, f"Enhanced model trained successfully", enhanced_params
            
        except Exception as e:
            error_msg = f"Error training enhanced model: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
    
    def predict_enhanced(self, customer, product, periods=12, start_date=None):
        """
        Generate enhanced predictions.
        
        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name
        periods : int
            Number of periods to predict
        start_date : str or datetime
            Start date for predictions
            
        Returns:
        --------
        pandas.DataFrame or None
            Enhanced predictions with uncertainty bounds
        """
        try:
            self.logger.info(f"Generating enhanced predictions for {customer} - {product}")
            
            # Get base predictions
            base_predictions = self.base_integration.predict_future(customer, product, periods, start_date)
            
            if base_predictions is None:
                self.logger.error("Base predictions failed")
                return None
            
            # Load enhanced parameters
            enhanced_params = self._load_enhanced_parameters(customer, product)
            
            if enhanced_params is None:
                self.logger.warning("No enhanced parameters found, using base predictions")
                return self._add_basic_enhancements(base_predictions)
            
            # Apply enhancements
            enhanced_predictions = self._apply_enhancements(base_predictions, enhanced_params)
            
            # Add metadata
            enhanced_predictions['model_type'] = 'enhanced_tci_fix'
            enhanced_predictions['enhancement_version'] = '1.0'
            enhanced_predictions['prediction_timestamp'] = datetime.now()
            
            self.logger.info(f"Enhanced predictions generated: {len(enhanced_predictions)} periods")
            
            return enhanced_predictions
            
        except Exception as e:
            self.logger.error(f"Error generating enhanced predictions: {str(e)}")
            return None
    
    def _calculate_enhanced_parameters(self, data):
        """Calculate enhanced model parameters from historical data."""
        
        target_col = 'Quantity'
        
        # Calculate volatility level
        cv = data[target_col].std() / (data[target_col].mean() + 1e-6)
        
        if cv > 1.0:
            volatility_level = 'high'
            uncertainty_factor = 0.40
            ensemble_weights = [0.5, 0.3, 0.2]  # Conservative
        elif cv > 0.5:
            volatility_level = 'medium'
            uncertainty_factor = 0.25
            ensemble_weights = [0.6, 0.25, 0.15]  # Balanced
        else:
            volatility_level = 'low'
            uncertainty_factor = 0.15
            ensemble_weights = [0.7, 0.2, 0.1]  # Trust TCI-fix more
        
        # Calculate bias correction factor
        bias_correction_factor = self._calculate_bias_correction(data)
        
        # Calculate trend parameters
        recent_trend = self._calculate_recent_trend(data)
        
        enhanced_params = {
            'volatility_level': volatility_level,
            'uncertainty_factor': uncertainty_factor,
            'ensemble_weights': ensemble_weights,
            'bias_correction_factor': bias_correction_factor,
            'recent_trend': recent_trend,
            'data_mean': data[target_col].mean(),
            'data_std': data[target_col].std(),
            'zero_order_threshold': 100,
            'training_date': datetime.now(),
            'data_points': len(data)
        }
        
        return enhanced_params
    
    def _calculate_bias_correction(self, data):
        """Calculate bias correction factor."""
        try:
            # Simple approach: use recent data vs trend
            target_col = 'Quantity'
            recent_data = data.tail(12)  # Last 12 months
            
            if len(recent_data) < 6:
                return 1.0
            
            # Calculate trend-based expectation vs actual
            x = np.arange(len(recent_data))
            trend_coef = np.polyfit(x, recent_data[target_col], 1)[0]
            
            # If trend is positive but recent values are lower, apply upward correction
            recent_mean = recent_data[target_col].mean()
            overall_mean = data[target_col].mean()
            
            if recent_mean > 0 and overall_mean > 0:
                bias_factor = recent_mean / overall_mean
                # Limit to reasonable range
                bias_factor = np.clip(bias_factor, 0.7, 1.5)
                return bias_factor
            
            return 1.0
            
        except Exception:
            return 1.0
    
    def _calculate_recent_trend(self, data):
        """Calculate recent trend for ensemble component."""
        try:
            target_col = 'Quantity'
            recent_data = data.tail(6)  # Last 6 months
            
            if len(recent_data) < 3:
                return 0
            
            x = np.arange(len(recent_data))
            trend_coef = np.polyfit(x, recent_data[target_col], 1)[0]
            
            return trend_coef
            
        except Exception:
            return 0
    
    def _load_enhanced_parameters(self, customer, product):
        """Load enhanced parameters for a model."""
        try:
            enhanced_model_path = self._get_enhanced_model_path(customer, product)
            
            if not os.path.exists(enhanced_model_path):
                return None
            
            with open(enhanced_model_path, 'rb') as f:
                enhanced_params = pickle.load(f)
            
            return enhanced_params
            
        except Exception as e:
            self.logger.warning(f"Could not load enhanced parameters: {e}")
            return None
    
    def _apply_enhancements(self, base_predictions, enhanced_params):
        """Apply enhancements to base predictions."""
        
        enhanced_predictions = base_predictions.copy()
        
        # Find prediction column
        prediction_col = None
        for col in enhanced_predictions.columns:
            if 'predict' in col.lower() or 'forecast' in col.lower() or 'quantity' in col.lower():
                prediction_col = col
                break
        
        if prediction_col is None:
            return enhanced_predictions
        
        # Standardize column name
        if prediction_col != 'predicted_quantity':
            enhanced_predictions['predicted_quantity'] = enhanced_predictions[prediction_col]
        
        # Apply bias correction
        if self.bias_correction_enabled:
            enhanced_predictions['predicted_quantity'] *= enhanced_params['bias_correction_factor']
        
        # Apply ensemble approach
        if self.ensemble_enabled:
            enhanced_predictions = self._apply_ensemble(enhanced_predictions, enhanced_params)
        
        # Add uncertainty quantification
        if self.uncertainty_quantification_enabled:
            enhanced_predictions = self._add_uncertainty_bounds(enhanced_predictions, enhanced_params)
        
        return enhanced_predictions
    
    def _apply_ensemble(self, predictions, enhanced_params):
        """Apply ensemble approach."""
        
        tci_fix_pred = predictions['predicted_quantity'].copy()
        
        # Trend component
        trend_coef = enhanced_params['recent_trend']
        trend_predictions = []
        last_value = enhanced_params['data_mean']
        
        for i in range(len(predictions)):
            trend_pred = max(0, last_value + trend_coef * (i + 1))
            trend_predictions.append(trend_pred)
        
        trend_pred = np.array(trend_predictions)
        
        # Moving average component
        ma_pred = np.full(len(predictions), enhanced_params['data_mean'])
        
        # Apply ensemble weights
        weights = enhanced_params['ensemble_weights']
        
        ensemble_prediction = (
            weights[0] * tci_fix_pred +
            weights[1] * trend_pred +
            weights[2] * ma_pred
        )
        
        predictions['predicted_quantity'] = ensemble_prediction
        predictions['tci_fix_component'] = tci_fix_pred
        predictions['trend_component'] = trend_pred
        predictions['ma_component'] = ma_pred
        
        return predictions
    
    def _add_uncertainty_bounds(self, predictions, enhanced_params):
        """Add uncertainty bounds to predictions."""
        
        uncertainty_factor = enhanced_params['uncertainty_factor']
        
        predictions['lower_bound'] = predictions['predicted_quantity'] * (1 - uncertainty_factor)
        predictions['upper_bound'] = predictions['predicted_quantity'] * (1 + uncertainty_factor)
        predictions['confidence_level'] = 1 - uncertainty_factor
        predictions['uncertainty_factor'] = uncertainty_factor
        predictions['volatility_level'] = enhanced_params['volatility_level']
        
        # Ensure non-negative bounds
        predictions['lower_bound'] = np.maximum(predictions['lower_bound'], 0)
        
        return predictions
    
    def _add_basic_enhancements(self, base_predictions):
        """Add basic enhancements when no enhanced parameters are available."""
        
        enhanced_predictions = base_predictions.copy()
        
        # Find prediction column
        prediction_col = None
        for col in enhanced_predictions.columns:
            if 'predict' in col.lower() or 'forecast' in col.lower() or 'quantity' in col.lower():
                prediction_col = col
                break
        
        if prediction_col is None:
            return enhanced_predictions
        
        # Standardize column name
        if prediction_col != 'predicted_quantity':
            enhanced_predictions['predicted_quantity'] = enhanced_predictions[prediction_col]
        
        # Add basic uncertainty bounds (25%)
        uncertainty_factor = 0.25
        enhanced_predictions['lower_bound'] = enhanced_predictions['predicted_quantity'] * 0.75
        enhanced_predictions['upper_bound'] = enhanced_predictions['predicted_quantity'] * 1.25
        enhanced_predictions['confidence_level'] = 0.75
        enhanced_predictions['uncertainty_factor'] = uncertainty_factor
        enhanced_predictions['volatility_level'] = 'medium'
        enhanced_predictions['model_type'] = 'basic_enhanced_tci_fix'
        
        return enhanced_predictions
    
    def _get_enhanced_model_path(self, customer, product):
        """Get the file path for enhanced model parameters."""
        safe_customer = customer.replace("/", "_").replace("\\\\", "_")
        safe_product = product.replace("/", "_").replace("\\\\", "_")
        return f"models/enhanced_params_{safe_customer}_{safe_product}.pkl"
'''
    
    # Save the production enhanced integration
    with open('production_enhanced_tci_fix.py', 'w') as f:
        f.write(enhanced_integration_code)
    
    print("✅ Production enhanced integration created: production_enhanced_tci_fix.py")

def update_model_integration():
    """Update model_integration.py to support enhanced models."""
    
    try:
        # Read current model integration
        with open('model_integration.py', 'r') as f:
            content = f.read()
        
        # Check if enhanced integration is already added
        if 'ProductionEnhancedTCIFix' in content:
            print("✅ Model integration already supports enhanced models")
            return
        
        # Add enhanced model import and integration
        enhanced_import = """
# Enhanced TCI-fix integration
try:
    from production_enhanced_tci_fix import ProductionEnhancedTCIFix
    ENHANCED_TCI_FIX_AVAILABLE = True
except ImportError:
    ENHANCED_TCI_FIX_AVAILABLE = False
"""
        
        # Insert after existing imports
        import_index = content.find('from tci_fix_integration import TCIFixIntegration')
        if import_index != -1:
            insert_point = content.find('\n', import_index) + 1
            content = content[:insert_point] + enhanced_import + content[insert_point:]
        
        # Add enhanced model to the model list
        models_section = content.find("'TCI-fix':")
        if models_section != -1:
            # Find the end of the TCI-fix model definition
            end_section = content.find('},', models_section)
            if end_section != -1:
                enhanced_model_def = """
        'Enhanced TCI-fix': {
            'class': 'ProductionEnhancedTCIFix',
            'description': 'Enhanced TCI-fix with bias correction, uncertainty quantification, and ensemble approach',
            'supports_external_data': True,
            'supports_uncertainty': True,
            'training_time': 'Medium',
            'accuracy': 'High'
        },"""
                content = content[:end_section + 2] + enhanced_model_def + content[end_section + 2:]
        
        # Save updated model integration
        with open('model_integration.py', 'w') as f:
            f.write(content)
        
        print("✅ Model integration updated to support enhanced models")
        
    except Exception as e:
        print(f"⚠️ Could not update model integration: {e}")

def create_enhanced_training_script():
    """Create a script for training enhanced models."""
    
    training_script = '''#!/usr/bin/env python3
"""
Train Enhanced Models

Script to train enhanced TCI-fix models for all customer-product combinations.
"""

import os
import pandas as pd
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def train_enhanced_models():
    """Train enhanced models for all customer-product combinations."""
    
    print("🤖 Training Enhanced TCI-fix Models")
    print("=" * 50)
    
    try:
        from production_enhanced_tci_fix import ProductionEnhancedTCIFix
        from folder_data_manager import FolderDataManager
        
        # Initialize enhanced integration
        enhanced_integration = ProductionEnhancedTCIFix()
        
        # Load data to get customer-product combinations
        data_manager = FolderDataManager("dataset")
        success, message = data_manager.load_data_from_folder()
        
        if not success:
            print(f"❌ Failed to load data: {message}")
            return
        
        # Get all customer-product combinations
        combinations = []
        for key in data_manager.data.keys():
            if '_' in key:
                customer, product = key.split('_', 1)
                combinations.append((customer, product))
        
        print(f"📊 Found {len(combinations)} customer-product combinations")
        
        # Train enhanced models
        successful = 0
        failed = 0
        
        for i, (customer, product) in enumerate(combinations, 1):
            print(f"\\n🔧 Training {i}/{len(combinations)}: {customer} - {product}")
            
            try:
                success, message, model_info = enhanced_integration.train_enhanced_model(
                    customer, product, force_retrain=False
                )
                
                if success:
                    print(f"✅ Success: {message}")
                    if model_info:
                        print(f"   Volatility: {model_info.get('volatility_level', 'unknown')}")
                        print(f"   Bias correction: {model_info.get('bias_correction_factor', 1.0):.3f}")
                    successful += 1
                else:
                    print(f"❌ Failed: {message}")
                    failed += 1
                    
            except Exception as e:
                print(f"❌ Error: {e}")
                failed += 1
        
        print(f"\\n📊 Training Summary:")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")
        
        if successful > 0:
            print(f"\\n🎉 Enhanced models ready for use!")
            print("You can now use 'Enhanced TCI-fix' in the GUI")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

def train_specific_model(customer, product):
    """Train enhanced model for a specific customer-product."""
    
    print(f"🎯 Training Enhanced Model: {customer} - {product}")
    print("-" * 50)
    
    try:
        from production_enhanced_tci_fix import ProductionEnhancedTCIFix
        
        enhanced_integration = ProductionEnhancedTCIFix()
        
        success, message, model_info = enhanced_integration.train_enhanced_model(
            customer, product, force_retrain=True
        )
        
        if success:
            print(f"✅ Success: {message}")
            if model_info:
                print(f"\\n📊 Model Information:")
                print(f"   Volatility level: {model_info.get('volatility_level', 'unknown')}")
                print(f"   Bias correction factor: {model_info.get('bias_correction_factor', 1.0):.3f}")
                print(f"   Uncertainty factor: {model_info.get('uncertainty_factor', 0.25):.1%}")
                print(f"   Data points: {model_info.get('data_points', 'unknown')}")
                print(f"   Training date: {model_info.get('training_date', 'unknown')}")
        else:
            print(f"❌ Failed: {message}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 3:
        # Train specific model
        customer = sys.argv[1]
        product = sys.argv[2]
        train_specific_model(customer, product)
    else:
        # Train all models
        train_enhanced_models()
'''
    
    with open('train_enhanced_models.py', 'w') as f:
        f.write(training_script)
    
    print("✅ Enhanced training script created: train_enhanced_models.py")

def update_gui_for_enhanced_models():
    """Update GUI to support enhanced models."""
    
    try:
        # Check if GUI file exists
        if not os.path.exists('gui.py'):
            print("⚠️ GUI file not found, skipping GUI update")
            return
        
        # Read current GUI
        with open('gui.py', 'r') as f:
            content = f.read()
        
        # Check if enhanced models are already supported
        if 'Enhanced TCI-fix' in content:
            print("✅ GUI already supports enhanced models")
            return
        
        # Find model selection section and add enhanced option
        model_options_pattern = "['TCI-fix', 'TCI Premium']"
        if model_options_pattern in content:
            enhanced_options = "['TCI-fix', 'TCI Premium', 'Enhanced TCI-fix']"
            content = content.replace(model_options_pattern, enhanced_options)
        
        # Add enhanced model handling in prediction logic
        prediction_section = content.find("if selected_model == 'TCI-fix':")
        if prediction_section != -1:
            # Find the end of the TCI-fix section
            end_section = content.find("elif selected_model == 'TCI Premium':", prediction_section)
            if end_section != -1:
                enhanced_section = """
            elif selected_model == 'Enhanced TCI-fix':
                try:
                    from production_enhanced_tci_fix import ProductionEnhancedTCIFix
                    enhanced_integration = ProductionEnhancedTCIFix()
                    forecast = enhanced_integration.predict_enhanced(selected_customer, selected_product, periods=periods)
                    
                    if forecast is not None:
                        st.success(f"Enhanced TCI-fix forecast generated: {len(forecast)} periods")
                        
                        # Display enhanced features
                        if 'uncertainty_factor' in forecast.columns:
                            avg_uncertainty = forecast['uncertainty_factor'].iloc[0]
                            st.info(f"Uncertainty level: ±{avg_uncertainty:.0%}")
                        
                        if 'volatility_level' in forecast.columns:
                            volatility = forecast['volatility_level'].iloc[0]
                            st.info(f"Volatility level: {volatility}")
                        
                        if 'confidence_level' in forecast.columns:
                            avg_confidence = forecast['confidence_level'].mean()
                            st.info(f"Average confidence: {avg_confidence:.0%}")
                    else:
                        st.error("Enhanced TCI-fix forecast generation failed")
                        
                except ImportError:
                    st.error("Enhanced TCI-fix not available. Please run: python train_enhanced_models.py")
                except Exception as e:
                    st.error(f"Enhanced TCI-fix error: {str(e)}")
"""
                content = content[:end_section] + enhanced_section + content[end_section:]
        
        # Save updated GUI
        with open('gui.py', 'w') as f:
            f.write(content)
        
        print("✅ GUI updated to support enhanced models")
        
    except Exception as e:
        print(f"⚠️ Could not update GUI: {e}")

def test_enhanced_implementation():
    """Test the enhanced implementation."""
    
    print("🧪 Testing Enhanced Implementation")
    print("-" * 40)
    
    try:
        # Test 1: Import enhanced integration
        print("Test 1: Importing enhanced integration...")
        from production_enhanced_tci_fix import ProductionEnhancedTCIFix
        enhanced_integration = ProductionEnhancedTCIFix()
        print("✅ Enhanced integration imported successfully")
        
        # Test 2: Train a test model
        print("\nTest 2: Training test enhanced model...")
        customer = "ADVIK (IND)"
        product = "MECHANICAL SEAL ( BB-0030451 )"
        
        success, message, model_info = enhanced_integration.train_enhanced_model(customer, product)
        
        if success:
            print(f"✅ Test model trained: {message}")
            if model_info:
                print(f"   Volatility: {model_info.get('volatility_level', 'unknown')}")
                print(f"   Bias correction: {model_info.get('bias_correction_factor', 1.0):.3f}")
        else:
            print(f"❌ Test model training failed: {message}")
            return False
        
        # Test 3: Generate enhanced predictions
        print("\nTest 3: Generating enhanced predictions...")
        enhanced_predictions = enhanced_integration.predict_enhanced(customer, product, periods=6)
        
        if enhanced_predictions is not None:
            print(f"✅ Enhanced predictions generated: {len(enhanced_predictions)} periods")
            
            # Check enhanced features
            required_columns = ['predicted_quantity', 'lower_bound', 'upper_bound', 'confidence_level']
            missing_columns = [col for col in required_columns if col not in enhanced_predictions.columns]
            
            if not missing_columns:
                print("✅ All enhanced features present")
                
                # Show sample prediction
                sample = enhanced_predictions.iloc[0]
                pred = sample['predicted_quantity']
                lower = sample['lower_bound']
                upper = sample['upper_bound']
                conf = sample['confidence_level']
                
                print(f"   Sample prediction: {pred:,.0f} [{lower:,.0f} - {upper:,.0f}] ({conf:.0%} confidence)")
            else:
                print(f"⚠️ Missing enhanced features: {missing_columns}")
        else:
            print("❌ Enhanced predictions failed")
            return False
        
        print("\n✅ Enhanced implementation test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Implementation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    implement_enhanced_system()
