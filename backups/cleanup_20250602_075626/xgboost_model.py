"""
XGBoost Order Prediction Module

This module uses the powerful XGBoost machine learning algorithm to predict future
customer orders based on historical patterns. XGBoost is particularly good at:

1. Capturing complex non-linear relationships in data
2. Handling a mix of numerical and categorical features
3. Automatically identifying important features that drive ordering behavior
4. Providing robust predictions even with limited data

Unlike Prophet (which focuses on time series patterns), XGBoost can incorporate
additional features beyond just dates and quantities, potentially leading to
more accurate predictions when customer behavior is influenced by multiple factors.

Author: Harry
Version: 1.0
"""

# Data handling and numerical libraries
import pandas as pd              # For data manipulation and analysis
import numpy as np               # For numerical operations

# Visualization
import matplotlib.pyplot as plt  # For creating plots and visualizations

# Standard library imports
import logging                   # For logging messages and errors
from datetime import datetime, timedelta  # For date and time handling
import os                        # For file and directory operations

# Machine learning libraries
from sklearn.preprocessing import StandardScaler  # For normalizing features
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV  # For model tuning
from sklearn.metrics import mean_absolute_percentage_error  # For MAPE calculation
import xgboost as xgb            # The gradient boosting implementation we use

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Custom evaluation metric for XGBoost
def quantity_error(predt, dtrain):
    """Custom evaluation metric for order quantities.

    This function calculates a weighted error that penalizes:
    1. Predicting zero when there's an order (false negative)
    2. Predicting a quantity that's far from the actual value

    Args:
        predt: Predictions
        dtrain: Training data

    Returns:
        tuple: (metric_name, metric_value, is_higher_better)
    """
    y = dtrain.get_label()

    # Convert predictions to non-negative values
    predt = np.maximum(predt, 0)

    # Calculate errors
    errors = np.abs(y - predt)

    # Penalize false negatives (predicting 0 when there's an order)
    false_neg_mask = (predt < 0.5) & (y > 0)
    false_neg_penalty = 2.0  # Higher penalty for missing an order

    # Penalize false positives (predicting an order when there's none)
    false_pos_mask = (predt > 0.5) & (y == 0)
    false_pos_penalty = 1.5  # Lower penalty for false alarms

    # Apply penalties
    weighted_errors = errors.copy()
    weighted_errors[false_neg_mask] *= false_neg_penalty
    weighted_errors[false_pos_mask] *= false_pos_penalty

    # Calculate mean weighted error
    mean_error = np.mean(weighted_errors)

    return 'quantity_error', mean_error, False  # Lower is better

class XGBoostOrderPredictor:
    """XGBoost-based order prediction model.

    This class handles all aspects of using XGBoost for order prediction:
    1. Feature engineering - converting dates into useful features
    2. Model training - fitting XGBoost to historical order data
    3. Prediction - forecasting future orders
    4. Visualization - displaying results in an understandable way
    5. Feature importance - identifying what drives ordering patterns

    The model maintains separate predictors for each customer-product combination,
    allowing for personalized forecasts that capture the unique patterns of each
    product line.
    """

    def __init__(self):
        """Initialize the XGBoost order predictor.

        Sets up the internal storage for models and forecasts, and defines
        default parameters for the prediction process.
        """
        self.models = {}              # Dictionary to store trained models by customer/product
        self.forecasts = {}           # Dictionary to store generated forecasts
        self.frequency_patterns = {}  # Dictionary to store detected ordering patterns
        self.min_orders_for_training = 5  # Minimum data points needed for reliable training

    def _preprocess_quantity_data(self, df):
        """
        Preprocess data specifically for quantity prediction.
        This method applies transformations to make quantity prediction more accurate.

        Args:
            df (pandas.DataFrame): DataFrame with 'ds' and 'y' columns

        Returns:
            pandas.DataFrame: Preprocessed DataFrame
        """
        df = df.copy()

        # Ensure 'y' column exists
        if 'y' not in df.columns:
            return df

        # Check if this is a large-quantity product
        non_zero_orders = df[df['y'] > 0]['y']
        mean_quantity = non_zero_orders.mean() if len(non_zero_orders) > 0 else 0
        is_large_quantity = mean_quantity > 1000  # Threshold for large quantities

        # Store the information about quantity scale
        df['is_large_quantity'] = is_large_quantity
        df['mean_quantity'] = mean_quantity

        # Apply appropriate transformation based on quantity scale
        if is_large_quantity:
            # For large quantities, use a different scaling approach
            # Scale down by dividing by 1000 to bring to a more manageable range
            df['y_scaled'] = df['y'] / 1000.0
            logger.info(f"Large quantity product detected with mean {mean_quantity:.2f}. Scaling down by 1000.")

            # Still keep log transform for model training
            df['y_log'] = np.log1p(df['y'])
        else:
            # For normal quantities, just use log transform
            df['y_log'] = np.log1p(df['y'])
            df['y_scaled'] = df['y']  # No scaling needed

        # Identify outliers using IQR method
        # Use the appropriate column based on quantity scale
        target_col = 'y_scaled' if is_large_quantity else 'y'

        q1 = df[target_col].quantile(0.25)
        q3 = df[target_col].quantile(0.75)
        iqr = q3 - q1

        # Define outlier bounds (1.5 * IQR is a common threshold)
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        # Flag outliers
        df['is_outlier'] = ((df[target_col] < lower_bound) | (df[target_col] > upper_bound)).astype(int)

        # Create a winsorized version of the target (capped at bounds)
        if is_large_quantity:
            # For large quantities, be more conservative with winsorizing
            # Use a wider bound to avoid excessive capping
            lower_bound = max(0, q1 - 2.0 * iqr)  # More permissive lower bound
            upper_bound = q3 + 2.0 * iqr  # More permissive upper bound

            # Winsorize the scaled version
            df['y_scaled_winsorized'] = df['y_scaled'].clip(lower=max(0, lower_bound), upper=upper_bound)
            # Convert back to original scale
            df['y_winsorized'] = df['y_scaled_winsorized'] * 1000.0
        else:
            # For normal quantities, use standard winsorizing
            df['y_winsorized'] = df['y'].clip(lower=max(0, lower_bound), upper=upper_bound)

        # Calculate rolling statistics of quantities
        if len(df) > 3:
            # Calculate rolling median (more robust to outliers than mean)
            df['rolling_median_3'] = df[target_col].rolling(window=3, min_periods=1).median()

            # Calculate exponentially weighted moving average (gives more weight to recent values)
            df['ewma'] = df[target_col].ewm(span=3, adjust=False).mean()

            if is_large_quantity:
                # Convert back to original scale
                df['rolling_median_3'] = df['rolling_median_3'] * 1000.0
                df['ewma'] = df['ewma'] * 1000.0

        return df

    def _create_features(self, df):
        """
        Create time-based features from datetime.

        Args:
            df (pandas.DataFrame): DataFrame with 'ds' column

        Returns:
            pandas.DataFrame: DataFrame with additional features
        """
        df = df.copy()

        # Ensure 'y' column exists
        if 'y' not in df.columns:
            df['y'] = 0

        # Basic time features
        df['month'] = df['ds'].dt.month
        df['quarter'] = df['ds'].dt.quarter
        df['year'] = df['ds'].dt.year
        df['day_of_month'] = df['ds'].dt.day
        df['day_of_week'] = df['ds'].dt.dayofweek
        df['day_of_year'] = df['ds'].dt.dayofyear
        df['week_of_year'] = df['ds'].dt.isocalendar().week

        # Cyclical encoding for month, day of week, etc.
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_of_week_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_of_week_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['quarter_sin'] = np.sin(2 * np.pi * df['quarter'] / 4)
        df['quarter_cos'] = np.cos(2 * np.pi * df['quarter'] / 4)

        # Enhanced features for better prediction

        # Binary indicator for whether an order was placed
        df['is_order'] = (df['y'] > 0).astype(int)

        # Order history features
        if len(df) > 0:
            # Days since first order
            first_order_date = df[df['y'] > 0]['ds'].min() if any(df['y'] > 0) else df['ds'].min()
            df['days_since_first_order'] = (df['ds'] - first_order_date).dt.days

            # Cumulative order count
            df['cumulative_orders'] = df['is_order'].cumsum()

            # Month-specific ordering pattern
            # Create binary indicators for each month to capture month-specific patterns
            for m in range(1, 13):
                df[f'is_month_{m}'] = (df['month'] == m).astype(int)

        # Lag features (if enough data)
        if len(df) > 12:
            # Create lag features for the last 3, 6, and 12 months
            for lag in [3, 6, 12]:
                if len(df) > lag:
                    df[f'lag_{lag}'] = df['y'].shift(lag)
                    # Also add binary lag features (was there an order N periods ago?)
                    df[f'order_{lag}_periods_ago'] = df['is_order'].shift(lag).fillna(0).astype(int)

        # Rolling statistics (if enough data)
        if len(df) > 3:
            # 3-month rolling average and std
            df['rolling_mean_3'] = df['y'].rolling(window=3, min_periods=1).mean()
            df['rolling_std_3'] = df['y'].rolling(window=3, min_periods=1).std()
            # Rolling order frequency (percentage of months with orders)
            df['rolling_order_freq_3'] = df['is_order'].rolling(window=3, min_periods=1).mean()

        if len(df) > 6:
            # 6-month rolling average and std
            df['rolling_mean_6'] = df['y'].rolling(window=6, min_periods=1).mean()
            df['rolling_std_6'] = df['y'].rolling(window=6, min_periods=1).std()
            # Rolling order frequency (percentage of months with orders)
            df['rolling_order_freq_6'] = df['is_order'].rolling(window=6, min_periods=1).mean()

        if len(df) > 12:
            # 12-month rolling statistics
            df['rolling_mean_12'] = df['y'].rolling(window=12, min_periods=1).mean()
            df['rolling_order_freq_12'] = df['is_order'].rolling(window=12, min_periods=1).mean()

        # Fill NaN values with appropriate defaults
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col != 'y' and df[col].isna().any():
                df[col] = df[col].fillna(df[col].mean())

        return df

    def _detect_month_patterns(self, data, customer, product):
        """
        Analyze which specific months orders are placed in.

        This is crucial for predicting zero orders in months where the customer rarely orders.

        Args:
            data (pandas.DataFrame): Prophet-formatted data (ds, y)
            customer (str): Customer name
            product (str): Product name

        Returns:
            dict: Monthly order frequency information
        """
        key = f"{customer}_{product}"
        monthly_order_frequency = {}

        try:
            if len(data) >= 12:  # Need at least a year of data for reliable patterns
                # Add month column if not already present
                if 'month' not in data.columns:
                    data['month'] = data['ds'].dt.month

                # Add binary indicator for orders
                data['is_order'] = (data['y'] > 0).astype(int)

                # Get unique years in the data
                years_with_data = data['ds'].dt.year.unique()
                num_years = len(years_with_data)

                if num_years >= 2:  # Need at least 2 years for reliable patterns
                    # For each month, calculate how often orders are placed
                    for month in range(1, 13):
                        # Count years where this month has orders
                        years_with_orders = data[(data['month'] == month) &
                                              (data['is_order'] == 1)]['ds'].dt.year.nunique()

                        # Calculate frequency as percentage of years with orders in this month
                        frequency = years_with_orders / num_years if num_years > 0 else 0
                        monthly_order_frequency[month] = frequency

                    logger.info(f"Monthly order frequency for {key}: {monthly_order_frequency}")

                    # Also store the actual order quantities by month
                    monthly_values = {}
                    for month in range(1, 13):
                        month_data = data[(data['month'] == month) & (data['y'] > 0)]
                        if not month_data.empty:
                            monthly_values[month] = sorted(month_data['y'].unique().tolist())

                    return {
                        'monthly_order_frequency': monthly_order_frequency,
                        'monthly_values': monthly_values
                    }

            return {
                'monthly_order_frequency': {},
                'monthly_values': {}
            }

        except Exception as e:
            logger.error(f"Error detecting month patterns for {key}: {str(e)}")
            return {
                'monthly_order_frequency': {},
                'monthly_values': {}
            }

    def analyze_frequency_pattern(self, data, customer, product):
        """
        Analyze the frequency pattern of orders for a specific customer-product combination.

        Args:
            data (pandas.DataFrame): Prophet-formatted data (ds, y)
            customer (str): Customer name
            product (str): Product name

        Returns:
            dict: Frequency pattern information
        """
        key = f"{customer}_{product}"

        if data is None or len(data) < 3:  # Need at least 3 data points to analyze pattern
            return None

        try:
            # Sort data by date
            sorted_data = data.sort_values('ds')

            # Calculate days between orders
            sorted_data['days_diff'] = sorted_data['ds'].diff().dt.days

            # Remove NaN (first row)
            days_diff = sorted_data['days_diff'].dropna()

            if len(days_diff) < 2:
                return None

            # Calculate basic statistics
            avg_days = days_diff.mean()
            median_days = days_diff.median()
            std_days = days_diff.std()
            min_days = days_diff.min()
            max_days = days_diff.max()

            # Determine if there's a regular pattern
            is_regular = std_days / avg_days < 0.5  # Low coefficient of variation suggests regularity

            # Estimate frequency in days (rounded to nearest common period)
            if is_regular:
                # Try to map to common periods (monthly, quarterly, etc.)
                if 25 <= median_days <= 35:  # Monthly
                    frequency = 30
                    frequency_name = 'Monthly'
                elif 85 <= median_days <= 95:  # Quarterly
                    frequency = 90
                    frequency_name = 'Quarterly'
                elif 175 <= median_days <= 185:  # Semi-annually
                    frequency = 180
                    frequency_name = 'Semi-annually'
                elif 350 <= median_days <= 380:  # Annually
                    frequency = 365
                    frequency_name = 'Annually'
                else:
                    frequency = round(median_days)
                    frequency_name = f'Every {frequency} days'
            else:
                # If irregular, use the median as a rough estimate
                frequency = round(median_days)
                frequency_name = f'Approximately every {frequency} days (irregular)'

            # Store the pattern
            pattern = {
                'avg_days': avg_days,
                'median_days': median_days,
                'std_days': std_days,
                'min_days': min_days,
                'max_days': max_days,
                'is_regular': is_regular,
                'frequency': frequency,
                'frequency_name': frequency_name
            }

            self.frequency_patterns[key] = pattern
            logger.info(f"Frequency pattern for {key}: {frequency_name} (every {frequency} days)")

            return pattern

        except Exception as e:
            logger.error(f"Error analyzing frequency pattern for {key}: {str(e)}")
            return None

    def _decompose_time_series(self, data):
        """
        Decompose time series into trend, seasonality, and residual components.
        Similar to Prophet's decomposition.

        Args:
            data (pandas.DataFrame): Time series data with 'ds' and 'y' columns

        Returns:
            dict: Components dictionary with trend, seasonality, and residual
        """
        try:
            # Convert to pandas datetime if not already
            data['ds'] = pd.to_datetime(data['ds'])

            # Sort by date
            data = data.sort_values('ds')

            # Create a time series
            ts = pd.Series(data['y'].values, index=data['ds'])

            # Check if we have enough data for decomposition
            if len(ts) < 12:
                # For short time series, use simple moving average for trend
                window = max(2, len(ts) // 2)
                trend = ts.rolling(window=window, center=True).mean()
                # Fill NaN values at the beginning and end
                trend = trend.fillna(method='bfill').fillna(method='ffill')
                seasonality = pd.Series(0, index=ts.index)  # No seasonality for short series
                residual = ts - trend
            else:
                # For longer series, try to use seasonal_decompose
                try:
                    from statsmodels.tsa.seasonal import seasonal_decompose
                    # Determine period based on data frequency
                    if len(ts) >= 365:  # Daily data with at least a year
                        period = 365
                    elif len(ts) >= 52:  # Weekly data with at least a year
                        period = 52
                    elif len(ts) >= 12:  # Monthly data with at least a year
                        period = 12
                    else:
                        period = 4  # Quarterly or custom

                    # Decompose
                    result = seasonal_decompose(ts, model='additive', period=period, extrapolate_trend='freq')
                    trend = result.trend
                    seasonality = result.seasonal
                    residual = result.resid
                except Exception as e:
                    # Fallback to simple moving average
                    logger.warning(f"Seasonal decomposition failed: {str(e)}. Using simple moving average.")
                    window = max(2, len(ts) // 4)
                    trend = ts.rolling(window=window, center=True).mean()
                    trend = trend.fillna(method='bfill').fillna(method='ffill')
                    seasonality = pd.Series(0, index=ts.index)
                    residual = ts - trend

            # Create components dictionary similar to Prophet
            components = {
                'trend': trend,
                'seasonality': seasonality,
                'residual': residual,
                'ds': data['ds'],
                'y': ts
            }

            return components

        except Exception as e:
            logger.error(f"Error decomposing time series: {str(e)}")
            return None

    def _get_params_for_dataset_size(self, data_size, is_large_quantity=False):
        """
        Get appropriate XGBoost parameters based on dataset size and quantity scale.

        Args:
            data_size (int): Number of data points
            is_large_quantity (bool): Whether this product has large quantities

        Returns:
            dict: Parameters optimized for the dataset size and quantity scale
        """
        # Choose objective function based on quantity scale
        if is_large_quantity:
            # For large quantities, use squared error regression instead of Poisson
            objective = 'reg:squarederror'  # Better for large quantities
            logger.info("Using reg:squarederror objective for large quantity product")
        else:
            # For normal quantities, use Poisson regression
            objective = 'count:poisson'  # Better for smaller count data
        # For very small datasets (less than 10 points)
        if data_size < 10:
            logger.info(f"Using small dataset XGBoost parameters with {data_size} records")
            return {
                'n_estimators': 50,  # Fewer trees to prevent overfitting
                'max_depth': 3,      # Shallower trees
                'learning_rate': 0.05,  # Slower learning rate
                'subsample': 0.9,    # Use more data in each tree
                'colsample_bytree': 0.9,  # Use more features
                'min_child_weight': 3,  # Require more observations per node
                'gamma': 0.1,        # More conservative split threshold
                'objective': objective,  # Dynamic objective based on quantity scale
                'random_state': 42
            }
        # For medium datasets (10-20 points)
        elif data_size < 20:
            logger.info(f"Using medium dataset XGBoost parameters with {data_size} records")
            return {
                'n_estimators': 75,
                'max_depth': 4,
                'learning_rate': 0.08,
                'subsample': 0.85,
                'colsample_bytree': 0.85,
                'min_child_weight': 2,
                'gamma': 0.05,
                'objective': objective,  # Dynamic objective based on quantity scale
                'random_state': 42
            }
        # For larger datasets (20+ points)
        else:
            # For large quantity products with sufficient data, use more trees
            if is_large_quantity and data_size >= 30:
                logger.info(f"Using enhanced parameters for large quantity product with {data_size} records")
                return {
                    'n_estimators': 150,  # More trees for better accuracy
                    'max_depth': 6,       # Deeper trees for complex patterns
                    'learning_rate': 0.08, # Slightly lower learning rate for stability
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'min_child_weight': 1,
                    'gamma': 0.01,
                    'objective': objective,  # Dynamic objective based on quantity scale
                    'random_state': 42
                }
            else:
                logger.info(f"Using standard XGBoost parameters with {data_size} records")
                return {
                    'n_estimators': 100,
                    'max_depth': 5,
                    'learning_rate': 0.1,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'min_child_weight': 1,
                    'gamma': 0.01,
                    'objective': objective,  # Dynamic objective based on quantity scale
                    'random_state': 42
                }

    def train_model(self, data, customer, product, params=None):
        """
        Train an XGBoost model for a specific customer-product combination.

        Args:
            data (pandas.DataFrame): Prophet-formatted data (ds, y)
            customer (str): Customer name
            product (str): Product name
            params (dict, optional): XGBoost model parameters

        Returns:
            tuple: (success, message)
        """
        key = f"{customer}_{product}"

        if data is None or len(data) < self.min_orders_for_training:
            return False, f"Insufficient data for {customer} - {product} (need at least {self.min_orders_for_training} records)"

        try:
            # Analyze frequency pattern first
            pattern = self.analyze_frequency_pattern(data, customer, product)

            # Detect month-specific ordering patterns
            month_patterns = self._detect_month_patterns(data, customer, product)

            # Preprocess data for quantity prediction
            preprocessed_data = self._preprocess_quantity_data(data)

            # Create features
            features_df = self._create_features(preprocessed_data)

            # Decide whether to use original or winsorized target based on outliers
            has_outliers = 'is_outlier' in features_df.columns and features_df['is_outlier'].sum() > 0
            if has_outliers:
                logger.info(f"Using winsorized target for {key} due to {features_df['is_outlier'].sum()} outliers")
                # Use winsorized target for training
                target_col = 'y_winsorized'
            else:
                # Use original target
                target_col = 'y'

            # Check if this is a large-quantity product
            is_large_quantity = False
            if 'is_large_quantity' in features_df.columns:
                is_large_quantity = features_df['is_large_quantity'].iloc[0]
                mean_quantity = features_df['mean_quantity'].iloc[0] if 'mean_quantity' in features_df.columns else 0
                if is_large_quantity:
                    logger.info(f"Using large quantity model configuration for {key} with mean quantity {mean_quantity:.2f}")

            # Get parameters based on dataset size and quantity scale
            default_params = self._get_params_for_dataset_size(len(data), is_large_quantity)

            # Update with custom parameters if provided
            if params:
                default_params.update(params)

            # Prepare data for training - make sure to drop the datetime column
            drop_cols = ['ds', 'y']

            # Add additional columns to drop if they exist
            for col in ['y_log', 'y_winsorized', 'is_outlier', 'y_scaled', 'y_scaled_winsorized', 'is_large_quantity', 'mean_quantity']:
                if col in features_df.columns and col != target_col:
                    drop_cols.append(col)

            X = features_df.drop(drop_cols, axis=1)

            # Check for any remaining datetime columns and convert them
            for col in X.select_dtypes(include=['datetime64']).columns:
                X[col] = X[col].astype(np.int64) // 10**9  # Convert to Unix timestamp

            # Convert any remaining object columns to numeric if possible
            for col in X.select_dtypes(include=['object']).columns:
                try:
                    X[col] = pd.to_numeric(X[col], errors='coerce')
                    X[col] = X[col].fillna(X[col].mean() if not X[col].isna().all() else 0)
                except Exception as e:
                    logger.warning(f"Could not convert column {col} to numeric: {str(e)}")
                    # Drop the column if we can't convert it
                    X = X.drop(columns=[col])
            # Use the selected target column (original or winsorized)
            y = features_df[target_col]

            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Create and train regression model for quantity prediction
            model = xgb.XGBRegressor(**default_params)

            # Create a binary model to predict whether an order will occur
            binary_params = default_params.copy()
            binary_params['objective'] = 'binary:logistic'
            binary_model = xgb.XGBClassifier(**binary_params)

            # Create binary target (1 if order, 0 if no order)
            y_binary = (y > 0).astype(int)

            # If we have enough data, use cross-validation to find best parameters
            if len(data) >= 10:
                try:
                    # Define parameter grid
                    param_grid = {
                        'max_depth': [3, 5, 7],
                        'learning_rate': [0.01, 0.1, 0.2],
                        'n_estimators': [50, 100, 200]
                    }

                    # Use time series split for cross-validation
                    tscv = TimeSeriesSplit(n_splits=min(5, len(data) // 2))

                    # Grid search with cross-validation
                    grid_search = GridSearchCV(
                        estimator=model,
                        param_grid=param_grid,
                        cv=tscv,
                        scoring='neg_mean_squared_error',
                        n_jobs=-1,
                        verbose=0
                    )

                    grid_search.fit(X_scaled, y)

                    # Get best parameters
                    best_params = grid_search.best_params_
                    logger.info(f"Best parameters for {key}: {best_params}")

                    # Update model with best parameters
                    model = xgb.XGBRegressor(**{**default_params, **best_params})

                except Exception as e:
                    logger.warning(f"Error during hyperparameter tuning for {key}: {str(e)}")
                    # Continue with default parameters

            # Train the regression model with custom evaluation metric
            model.fit(
                X_scaled, y,
                eval_metric=quantity_error,  # Use our custom metric
                verbose=False
            )

            # Train the binary model if we have both positive and negative examples
            binary_model_trained = False
            if y_binary.sum() > 0 and len(y_binary) - y_binary.sum() > 0:
                try:
                    binary_model.fit(X_scaled, y_binary)
                    binary_model_trained = True
                    logger.info(f"Binary classification model trained for {key} with {y_binary.sum()} positive examples")
                except Exception as e:
                    logger.warning(f"Error training binary model: {str(e)}")
                    binary_model = None

            # Calculate statistics
            stats = {
                'min': data['y'].min(),
                'max': data['y'].max(),
                'mean': data['y'].mean(),
                'median': data['y'].median(),
                'last_date': data['ds'].max(),
                'record_count': len(data)
            }

            # Decompose time series for Prophet-like components
            components = self._decompose_time_series(data)

            # Detect month-specific ordering patterns
            monthly_patterns = {}
            if len(data) >= 12:  # Need at least a year of data
                try:
                    # Add month column if not already present
                    if 'month' not in data.columns:
                        data['month'] = data['ds'].dt.month

                    # Add binary indicator for orders
                    data['has_order'] = (data['y'] > 0).astype(int)

                    # Get unique years in the data
                    years = data['ds'].dt.year.unique()
                    num_years = len(years)

                    if num_years >= 2:  # Need at least 2 years for reliable patterns
                        # For each month, calculate how often orders are placed
                        for month in range(1, 13):
                            # Count years where this month has orders
                            years_with_orders = data[(data['month'] == month) &
                                                  (data['has_order'] == 1)]['ds'].dt.year.nunique()

                            # Calculate frequency as percentage of years with orders in this month
                            frequency = years_with_orders / num_years if num_years > 0 else 0
                            monthly_patterns[month] = frequency

                        logger.info(f"Monthly order patterns for {key}: {monthly_patterns}")
                except Exception as e:
                    logger.warning(f"Error detecting monthly patterns: {str(e)}")
                    monthly_patterns = {}

            # Store model and related data
            self.models[key] = {
                'model': model,
                'binary_model': binary_model if binary_model_trained else None,
                'scaler': scaler,
                'features': X.columns.tolist(),
                'data': data,
                'stats': stats,
                'components': components,
                'monthly_patterns': monthly_patterns,
                'monthly_values': month_patterns.get('monthly_values', {})
            }

            # Also train a binary classification model to predict if an order will occur
            if len(data) >= self.min_orders_for_training:
                try:
                    # Create binary target: 1 if order, 0 if no order
                    features_df['is_order'] = (features_df['y'] > 0).astype(int)

                    # Prepare data for training
                    X_binary = X.copy()
                    y_binary = features_df['is_order']

                    # Train binary classifier
                    binary_model = xgb.XGBClassifier(
                        n_estimators=100,
                        max_depth=3,
                        learning_rate=0.1,
                        objective='binary:logistic',
                        random_state=42
                    )

                    binary_model.fit(X_scaled, y_binary)

                    # Store binary model
                    self.models[key]['binary_model'] = binary_model

                    logger.info(f"Binary classification model trained for {key}")
                except Exception as e:
                    logger.warning(f"Error training binary model for {key}: {str(e)}")
                    # Continue without binary model

            logger.info(f"Model trained for {key} with {len(data)} records")
            return True, f"Model trained with {len(data)} records"

        except Exception as e:
            logger.error(f"Error training model: {str(e)}")
            return False, f"Error training model: {str(e)}"

    def predict(self, customer, product, periods=6, freq='M', include_history=False, start_year=None):
        """
        Generate predictions for a specific customer-product combination.

        Args:
            customer (str): Customer name
            product (str): Product name
            periods (int): Number of periods to forecast
            freq (str): Frequency of forecast (D=daily, W=weekly, M=monthly)
            include_history (bool): Whether to include historical data in the forecast
            start_year (int, optional): Year to start predictions from

        Returns:
            tuple: (success, message, forecast)
        """
        key = f"{customer}_{product}"

        if key not in self.models:
            return False, f"No model found for {customer} - {product}", None

        try:
            model_data = self.models[key]
            model = model_data['model']
            scaler = model_data['scaler']
            features = model_data['features']
            stats = model_data['stats']
            data = model_data['data']

            # Get frequency pattern if available
            pattern = self.frequency_patterns.get(key, None)
            detected_freq = None

            # Always use historical pattern-based prediction
            # We'll keep detected_freq for backward compatibility
            if pattern:
                if pattern['is_regular']:
                    if pattern['frequency'] >= 300:  # Annual
                        detected_freq = 'Y'
                        logger.info(f"Detected yearly pattern for {key}")
                    elif pattern['frequency'] >= 90:  # Quarterly
                        detected_freq = 'Q'
                        logger.info(f"Detected quarterly pattern for {key}")
                    elif pattern['frequency'] >= 30:  # Monthly
                        detected_freq = 'M'
                        logger.info(f"Detected monthly pattern for {key}")
                    elif pattern['frequency'] >= 7:  # Weekly
                        detected_freq = 'W'
                        logger.info(f"Detected weekly pattern for {key}")
                    else:  # Custom frequency in days
                        detected_freq = f"{pattern['frequency']}D"
                        logger.info(f"Detected custom pattern ({pattern['frequency']} days) for {key}")
                else:
                    # For irregular patterns, use the pattern frequency but note it's irregular
                    detected_freq = freq
                    logger.info(f"Detected irregular pattern for {key}")
            else:
                # No pattern detected
                detected_freq = freq
                logger.info(f"No clear pattern detected for {key}, using {freq} as fallback")

            # Get the last date in the data
            last_date = stats['last_date']

            # Set start date based on start_year if provided
            if start_year is not None:
                # Create a future dataframe starting from January 1st of the specified year
                start_date = pd.Timestamp(f"{start_year}-01-01")

                # If start date is before the last order date, use the last order date
                if start_date < last_date:
                    start_date = last_date + pd.Timedelta(days=1)
            else:
                # Start from the day after the last date
                start_date = last_date + pd.Timedelta(days=1)

            # Use historical dates as a template for future predictions
            if len(data) >= 2:
                # Sort data by date
                sorted_data = data.sort_values('ds')

                # Get the original data from the data manager to find the actual days of month
                # This is necessary because the Prophet data is aggregated to the 1st of each month
                try:
                    # For small datasets, use the actual days from the historical data
                    # This preserves the exact timing pattern of orders
                    days_of_month = sorted_data['ds'].dt.day.unique()
                    logger.info(f"Using days from historical data: {days_of_month.tolist()}")
                except Exception as e:
                    logger.warning(f"Error setting days of month: {str(e)}. Using default.")
                    days_of_month = sorted_data['ds'].dt.day.unique()

                # Get all unique dates
                historical_dates = sorted_data['ds'].dt.date.unique()

                # Get all unique months
                months = sorted_data['ds'].dt.month.unique()

                logger.info(f"Historical dates pattern for {key}: Days={days_of_month.tolist()}, Months={months.tolist()}")

                # Create future dates based on historical pattern
                future_dates = []

                # Start from the day after the last date
                current_date = start_date

                # IMPROVED: Always try to use the actual pattern from historical data first
                # Calculate days between orders for all datasets
                days_diff = sorted_data['ds'].diff().dt.days.dropna()

                if len(days_diff) > 0:
                    # Calculate the median days between orders
                    median_days = int(round(days_diff.median()))
                    std_days = days_diff.std()
                    cv = std_days / median_days if median_days > 0 else 0

                    logger.info(f"Actual median days between orders for {key}: {median_days} (CV: {cv:.2f})")

                    # If the pattern is regular enough (CV < 0.5), use the median days pattern
                    # Or if we have a small dataset, always use the median days pattern
                    if cv < 0.5 or len(data) < 10:
                        logger.info(f"Using median days between orders ({median_days}) to generate future dates for {key}")

                        # Generate future dates based on the median days between orders
                        while len(future_dates) < periods:
                            future_dates.append(current_date)
                            logger.info(f"Added future date: {current_date} for {key}")
                            # Move to next expected order date based on the median days between orders
                            current_date = current_date + pd.Timedelta(days=median_days)
                    else:
                        # For irregular patterns with larger datasets, use the days of month pattern
                        logger.info(f"Pattern is irregular (CV={cv:.2f}). Using days of month pattern for {key}")

                        # Generate future dates
                        while len(future_dates) < periods:
                            # Always use the days of month that appear in historical data
                            if current_date.day in days_of_month:
                                future_dates.append(current_date)
                                logger.info(f"Added future date: {current_date} for {key}")

                            # Move to next day
                            current_date = current_date + pd.Timedelta(days=1)
                else:
                    # Fallback if we can't calculate days between orders
                    logger.info(f"Could not calculate days between orders. Using days of month pattern for {key}")

                    # Generate future dates
                    while len(future_dates) < periods:
                        # Always use the days of month that appear in historical data
                        if current_date.day in days_of_month:
                            future_dates.append(current_date)
                            logger.info(f"Added future date: {current_date} for {key}")

                        # Move to next day
                        current_date = current_date + pd.Timedelta(days=1)

                # Convert to DatetimeIndex
                future_dates = pd.DatetimeIndex(future_dates[:periods])
                logger.info(f"Created {len(future_dates)} future dates based on historical pattern for {key}")
            else:
                # Not enough historical data, use detected frequency
                # Initialize day_of_month to avoid warnings
                day_of_month = 1  # Default to 1st of month

                if detected_freq == 'M':
                    # Find the most common day of month in the historical data
                    if len(data) > 0:
                        days = data['ds'].dt.day.value_counts()
                        if not days.empty:
                            day_of_month = days.idxmax()
                            logger.info(f"Detected day of month pattern: {day_of_month} for {key}")
                        else:
                            logger.info(f"No day of month pattern detected, using default (1st) for {key}")
                    else:
                        logger.info(f"No data available, using default day of month (1st) for {key}")

                    # Monthly frequency
                    if day_of_month is not None:
                        # Use the detected day of month
                        # First create dates at the beginning of each month
                        month_starts = pd.date_range(start=start_date.replace(day=1), periods=periods, freq='MS')
                        # Then adjust to the detected day of month
                        future_dates = []
                        for date in month_starts:
                            # Handle month length differences (28/29/30/31 days)
                            year, month = date.year, date.month
                            # Get the last day of the month
                            if month == 12:
                                last_day = pd.Timestamp(year+1, 1, 1) - pd.Timedelta(days=1)
                            else:
                                last_day = pd.Timestamp(year, month+1, 1) - pd.Timedelta(days=1)

                            # Ensure day_of_month is defined
                            if 'day_of_month' not in locals() or day_of_month is None:
                                day_of_month = 1  # Default to 1st of month
                                logger.info(f"Using default day of month (1st) for {key}")

                            # Use the detected day or the last day of the month if it's shorter
                            day = min(day_of_month, last_day.day)
                            future_dates.append(pd.Timestamp(year, month, day))

                        future_dates = pd.DatetimeIndex(future_dates)
                        # Ensure day_of_month is defined for logging
                        if 'day_of_month' not in locals() or day_of_month is None:
                            day_of_month = 1  # Default to 1st of month
                        logger.info(f"Created future dates with day {day_of_month} of each month for {key}")
                    else:
                        # Default to first day of month if no pattern detected
                        future_dates = pd.date_range(start=start_date, periods=periods, freq='MS')
                elif detected_freq == 'Q':
                    # Quarterly frequency
                    if day_of_month is not None:
                        # Use the detected day of month for quarterly dates
                        quarter_starts = pd.date_range(start=start_date.replace(day=1), periods=periods, freq='QS')
                        future_dates = []
                        for date in quarter_starts:
                            year, month = date.year, date.month
                            # Handle month length differences
                            if month == 12:
                                last_day = pd.Timestamp(year+1, 1, 1) - pd.Timedelta(days=1)
                            else:
                                last_day = pd.Timestamp(year, month+1, 1) - pd.Timedelta(days=1)

                            day = min(day_of_month, last_day.day)
                            future_dates.append(pd.Timestamp(year, month, day))

                        future_dates = pd.DatetimeIndex(future_dates)
                    else:
                        future_dates = pd.date_range(start=start_date, periods=periods, freq='QS')
                elif detected_freq == 'Y':
                    # Yearly frequency
                    if day_of_month is not None:
                        # Use the detected day of month for yearly dates
                        # Also try to detect the month pattern
                        if len(data) > 0:
                            months = data['ds'].dt.month.value_counts()
                            if not months.empty:
                                month = months.idxmax()
                            else:
                                month = start_date.month
                        else:
                            month = start_date.month

                        future_dates = []
                        for i in range(periods):
                            year = start_date.year + i
                            # Handle February 29 in leap years
                            if month == 2 and day_of_month > 28:
                                if pd.Timestamp(year, 2, 1).is_leap_year:
                                    day = min(day_of_month, 29)
                                else:
                                    day = 28
                            else:
                                # Get the last day of the month
                                if month == 12:
                                    last_day = pd.Timestamp(year+1, 1, 1) - pd.Timedelta(days=1)
                                else:
                                    last_day = pd.Timestamp(year, month+1, 1) - pd.Timedelta(days=1)

                                day = min(day_of_month, last_day.day)

                            future_dates.append(pd.Timestamp(year, month, day))

                        future_dates = pd.DatetimeIndex(future_dates)
                    else:
                        future_dates = pd.date_range(start=start_date, periods=periods, freq='YS')
                elif detected_freq == 'W':
                    # Weekly frequency
                    # Try to detect the day of week pattern
                    if len(data) > 0:
                        weekdays = data['ds'].dt.dayofweek.value_counts()
                        if not weekdays.empty:
                            weekday = weekdays.idxmax()
                            # Create weekly dates starting from the detected weekday
                            # Find the next occurrence of the weekday
                            days_ahead = weekday - start_date.dayofweek
                            if days_ahead < 0:
                                days_ahead += 7
                            next_weekday = start_date + pd.Timedelta(days=days_ahead)
                            future_dates = pd.date_range(start=next_weekday, periods=periods, freq='W-' + str(weekday))
                        else:
                            future_dates = pd.date_range(start=start_date, periods=periods, freq='W')
                    else:
                        future_dates = pd.date_range(start=start_date, periods=periods, freq='W')
                elif 'D' in str(detected_freq):
                    # Custom day frequency
                    days = int(str(detected_freq).replace('D', ''))
                    future_dates = [start_date + pd.Timedelta(days=days*i) for i in range(periods)]
                    future_dates = pd.DatetimeIndex(future_dates)
                else:
                    # Default to monthly if frequency not recognized
                    future_dates = pd.date_range(start=start_date, periods=periods, freq='MS')

            # Create future dataframe
            future = pd.DataFrame({'ds': future_dates})

            # Create features for future dates
            # Add a dummy 'y' column for _create_features to work with
            future_with_dummy = future.copy()
            future_with_dummy['y'] = 0

            # Create features
            future_features = self._create_features(future_with_dummy)

            # Drop ds and y
            X_future = future_features.drop(['ds', 'y'], axis=1)

            # Check for any datetime columns and convert them
            for col in X_future.select_dtypes(include=['datetime64']).columns:
                X_future[col] = X_future[col].astype(np.int64) // 10**9  # Convert to Unix timestamp

            # Convert any remaining object columns to numeric if possible
            for col in X_future.select_dtypes(include=['object']).columns:
                try:
                    X_future[col] = pd.to_numeric(X_future[col], errors='coerce')
                    X_future[col] = X_future[col].fillna(X_future[col].mean() if not X_future[col].isna().all() else 0)
                except Exception as e:
                    logger.warning(f"Could not convert column {col} to numeric: {str(e)}")
                    # Drop the column if we can't convert it
                    X_future = X_future.drop(columns=[col])

            # Ensure we have all required features
            for feature in features:
                if feature not in X_future.columns:
                    X_future[feature] = 0

            # Reorder columns to match training data
            X_future = X_future[features]

            # Scale features
            X_future_scaled = scaler.transform(X_future)

            # Check if we have a binary model to predict whether an order will occur
            binary_model = model_data.get('binary_model')
            monthly_patterns = model_data.get('monthly_patterns', {})

            # First predict whether an order will occur (0 or 1)
            if binary_model is not None:
                # Generate binary predictions (probability of an order)
                order_probabilities = binary_model.predict_proba(X_future_scaled)[:, 1]  # Probability of class 1 (order)

                # Create a mask for months that should have zero orders
                zero_order_mask = np.zeros(len(future_dates), dtype=bool)

                # Apply month-specific patterns
                for i, date in enumerate(future_dates):
                    month = date.month

                    # Check if this month historically has low order frequency
                    month_frequency = monthly_patterns.get(month, 1.0)  # Default to 100% if no data

                    # If the customer orders in this month less than 30% of the time, predict zero
                    if month_frequency < 0.3:
                        zero_order_mask[i] = True
                        logger.info(f"Zeroing out month {month} for {key} due to low historical frequency: {month_frequency:.2f}")
                    # For borderline months (30-70%), use the binary model's prediction
                    elif month_frequency < 0.7:
                        # If probability is less than 0.5, predict no order
                        if order_probabilities[i] < 0.5:
                            zero_order_mask[i] = True
                            logger.info(f"Zeroing out month {month} for {key} based on binary model prediction: {order_probabilities[i]:.2f}")
            else:
                # If no binary model, use only month patterns
                zero_order_mask = np.zeros(len(future_dates), dtype=bool)

                # Apply month-specific patterns
                for i, date in enumerate(future_dates):
                    month = date.month

                    # Check if this month historically has low order frequency
                    month_frequency = monthly_patterns.get(month, 1.0)  # Default to 100% if no data

                    # If the customer orders in this month less than 40% of the time, predict zero
                    if month_frequency < 0.4:
                        zero_order_mask[i] = True
                        logger.info(f"Zeroing out month {month} for {key} due to low historical frequency: {month_frequency:.2f}")

            # Generate quantity predictions
            predictions = model.predict(X_future_scaled)

            # Special handling for ********** which has very large quantities
            if '**********' in key:
                logger.info(f"Special handling for ********** product: {key}")
                # Get historical data for more accurate predictions
                if 'data' in model_data and model_data['data'] is not None:
                    historical_data = model_data['data']
                    if 'y' in historical_data.columns:
                        # Get non-zero orders
                        non_zero_orders = historical_data[historical_data['y'] > 0]['y']
                        if len(non_zero_orders) > 0:
                            # Calculate statistics from recent orders
                            recent_orders = non_zero_orders.tail(min(12, len(non_zero_orders)))
                            min_order = non_zero_orders.min()
                            recent_mean = recent_orders.mean()

                            # Override predictions with values based on historical data
                            for i in range(len(predictions)):
                                if not zero_order_mask[i]:
                                    # Use month-specific patterns if available
                                    month = future_dates[i].month
                                    month_orders = historical_data[historical_data['ds'].dt.month == month]['y']
                                    month_orders = month_orders[month_orders > 0]

                                    if len(month_orders) >= 2:  # If we have enough month-specific data
                                        # Use month-specific mean
                                        predictions[i] = month_orders.mean()
                                        logger.info(f"Using month-specific mean for ********** on {future_dates[i]}: {predictions[i]}")
                                    else:
                                        # Use overall mean
                                        predictions[i] = recent_mean
                                        logger.info(f"Using overall mean for ********** on {future_dates[i]}: {predictions[i]}")

                                    # Round to nearest 500
                                    predictions[i] = round(predictions[i] / 500) * 500

            # Get historical data statistics for post-processing
            historical_data = model_data.get('data')
            if historical_data is not None and 'y' in historical_data.columns:
                # Check if this is a large-quantity product
                is_large_quantity = False
                mean_quantity = 0
                if 'is_large_quantity' in historical_data.columns:
                    is_large_quantity = historical_data['is_large_quantity'].iloc[0]
                    mean_quantity = historical_data['mean_quantity'].iloc[0] if 'mean_quantity' in historical_data.columns else 0
                else:
                    # Calculate mean quantity if not already stored
                    non_zero_orders = historical_data[historical_data['y'] > 0]['y']
                    if len(non_zero_orders) > 0:
                        mean_quantity = non_zero_orders.mean()
                        is_large_quantity = mean_quantity > 1000  # Same threshold as in preprocessing

                # Get statistics of non-zero orders
                non_zero_orders = historical_data[historical_data['y'] > 0]['y']
                if len(non_zero_orders) > 0:
                    min_order = non_zero_orders.min()
                    median_order = non_zero_orders.median()
                    mean_order = non_zero_orders.mean()

                    # Post-process predictions to make them more realistic
                    for i in range(len(predictions)):
                        if not zero_order_mask[i] and predictions[i] > 0:
                            # For large quantity products, use special handling
                            if is_large_quantity:
                                # For ********** and similar products with extremely large quantities
                                if mean_quantity > 5000:
                                    # Use historical statistics directly
                                    # Calculate the typical range of orders
                                    recent_orders = non_zero_orders.tail(min(10, len(non_zero_orders)))
                                    recent_mean = recent_orders.mean()
                                    recent_std = recent_orders.std() if len(recent_orders) > 1 else recent_mean * 0.2

                                    # Generate a prediction based on historical range
                                    # Use a normal distribution centered around the recent mean
                                    import random
                                    random.seed(i)  # Use prediction index as seed for reproducibility

                                    # Generate a value within ±1.5 std of the recent mean
                                    lower_bound = max(min_order, recent_mean - 1.5 * recent_std)
                                    upper_bound = recent_mean + 1.5 * recent_std

                                    # Replace the prediction with a value from the historical distribution
                                    historical_pred = lower_bound + random.random() * (upper_bound - lower_bound)

                                    # Round to nearest 500 for very large quantities
                                    predictions[i] = round(historical_pred / 500) * 500

                                    logger.info(f"Using historical distribution for very large quantity product: {predictions[i]}")
                                else:
                                    # For moderately large quantities
                                    # Round to nearest 100 for large quantities
                                    predictions[i] = round(predictions[i] / 100) * 100

                                    # Ensure prediction is not too small compared to historical data
                                    if predictions[i] < min_order * 0.7 and predictions[i] > 0:
                                        # If prediction is less than 70% of the minimum historical order, adjust it
                                        predictions[i] = max(min_order, round(min_order * 0.8 + mean_order * 0.2))
                                        logger.info(f"Adjusted small prediction for large quantity product: {predictions[i]}")

                                    # Ensure predictions aren't too far from historical values
                                    if predictions[i] < mean_quantity * 0.5 and predictions[i] > 0:
                                        # If prediction is less than 50% of mean, adjust it
                                        predictions[i] = max(predictions[i], round(mean_quantity * 0.7))
                                        logger.info(f"Adjusted prediction for large quantity product: {predictions[i]}")
                            else:
                                # For normal quantities, use standard processing
                                # Round to nearest integer
                                predictions[i] = round(predictions[i])

                                # Ensure minimum order size
                                if 0 < predictions[i] < min_order:
                                    predictions[i] = min_order

                                # For very small datasets, bias towards historical values
                                if len(historical_data) < 10 and abs(predictions[i] - median_order) / median_order > 0.5:
                                    # If prediction is very different from median, adjust it
                                    predictions[i] = (predictions[i] + median_order) / 2

            # Apply zero mask to predictions
            predictions[zero_order_mask] = 0

            # Create forecast dataframe
            if 'binary_model' in model_data and model_data['binary_model'] is not None:
                # If we have a binary model, include order probability
                forecast = pd.DataFrame({
                    'ds': future_dates,
                    'yhat': predictions,
                    'order_probability': order_probabilities
                })
            else:
                # Otherwise, just include predictions
                forecast = pd.DataFrame({
                    'ds': future_dates,
                    'yhat': predictions
                })

            # Apply small dataset adjustments if needed
            if 'data' in model_data and len(model_data['data']) < 10:
                forecast = self._apply_small_dataset_adjustments(model_data['data'], forecast, customer, product, stats)
            else:
                # Use a simpler approach for prediction intervals
                # Calculate the historical variability
                if 'data' in model_data and len(model_data['data']) > 0:
                    historical_data = model_data['data']
                    if len(historical_data) >= 3:
                        # Calculate coefficient of variation
                        cv = historical_data['y'].std() / historical_data['y'].mean() if historical_data['y'].mean() > 0 else 0.2
                        # Use coefficient of variation to set prediction intervals
                        lower_factor = max(0.6, 1.0 - cv)
                        upper_factor = min(1.5, 1.0 + cv)
                        forecast['yhat_lower'] = forecast['yhat'] * lower_factor
                        forecast['yhat_upper'] = forecast['yhat'] * upper_factor
                    else:
                        # Default intervals for very small datasets
                        forecast['yhat_lower'] = forecast['yhat'] * 0.8
                        forecast['yhat_upper'] = forecast['yhat'] * 1.2
                else:
                    # Default intervals if no historical data available
                    forecast['yhat_lower'] = forecast['yhat'] * 0.8
                    forecast['yhat_upper'] = forecast['yhat'] * 1.2

                # Apply reasonable bounds based on history
                forecast['yhat'] = forecast['yhat'].clip(
                    lower=max(stats['min'] * 0.5, 1),
                    upper=stats['max'] * 1.5
                )

            # Round quantities to integers
            for col in ['yhat', 'yhat_lower', 'yhat_upper']:
                forecast[col] = forecast[col].round().astype(int)

            # Apply general approach for all customers/products

            # Include historical data if requested
            if include_history:
                history = data[['ds', 'y']].copy()
                history.columns = ['ds', 'yhat']
                history['yhat_lower'] = history['yhat']
                history['yhat_upper'] = history['yhat']
                forecast = pd.concat([history, forecast], ignore_index=True)

            # Store forecast
            self.forecasts[key] = forecast

            logger.info(f"Generated {len(forecast)} predictions for {key}")
            return True, f"Generated {len(forecast)} predictions", forecast

        except Exception as e:
            logger.error(f"Error generating predictions for {key}: {str(e)}")
            return False, f"Error generating predictions: {str(e)}", None

    def plot_forecast(self, customer, product, ax=None, uncertainty=True, graph_style='Bar Chart'):
        """
        Plot forecast for a specific customer-product combination.

        Args:
            customer (str): Customer name
            product (str): Product name
            ax (matplotlib.axes.Axes, optional): Axes to plot on
            uncertainty (bool): Whether to include uncertainty intervals
            graph_style (str): Style of graph to plot ('Bar Chart', 'Line Chart', etc.)

        Returns:
            matplotlib.axes.Axes: The axes with the plot
        """
        key = f"{customer}_{product}"

        if key not in self.models or key not in self.forecasts:
            if ax is None:
                fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f"No forecast available for {customer} - {product}",
                   ha='center', va='center', fontsize=12)
            return ax

        model_data = self.models[key]
        data = model_data['data']
        forecast = self.forecasts[key]
        components = model_data.get('components', None)

        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))

        # Clear the axes first
        ax.clear()

        # Determine appropriate bar width based on frequency
        freq_width_map = {
            'D': 1,    # Daily: 1 day width
            'W': 5,    # Weekly: 5 days width
            'M': 20,   # Monthly: 20 days width
            'Q': 40,   # Quarterly: 40 days width
            'Y': 120   # Yearly: 120 days width
        }

        # Get frequency from pattern if available
        bar_width = 20  # Default (monthly)
        if key in self.frequency_patterns:
            pattern = self.frequency_patterns[key]
            if pattern['frequency'] >= 300:  # Annual
                bar_width = freq_width_map['Y']
            elif pattern['frequency'] >= 90:  # Quarterly
                bar_width = freq_width_map['Q']
            elif pattern['frequency'] >= 30:  # Monthly
                bar_width = freq_width_map['M']
            elif pattern['frequency'] >= 7:  # Weekly
                bar_width = freq_width_map['W']
            else:  # Daily or custom
                bar_width = freq_width_map['D']

        # Plot based on selected style
        if graph_style == 'Line Chart':
            # Plot historical data as points and line
            ax.plot(data['ds'], data['y'], 'ko', markersize=4, alpha=0.7, label='Historical')
            ax.plot(data['ds'], data['y'], 'k-', linewidth=1, alpha=0.3)

            # Plot forecast as line
            ax.plot(forecast['ds'], forecast['yhat'], 'b-', linewidth=2, label='Forecast')

            # Plot uncertainty intervals
            if uncertainty and 'yhat_lower' in forecast.columns and 'yhat_upper' in forecast.columns:
                ax.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'],
                               color='blue', alpha=0.2, label='Uncertainty Interval')

        elif graph_style == 'Area Chart':
            # Plot historical data as area
            ax.fill_between(data['ds'], 0, data['y'], color='gray', alpha=0.5, label='Historical')

            # Plot forecast as area
            ax.fill_between(forecast['ds'], 0, forecast['yhat'], color='blue', alpha=0.5, label='Forecast')

            # Plot uncertainty intervals
            if uncertainty and 'yhat_lower' in forecast.columns and 'yhat_upper' in forecast.columns:
                ax.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'],
                               color='blue', alpha=0.2, label='Uncertainty Interval')

        elif graph_style == 'Scatter Plot':
            # Plot historical data as scatter
            ax.scatter(data['ds'], data['y'], color='gray', alpha=0.7, label='Historical')

            # Plot forecast as scatter
            ax.scatter(forecast['ds'], forecast['yhat'], color='blue', alpha=0.7, label='Forecast')

            # Plot uncertainty intervals
            if uncertainty and 'yhat_lower' in forecast.columns and 'yhat_upper' in forecast.columns:
                ax.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'],
                               color='blue', alpha=0.2, label='Uncertainty Interval')
        else:  # Default to Bar Chart
            # Plot historical data as bar chart
            ax.bar(data['ds'], data['y'],
                   width=bar_width, color='gray', alpha=0.7, label='Historical')

            # Plot forecast as bar chart
            ax.bar(forecast['ds'], forecast['yhat'],
                  width=bar_width, color='blue', alpha=0.7, label='Forecast')

            # Plot uncertainty intervals
            if uncertainty and 'yhat_lower' in forecast.columns and 'yhat_upper' in forecast.columns:
                ax.errorbar(forecast['ds'], forecast['yhat'],
                          yerr=[forecast['yhat']-forecast['yhat_lower'], forecast['yhat_upper']-forecast['yhat']],
                          fmt='none', ecolor='blue', alpha=0.3)

        # Set title and labels
        ax.set_title(f"XGBoost Order Forecast: {customer} - {product}")
        ax.set_xlabel("Date")
        ax.set_ylabel("Quantity")
        ax.grid(True, linestyle='--', alpha=0.7, axis='y')
        ax.legend()

        # Format x-axis dates
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        # Adjust layout to fit in container
        if ax.figure:
            ax.figure.tight_layout(pad=1.0)

        return ax

    def plot_components(self, customer, product, figsize=(10, 12)):
        """
        Plot the components of the forecast (trend, seasonality) similar to Prophet.

        Args:
            customer (str): Customer name
            product (str): Product name
            figsize (tuple): Figure size

        Returns:
            matplotlib.figure.Figure: The figure with component plots
        """
        key = f"{customer}_{product}"

        if key not in self.models or 'components' not in self.models[key] or self.models[key]['components'] is None:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f"No components available for {customer} - {product}",
                   ha='center', va='center', fontsize=12)
            return fig

        components = self.models[key]['components']
        forecast = self.forecasts.get(key, None)

        # Create figure with subplots
        fig, axes = plt.subplots(3, 1, figsize=figsize, sharex=True)

        # Plot trend
        axes[0].plot(components['ds'], components['trend'], 'b-')
        axes[0].set_title('Trend')
        axes[0].grid(True, linestyle='--', alpha=0.7)

        # Plot seasonality
        axes[1].plot(components['ds'], components['seasonality'], 'g-')
        axes[1].set_title('Seasonality')
        axes[1].grid(True, linestyle='--', alpha=0.7)

        # Plot residuals
        axes[2].scatter(components['ds'], components['residual'], alpha=0.5, s=10)
        axes[2].axhline(y=0, color='r', linestyle='-', alpha=0.3)
        axes[2].set_title('Residuals')
        axes[2].grid(True, linestyle='--', alpha=0.7)

        # Format x-axis dates
        for ax in axes:
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        # Add overall title
        fig.suptitle(f"XGBoost Components: {customer} - {product}", fontsize=14)

        # Adjust layout
        fig.tight_layout(rect=[0, 0, 1, 0.97])  # Leave space for suptitle

        return fig

    def export_components(self, customer, product, file_path):
        """
        Export forecast components to CSV file.

        Args:
            customer (str): Customer name
            product (str): Product name
            file_path (str): Path to save the components

        Returns:
            tuple: (success, message)
        """
        key = f"{customer}_{product}"

        if key not in self.models or 'components' not in self.models[key] or self.models[key]['components'] is None:
            return False, f"No components available for {customer} - {product}"

        try:
            components = self.models[key]['components']

            # Create DataFrame with components
            df = pd.DataFrame({
                'ds': components['ds'],
                'trend': components['trend'],
                'seasonality': components['seasonality'],
                'residual': components['residual'],
                'y': components['y']
            })

            # Ensure directory exists
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)

            # Export to CSV
            df.to_csv(file_path, index=False)

            return True, f"Components exported to {file_path}"

        except Exception as e:
            logger.error(f"Error exporting components: {str(e)}")
            return False, f"Error exporting components: {str(e)}"

    def export_forecast(self, customer, product, file_path):
        """
        Export forecast to CSV file.

        Args:
            customer (str): Customer name
            product (str): Product name
            file_path (str): Path to save the forecast

        Returns:
            tuple: (success, message)
        """
        key = f"{customer}_{product}"

        if key not in self.forecasts:
            return False, f"No forecast available for {customer} - {product}"

        try:
            forecast = self.forecasts[key]

            # Ensure directory exists
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)

            # Export to CSV
            forecast.to_csv(file_path, index=False)

            return True, f"Forecast exported to {file_path}"

        except Exception as e:
            logger.error(f"Error exporting forecast: {str(e)}")
            return False, f"Error exporting forecast: {str(e)}"

    def _apply_small_dataset_adjustments(self, data, forecast, customer, product, stats):
        """
        Apply special adjustments for small datasets to improve quantity predictions.

        Args:
            data (pandas.DataFrame): Historical data (ds, y)
            forecast (pandas.DataFrame): Forecast dataframe to modify
            customer (str): Customer name
            product (str): Product name
            stats (dict): Statistics about the historical data

        Returns:
            pandas.DataFrame: Modified forecast with adjustments for small datasets
        """
        logger.info(f"Applying XGBoost small dataset adjustments for {customer}_{product}")

        # Get historical stats
        mean_quantity = data['y'].mean()
        median_quantity = data['y'].median()
        min_quantity = data['y'].min()
        max_quantity = data['y'].max()
        std_quantity = data['y'].std()

        # For very small datasets, use a blend of XGBoost and simple average/median
        if len(data) < 7:
            # Calculate weight (less weight to XGBoost with smaller datasets)
            xgboost_weight = max(0.4, len(data) / 10)
            simple_weight = 1 - xgboost_weight

            logger.info(f"Using weighted blend: XGBoost {xgboost_weight:.2f}, Simple {simple_weight:.2f}")

            # Use median for more robustness with small datasets
            simple_pred = median_quantity

            # Blend predictions
            for i in range(len(forecast)):
                # Blend predictions with more weight on historical data for small datasets
                forecast.loc[i, 'yhat'] = (xgboost_weight * forecast.loc[i, 'yhat']) + (simple_weight * simple_pred)

                # Add small variations based on historical patterns
                # Seed for reproducibility
                np.random.seed(hash(f"{customer}_{product}_{i}") % 10000)

                # Add variation similar to historical patterns but reduced for stability
                variation_scale = max(std_quantity * 0.3, 0.05 * mean_quantity)
                variation = np.random.normal(0, variation_scale)
                forecast.loc[i, 'yhat'] += variation

        # Ensure predictions are within tighter bounds of historical data for small datasets
        # This prevents extreme predictions with limited data
        forecast['yhat'] = forecast['yhat'].clip(
            lower=max(min_quantity * 0.8, 1),  # Ensure at least 1
            upper=max_quantity * 1.2  # Allow only 20% growth from historical max
        )

        # For prediction intervals with small datasets
        forecast['yhat_lower'] = forecast['yhat'] * 0.8
        forecast['yhat_upper'] = forecast['yhat'] * 1.2

        return forecast



    def get_feature_importance(self, customer, product):
        """
        Get feature importance for a specific customer-product model.

        Args:
            customer (str): Customer name
            product (str): Product name

        Returns:
            tuple: (success, message, feature_importance)
        """
        key = f"{customer}_{product}"

        if key not in self.models:
            return False, f"No model found for {customer} - {product}", None

        try:
            model_data = self.models[key]
            model = model_data['model']
            features = model_data['features']

            # Get feature importance
            importance = model.feature_importances_

            # Create dataframe
            feature_importance = pd.DataFrame({
                'Feature': features,
                'Importance': importance
            }).sort_values('Importance', ascending=False)

            return True, "Feature importance retrieved", feature_importance

        except Exception as e:
            logger.error(f"Error getting feature importance: {str(e)}")
            return False, f"Error getting feature importance: {str(e)}", None

    def plot_feature_importance(self, customer, product, ax=None, top_n=10):
        """
        Plot feature importance for a specific customer-product model.

        Args:
            customer (str): Customer name
            product (str): Product name
            ax (matplotlib.axes.Axes, optional): Axes to plot on
            top_n (int): Number of top features to show

        Returns:
            matplotlib.axes.Axes: The axes with the plot
        """
        success, message, feature_importance = self.get_feature_importance(customer, product)

        if not success:
            if ax is None:
                fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, message, ha='center', va='center', fontsize=12)
            return ax

        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))

        # Clear the axes first
        ax.clear()

        # Get top N features
        top_features = feature_importance.head(top_n)

        # Plot horizontal bar chart
        ax.barh(top_features['Feature'], top_features['Importance'], color='skyblue')

        # Set title and labels
        ax.set_title(f"XGBoost Feature Importance: {customer} - {product}")
        ax.set_xlabel("Importance")
        ax.set_ylabel("Feature")

        # Invert y-axis to show most important at the top
        ax.invert_yaxis()

        # Adjust layout to fit in container
        if ax.figure:
            ax.figure.tight_layout(pad=1.0)

        return ax

    def get_metrics(self, customer, product):
        """Get performance metrics (MAPE, MASE, RMSE) for a specific customer-product combination.

        Args:
            customer (str): Customer name
            product (str): Product name

        Returns:
            tuple: (success, message, metrics_dict)
                success: Boolean indicating if metrics are available
                message: Descriptive message
                metrics_dict: Dictionary containing metrics (MAPE, MASE, RMSE)
        """
        key = f"{customer}_{product}"

        # Check if we have a model for this customer-product combination
        if key not in self.models:
            return False, f"No model found for {customer} - {product}", None

        # Check if metrics were already calculated
        print(f"DEBUG: Checking if metrics for {key} are already calculated")
        if hasattr(self, 'performance_metrics') and key in self.performance_metrics and 'mape' in self.performance_metrics[key]:
            metrics = self.performance_metrics[key]
            print(f"DEBUG: Found cached metrics: {metrics}")
            if metrics.get('mape') is not None:
                return True, "Metrics retrieved from cache", metrics
        else:
            print(f"DEBUG: No cached metrics found for {key}")
            # Initialize performance_metrics if it doesn't exist
            if not hasattr(self, 'performance_metrics'):
                self.performance_metrics = {}

        # Calculate metrics from scratch
        try:
            # Get the model and data
            model_data = self.models[key]
            model = model_data.get('model')
            data = model_data.get('data')
            scaler = model_data.get('scaler')

            if model is not None and data is not None and len(data) >= 5:
                print(f"DEBUG: Calculating performance metrics for {key} with {len(data)} data points")

                # Get the in-sample predictions
                X = self._create_features(data)
                X_scaled = scaler.transform(X)
                y_pred = model.predict(X_scaled)

                # Create a dataframe with actual and predicted values
                merged_data = pd.DataFrame({
                    'ds': data['ds'],
                    'y': data['y'],
                    'yhat': y_pred
                })

                # Initialize metrics dictionary
                metrics = {}

                # Calculate RMSE (Root Mean Squared Error)
                squared_errors = (merged_data['y'] - merged_data['yhat']) ** 2
                rmse = np.sqrt(squared_errors.mean())
                metrics['rmse'] = rmse
                print(f"DEBUG: RMSE calculated: {rmse}")

                # Calculate MAE (Mean Absolute Error)
                mae = np.abs(merged_data['y'] - merged_data['yhat']).mean()
                metrics['mae'] = mae

                # Calculate MASE (Mean Absolute Scaled Error)
                # First, create a naive forecast (using previous value)
                naive_data = merged_data.sort_values('ds').copy()
                naive_data['naive_forecast'] = naive_data['y'].shift(1)
                naive_data = naive_data.dropna()

                print(f"DEBUG: Naive data points for MASE: {len(naive_data)}")

                if len(naive_data) > 1:
                    # Calculate MAE of naive forecast
                    naive_errors = np.abs(naive_data['y'] - naive_data['naive_forecast'])
                    naive_mae = naive_errors.mean()
                    print(f"DEBUG: Naive MAE: {naive_mae}, Model MAE: {mae}")

                    # Calculate MASE
                    if naive_mae > 0:
                        mase = mae / naive_mae
                        metrics['mase'] = mase
                        print(f"DEBUG: MASE calculated: {mase}")
                    else:
                        # If naive MAE is zero, use a small value to avoid division by zero
                        print("DEBUG: Naive MAE is zero, using small value instead")
                        mase = mae / 0.0001
                        metrics['mase'] = mase
                else:
                    # If not enough data for naive forecast, use MAE directly
                    print("DEBUG: Not enough data for naive forecast, using MAE directly")
                    metrics['mase'] = mae

                # Ensure we have a performance_metrics dictionary
                if not hasattr(self, 'performance_metrics'):
                    self.performance_metrics = {}

                # Store the metrics for this customer-product combination
                self.performance_metrics[key] = metrics.copy()
                print(f"DEBUG: Stored metrics in performance_metrics[{key}]: {self.performance_metrics[key]}")

                # Calculate MAPE (Mean Absolute Percentage Error)
                # Filter out zeros and very small values to avoid division by zero
                mape_data = merged_data[merged_data['y'] > 0.1].copy()

                if len(mape_data) > 0:
                    # Calculate percent errors
                    mape_data['abs_percent_error'] = 100 * np.abs((mape_data['y'] - mape_data['yhat']) / mape_data['y'])
                    # Remove infinities and NaNs
                    valid_errors = mape_data['abs_percent_error'][np.isfinite(mape_data['abs_percent_error'])]

                    if len(valid_errors) > 0:
                        mape = valid_errors.mean()
                        metrics['mape'] = mape
                    else:
                        # Alternative MAPE calculation
                        avg_y = merged_data['y'].mean()
                        if avg_y > 0:
                            mape = 100 * mae / avg_y
                            metrics['mape'] = mape
                        else:
                            metrics['mape'] = None
                else:
                    # Alternative MAPE calculation
                    avg_y = merged_data['y'].mean()
                    if avg_y > 0:
                        mape = 100 * mae / avg_y
                        metrics['mape'] = mape
                    else:
                        metrics['mape'] = None

                # Store metrics for future reference
                if not hasattr(self, 'performance_metrics'):
                    self.performance_metrics = {}
                self.performance_metrics[key] = metrics

                # Create a message summarizing the metrics
                message = "Performance metrics calculated:\n"
                if 'mape' in metrics and metrics['mape'] is not None:
                    message += f"MAPE: {metrics['mape']:.2f}%\n"
                if 'mase' in metrics and metrics['mase'] is not None:
                    message += f"MASE: {metrics['mase']:.2f}\n"
                if 'rmse' in metrics and metrics['rmse'] is not None:
                    message += f"RMSE: {metrics['rmse']:.2f}"

                return True, message, metrics
        except Exception as e:
            print(f"DEBUG: Error calculating metrics: {str(e)}")
            return False, f"Error calculating metrics: {str(e)}", None

        # If no metrics are available
        return False, "Metrics not available for this model (insufficient data for calculation)", None

    def get_mape(self, customer, product):
        """Get Mean Absolute Percentage Error (MAPE) for a specific customer-product combination.

        MAPE is a measure of prediction accuracy, expressed as a percentage. Lower values
        indicate better model performance.

        Args:
            customer (str): Customer name
            product (str): Product name

        Returns:
            tuple: (success, message, mape_value)
                success: Boolean indicating if MAPE is available
                message: Descriptive message
                mape_value: The MAPE value as a percentage, or None if not available
        """
        success, message, metrics = self.get_metrics(customer, product)

        if success and metrics and 'mape' in metrics and metrics['mape'] is not None:
            return True, f"MAPE: {metrics['mape']:.2f}%", metrics['mape']

        return False, "MAPE not available", None

    def get_mase(self, customer, product):
        """Get Mean Absolute Scaled Error (MASE) for a specific customer-product combination.

        MASE is a measure of prediction accuracy relative to a naive forecast. Values less than 1
        indicate the model performs better than a naive forecast. MASE is scale-independent and
        handles small values well.

        Args:
            customer (str): Customer name
            product (str): Product name

        Returns:
            tuple: (success, message, mase_value)
                success: Boolean indicating if MASE is available
                message: Descriptive message
                mase_value: The MASE value, or None if not available
        """
        success, message, metrics = self.get_metrics(customer, product)

        if success and metrics and 'mase' in metrics and metrics['mase'] is not None:
            return True, f"MASE: {metrics['mase']:.2f}", metrics['mase']

        return False, "MASE not available", None

    def get_rmse(self, customer, product):
        """Get Root Mean Squared Error (RMSE) for a specific customer-product combination.

        RMSE is a measure of the average magnitude of errors. It gives higher weight to larger errors.

        Args:
            customer (str): Customer name
            product (str): Product name

        Returns:
            tuple: (success, message, rmse_value)
                success: Boolean indicating if RMSE is available
                message: Descriptive message
                rmse_value: The RMSE value, or None if not available
        """
        success, message, metrics = self.get_metrics(customer, product)

        if success and metrics and 'rmse' in metrics and metrics['rmse'] is not None:
            return True, f"RMSE: {metrics['rmse']:.2f}", metrics['rmse']

        return False, "RMSE not available", None