"""
Model-Agnostic Meta-Learning (MAML) module for TCI.

This module provides MAML capabilities for the TCI framework,
enabling meta-learning for fast adaptation to new tasks.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
import copy
import os
import higher

logger = logging.getLogger(__name__)


class MAML:
    """
    Model-Agnostic Meta-Learning (MAML) for TCI.
    
    This class provides methods for meta-learning using MAML, enabling
    fast adaptation to new tasks with few examples.
    
    Parameters
    ----------
    model : nn.Module
        Model to meta-train
    inner_lr : float, optional
        Learning rate for inner loop optimization, by default 0.01
    outer_lr : float, optional
        Learning rate for outer loop optimization, by default 0.001
    first_order : bool, optional
        Whether to use first-order approximation, by default False
    """
    
    def __init__(
        self,
        model: nn.Module,
        inner_lr: float = 0.01,
        outer_lr: float = 0.001,
        first_order: bool = False
    ):
        self.model = model
        self.inner_lr = inner_lr
        self.outer_lr = outer_lr
        self.first_order = first_order
        
        # Create optimizer for outer loop
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.outer_lr)
    
    def meta_train(
        self,
        task_loaders: List[Tuple[torch.utils.data.DataLoader, torch.utils.data.DataLoader]],
        n_epochs: int = 10,
        n_inner_steps: int = 5,
        device: str = "cpu",
        checkpoint_dir: Optional[str] = None,
        checkpoint_interval: int = 1
    ) -> Dict[str, List[float]]:
        """
        Meta-train the model on a set of tasks.
        
        Parameters
        ----------
        task_loaders : List[Tuple[torch.utils.data.DataLoader, torch.utils.data.DataLoader]]
            List of (support_loader, query_loader) tuples for each task
        n_epochs : int, optional
            Number of meta-training epochs, by default 10
        n_inner_steps : int, optional
            Number of inner loop optimization steps, by default 5
        device : str, optional
            Device to use for training, by default "cpu"
        checkpoint_dir : Optional[str], optional
            Directory to save checkpoints, by default None
        checkpoint_interval : int, optional
            Interval for saving checkpoints, by default 1
            
        Returns
        -------
        Dict[str, List[float]]
            Dictionary of training metrics
        """
        # Move model to device
        self.model.to(device)
        
        # Initialize metrics
        metrics = {
            "meta_loss": [],
            "pre_adapt_loss": [],
            "post_adapt_loss": [],
            "pre_adapt_acc": [],
            "post_adapt_acc": []
        }
        
        # Meta-training loop
        for epoch in range(n_epochs):
            # Initialize epoch metrics
            epoch_meta_loss = 0.0
            epoch_pre_adapt_loss = 0.0
            epoch_post_adapt_loss = 0.0
            epoch_pre_adapt_acc = 0.0
            epoch_post_adapt_acc = 0.0
            
            # Iterate over tasks
            for task_idx, (support_loader, query_loader) in enumerate(task_loaders):
                # Zero gradients
                self.optimizer.zero_grad()
                
                # Evaluate pre-adaptation performance
                pre_adapt_metrics = self._evaluate_task(self.model, query_loader, device)
                epoch_pre_adapt_loss += pre_adapt_metrics["loss"]
                epoch_pre_adapt_acc += pre_adapt_metrics["accuracy"]
                
                # Inner loop optimization
                with higher.innerloop_ctx(self.model, torch.optim.SGD(self.model.parameters(), lr=self.inner_lr), copy_initial_weights=True) as (fmodel, diffopt):
                    # Adapt the model to the task
                    for _ in range(n_inner_steps):
                        # Get a batch of support data
                        for support_data, support_labels in support_loader:
                            # Move data to device
                            support_data = support_data.to(device)
                            support_labels = support_labels.to(device)
                            
                            # Forward pass
                            support_preds = fmodel(support_data)
                            
                            # Compute loss
                            support_loss = F.cross_entropy(support_preds, support_labels)
                            
                            # Update model
                            diffopt.step(support_loss)
                            
                            # Only use one batch for adaptation
                            break
                    
                    # Evaluate post-adaptation performance on query set
                    query_loss = 0.0
                    query_correct = 0
                    query_total = 0
                    
                    for query_data, query_labels in query_loader:
                        # Move data to device
                        query_data = query_data.to(device)
                        query_labels = query_labels.to(device)
                        
                        # Forward pass
                        query_preds = fmodel(query_data)
                        
                        # Compute loss
                        batch_loss = F.cross_entropy(query_preds, query_labels)
                        query_loss += batch_loss
                        
                        # Compute accuracy
                        _, predicted = torch.max(query_preds, 1)
                        query_correct += (predicted == query_labels).sum().item()
                        query_total += query_labels.size(0)
                    
                    # Compute meta-loss
                    meta_loss = query_loss / len(query_loader)
                    
                    # Backward pass
                    meta_loss.backward()
                
                # Update model parameters
                self.optimizer.step()
                
                # Evaluate post-adaptation performance
                post_adapt_metrics = self._evaluate_task(self.model, query_loader, device, n_adapt_steps=n_inner_steps, support_loader=support_loader)
                epoch_post_adapt_loss += post_adapt_metrics["loss"]
                epoch_post_adapt_acc += post_adapt_metrics["accuracy"]
                
                # Update epoch metrics
                epoch_meta_loss += meta_loss.item()
                
                # Log progress
                logger.info(f"Epoch {epoch+1}/{n_epochs}, Task {task_idx+1}/{len(task_loaders)}, Meta Loss: {meta_loss.item():.4f}, Pre-Adapt Acc: {pre_adapt_metrics['accuracy']:.4f}, Post-Adapt Acc: {post_adapt_metrics['accuracy']:.4f}")
            
            # Calculate epoch metrics
            epoch_meta_loss /= len(task_loaders)
            epoch_pre_adapt_loss /= len(task_loaders)
            epoch_post_adapt_loss /= len(task_loaders)
            epoch_pre_adapt_acc /= len(task_loaders)
            epoch_post_adapt_acc /= len(task_loaders)
            
            # Update metrics
            metrics["meta_loss"].append(epoch_meta_loss)
            metrics["pre_adapt_loss"].append(epoch_pre_adapt_loss)
            metrics["post_adapt_loss"].append(epoch_post_adapt_loss)
            metrics["pre_adapt_acc"].append(epoch_pre_adapt_acc)
            metrics["post_adapt_acc"].append(epoch_post_adapt_acc)
            
            # Log progress
            logger.info(f"Epoch {epoch+1}/{n_epochs}, Meta Loss: {epoch_meta_loss:.4f}, Pre-Adapt Loss: {epoch_pre_adapt_loss:.4f}, Post-Adapt Loss: {epoch_post_adapt_loss:.4f}, Pre-Adapt Acc: {epoch_pre_adapt_acc:.4f}, Post-Adapt Acc: {epoch_post_adapt_acc:.4f}")
            
            # Save checkpoint
            if checkpoint_dir is not None and (epoch + 1) % checkpoint_interval == 0:
                os.makedirs(checkpoint_dir, exist_ok=True)
                checkpoint_path = os.path.join(checkpoint_dir, f"maml_checkpoint_epoch_{epoch+1}.pt")
                torch.save({
                    "epoch": epoch + 1,
                    "model_state_dict": self.model.state_dict(),
                    "optimizer_state_dict": self.optimizer.state_dict(),
                    "meta_loss": epoch_meta_loss,
                    "pre_adapt_loss": epoch_pre_adapt_loss,
                    "post_adapt_loss": epoch_post_adapt_loss,
                    "pre_adapt_acc": epoch_pre_adapt_acc,
                    "post_adapt_acc": epoch_post_adapt_acc
                }, checkpoint_path)
        
        return metrics
    
    def adapt(
        self,
        support_loader: torch.utils.data.DataLoader,
        n_steps: int = 5,
        device: str = "cpu"
    ) -> nn.Module:
        """
        Adapt the model to a new task.
        
        Parameters
        ----------
        support_loader : torch.utils.data.DataLoader
            Data loader for support set
        n_steps : int, optional
            Number of adaptation steps, by default 5
        device : str, optional
            Device to use for adaptation, by default "cpu"
            
        Returns
        -------
        nn.Module
            Adapted model
        """
        # Move model to device
        self.model.to(device)
        
        # Create a copy of the model for adaptation
        adapted_model = copy.deepcopy(self.model)
        
        # Create optimizer for adaptation
        optimizer = torch.optim.SGD(adapted_model.parameters(), lr=self.inner_lr)
        
        # Adaptation loop
        for step in range(n_steps):
            # Initialize step metrics
            step_loss = 0.0
            step_correct = 0
            step_total = 0
            
            # Iterate over support set
            for support_data, support_labels in support_loader:
                # Move data to device
                support_data = support_data.to(device)
                support_labels = support_labels.to(device)
                
                # Zero gradients
                optimizer.zero_grad()
                
                # Forward pass
                support_preds = adapted_model(support_data)
                
                # Compute loss
                loss = F.cross_entropy(support_preds, support_labels)
                
                # Backward pass
                loss.backward()
                
                # Update model
                optimizer.step()
                
                # Update metrics
                step_loss += loss.item()
                _, predicted = torch.max(support_preds, 1)
                step_correct += (predicted == support_labels).sum().item()
                step_total += support_labels.size(0)
            
            # Calculate step metrics
            step_loss /= len(support_loader)
            step_acc = step_correct / step_total if step_total > 0 else 0.0
            
            # Log progress
            logger.info(f"Adaptation Step {step+1}/{n_steps}, Loss: {step_loss:.4f}, Acc: {step_acc:.4f}")
        
        return adapted_model
    
    def _evaluate_task(
        self,
        model: nn.Module,
        data_loader: torch.utils.data.DataLoader,
        device: str,
        n_adapt_steps: int = 0,
        support_loader: Optional[torch.utils.data.DataLoader] = None
    ) -> Dict[str, float]:
        """
        Evaluate the model on a task.
        
        Parameters
        ----------
        model : nn.Module
            Model to evaluate
        data_loader : torch.utils.data.DataLoader
            Data loader for evaluation
        device : str
            Device to use for evaluation
        n_adapt_steps : int, optional
            Number of adaptation steps to perform before evaluation, by default 0
        support_loader : Optional[torch.utils.data.DataLoader], optional
            Data loader for support set, by default None
            
        Returns
        -------
        Dict[str, float]
            Dictionary of evaluation metrics
        """
        # If adaptation steps are specified, adapt the model first
        if n_adapt_steps > 0 and support_loader is not None:
            # Create a copy of the model for adaptation
            adapted_model = copy.deepcopy(model)
            
            # Create optimizer for adaptation
            optimizer = torch.optim.SGD(adapted_model.parameters(), lr=self.inner_lr)
            
            # Adaptation loop
            for _ in range(n_adapt_steps):
                # Iterate over support set
                for support_data, support_labels in support_loader:
                    # Move data to device
                    support_data = support_data.to(device)
                    support_labels = support_labels.to(device)
                    
                    # Zero gradients
                    optimizer.zero_grad()
                    
                    # Forward pass
                    support_preds = adapted_model(support_data)
                    
                    # Compute loss
                    loss = F.cross_entropy(support_preds, support_labels)
                    
                    # Backward pass
                    loss.backward()
                    
                    # Update model
                    optimizer.step()
                    
                    # Only use one batch for adaptation
                    break
            
            # Use adapted model for evaluation
            eval_model = adapted_model
        else:
            # Use original model for evaluation
            eval_model = model
        
        # Set model to evaluation mode
        eval_model.eval()
        
        # Initialize metrics
        total_loss = 0.0
        correct = 0
        total = 0
        
        # Evaluation loop
        with torch.no_grad():
            for data, labels in data_loader:
                # Move data to device
                data = data.to(device)
                labels = labels.to(device)
                
                # Forward pass
                outputs = eval_model(data)
                
                # Compute loss
                loss = F.cross_entropy(outputs, labels)
                total_loss += loss.item()
                
                # Compute accuracy
                _, predicted = torch.max(outputs, 1)
                correct += (predicted == labels).sum().item()
                total += labels.size(0)
        
        # Calculate metrics
        avg_loss = total_loss / len(data_loader)
        accuracy = correct / total if total > 0 else 0.0
        
        return {
            "loss": avg_loss,
            "accuracy": accuracy
        }
    
    def save(self, path: str):
        """
        Save the model to a file.
        
        Parameters
        ----------
        path : str
            Path to save the model
        """
        torch.save({
            "model_state_dict": self.model.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "inner_lr": self.inner_lr,
            "outer_lr": self.outer_lr,
            "first_order": self.first_order
        }, path)
    
    def load(self, path: str):
        """
        Load the model from a file.
        
        Parameters
        ----------
        path : str
            Path to load the model from
        """
        checkpoint = torch.load(path)
        self.model.load_state_dict(checkpoint["model_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.inner_lr = checkpoint["inner_lr"]
        self.outer_lr = checkpoint["outer_lr"]
        self.first_order = checkpoint["first_order"]
    
    def get_model(self) -> nn.Module:
        """
        Get the meta-trained model.
        
        Returns
        -------
        nn.Module
            Meta-trained model
        """
        return self.model
