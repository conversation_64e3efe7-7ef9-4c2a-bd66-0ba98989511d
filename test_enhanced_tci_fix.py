#!/usr/bin/env python3
"""
Test Enhanced TCI-fix

This script tests the enhanced TCI-fix model and compares it with the original
to validate the improvements in prediction accuracy.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_model():
    """Test the enhanced TCI-fix model."""
    print("🧪 Testing Enhanced TCI-fix Model")
    print("=" * 60)
    
    try:
        # Import integrations
        from tci_fix_enhanced_integration import EnhancedTCIFixIntegration
        from tci_fix_integration import TCIFixIntegration
        
        # Initialize integrations
        enhanced_integration = EnhancedTCIFixIntegration()
        original_integration = TCIFixIntegration()
        
        # Test with ADVIK product (known to work)
        customer = "ADVIK (IND)"
        product = "MECHANICAL SEAL ( BB-0030451 )"
        
        print(f"\n🎯 Testing with: {customer} - {product}")
        print("-" * 50)
        
        # Test 1: Train enhanced model
        print("🔧 Test 1: Training Enhanced Model")
        success, message = enhanced_integration.train_model(customer, product, force_retrain=True)
        
        if success:
            print(f"✅ Enhanced model training: {message}")
        else:
            print(f"❌ Enhanced model training failed: {message}")
            return
        
        # Test 2: Generate enhanced predictions
        print("\n📈 Test 2: Generating Enhanced Predictions")
        enhanced_predictions = enhanced_integration.predict_future(customer, product, periods=12)
        
        if enhanced_predictions is not None:
            print(f"✅ Enhanced predictions generated: {len(enhanced_predictions)} periods")
            print(f"   Prediction range: {enhanced_predictions['predicted_quantity'].min():,.0f} to {enhanced_predictions['predicted_quantity'].max():,.0f}")
            
            # Check for enhanced features
            if 'lower_bound' in enhanced_predictions.columns:
                print(f"✅ Uncertainty bounds included")
                avg_uncertainty = (enhanced_predictions['upper_bound'] - enhanced_predictions['lower_bound']).mean()
                print(f"   Average uncertainty range: ±{avg_uncertainty:,.0f}")
            
            if 'confidence_level' in enhanced_predictions.columns:
                print(f"✅ Confidence levels included")
                avg_confidence = enhanced_predictions['confidence_level'].mean()
                print(f"   Average confidence: {avg_confidence:.1%}")
        else:
            print("❌ Enhanced predictions failed")
            return
        
        # Test 3: Generate original predictions for comparison
        print("\n📊 Test 3: Generating Original Predictions for Comparison")
        original_predictions = original_integration.predict_future(customer, product, periods=12)
        
        if original_predictions is not None:
            print(f"✅ Original predictions generated: {len(original_predictions)} periods")
            print(f"   Prediction range: {original_predictions['predicted_quantity'].min():,.0f} to {original_predictions['predicted_quantity'].max():,.0f}")
        else:
            print("❌ Original predictions failed")
            return
        
        # Test 4: Compare predictions
        print("\n🔍 Test 4: Comparing Enhanced vs Original Predictions")
        comparison = compare_predictions(enhanced_predictions, original_predictions)
        
        if comparison:
            print("✅ Comparison completed")
            display_comparison_results(comparison)
        else:
            print("❌ Comparison failed")
        
        # Test 5: Validate enhanced features
        print("\n🎯 Test 5: Validating Enhanced Features")
        validate_enhanced_features(enhanced_predictions)
        
        # Test 6: Performance summary
        print("\n📊 Test 6: Performance Summary")
        performance = enhanced_integration.get_performance_summary()
        print(f"Performance summary: {performance}")
        
        print(f"\n🎉 Enhanced TCI-fix Testing Complete!")
        print("=" * 60)
        print("The enhanced model includes:")
        print("✅ Two-stage modeling (order/no-order + size)")
        print("✅ Bias correction")
        print("✅ Uncertainty quantification")
        print("✅ Volatility-aware modeling")
        print("✅ Ensemble approach")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

def compare_predictions(enhanced_pred, original_pred):
    """Compare enhanced and original predictions."""
    try:
        # Align predictions by date
        enhanced_df = enhanced_pred.copy()
        original_df = original_pred.copy()
        
        # Ensure both have the same date range
        min_len = min(len(enhanced_df), len(original_df))
        enhanced_df = enhanced_df.head(min_len)
        original_df = original_df.head(min_len)
        
        comparison = {
            'periods': min_len,
            'enhanced_mean': enhanced_df['predicted_quantity'].mean(),
            'original_mean': original_df['predicted_quantity'].mean(),
            'enhanced_std': enhanced_df['predicted_quantity'].std(),
            'original_std': original_df['predicted_quantity'].std(),
            'enhanced_min': enhanced_df['predicted_quantity'].min(),
            'enhanced_max': enhanced_df['predicted_quantity'].max(),
            'original_min': original_df['predicted_quantity'].min(),
            'original_max': original_df['predicted_quantity'].max(),
            'correlation': np.corrcoef(enhanced_df['predicted_quantity'], original_df['predicted_quantity'])[0,1],
            'mean_difference': enhanced_df['predicted_quantity'].mean() - original_df['predicted_quantity'].mean(),
            'enhanced_data': enhanced_df,
            'original_data': original_df
        }
        
        return comparison
        
    except Exception as e:
        logger.error(f"Error comparing predictions: {e}")
        return None

def display_comparison_results(comparison):
    """Display comparison results."""
    print(f"📊 Prediction Comparison Results:")
    print(f"   Periods compared: {comparison['periods']}")
    print(f"   Enhanced mean: {comparison['enhanced_mean']:,.0f}")
    print(f"   Original mean: {comparison['original_mean']:,.0f}")
    print(f"   Mean difference: {comparison['mean_difference']:+,.0f}")
    print(f"   Enhanced volatility: {comparison['enhanced_std']:,.0f}")
    print(f"   Original volatility: {comparison['original_std']:,.0f}")
    print(f"   Correlation: {comparison['correlation']:.3f}")
    
    # Analyze differences
    if abs(comparison['mean_difference']) > 1000:
        if comparison['mean_difference'] > 0:
            print(f"   📈 Enhanced model predicts higher values")
        else:
            print(f"   📉 Enhanced model predicts lower values")
    else:
        print(f"   ⚖️ Predictions are similar in magnitude")
    
    # Show sample predictions
    print(f"\n📋 Sample Predictions:")
    enhanced_data = comparison['enhanced_data']
    original_data = comparison['original_data']
    
    for i in range(min(5, len(enhanced_data))):
        enhanced_val = enhanced_data.iloc[i]['predicted_quantity']
        original_val = original_data.iloc[i]['predicted_quantity']
        diff = enhanced_val - original_val
        diff_pct = (diff / original_val) * 100 if original_val > 0 else 0
        
        print(f"   Period {i+1}: Enhanced={enhanced_val:6,.0f}, Original={original_val:6,.0f}, Diff={diff:+6,.0f} ({diff_pct:+5.1f}%)")

def validate_enhanced_features(predictions):
    """Validate that enhanced features are present and reasonable."""
    
    required_features = ['predicted_quantity']
    enhanced_features = ['lower_bound', 'upper_bound', 'confidence_level']
    
    print(f"🔍 Feature Validation:")
    
    # Check required features
    for feature in required_features:
        if feature in predictions.columns:
            print(f"   ✅ {feature}: Present")
        else:
            print(f"   ❌ {feature}: Missing")
    
    # Check enhanced features
    for feature in enhanced_features:
        if feature in predictions.columns:
            print(f"   ✅ {feature}: Present")
            
            # Validate values
            values = predictions[feature]
            if feature in ['lower_bound', 'upper_bound']:
                if (values >= 0).all():
                    print(f"      ✅ All values non-negative")
                else:
                    print(f"      ⚠️ Some negative values found")
            
            elif feature == 'confidence_level':
                if (values >= 0).all() and (values <= 1).all():
                    print(f"      ✅ Values in valid range [0,1]")
                else:
                    print(f"      ⚠️ Values outside valid range")
        else:
            print(f"   ⚠️ {feature}: Missing (optional)")
    
    # Validate uncertainty bounds relationship
    if 'lower_bound' in predictions.columns and 'upper_bound' in predictions.columns:
        lower = predictions['lower_bound']
        upper = predictions['upper_bound']
        predicted = predictions['predicted_quantity']
        
        if (lower <= predicted).all() and (predicted <= upper).all():
            print(f"   ✅ Uncertainty bounds are consistent")
        else:
            print(f"   ⚠️ Uncertainty bounds inconsistent")
    
    # Check for reasonable values
    predicted_values = predictions['predicted_quantity']
    if (predicted_values >= 0).all():
        print(f"   ✅ All predictions non-negative")
    else:
        print(f"   ⚠️ Some negative predictions found")
    
    # Check for variation
    cv = predicted_values.std() / (predicted_values.mean() + 1e-6)
    if cv > 0.01:  # At least 1% variation
        print(f"   ✅ Predictions show reasonable variation (CV: {cv:.3f})")
    else:
        print(f"   ⚠️ Predictions may be too flat (CV: {cv:.3f})")

def test_multiple_products():
    """Test enhanced model with multiple products."""
    print("\n🧪 Testing Multiple Products")
    print("-" * 40)
    
    # Test products
    test_cases = [
        ("ADVIK (IND)", "MECHANICAL SEAL ( BB-0030451 )"),
        ("KMI (INT)", "AIR INDUCTION SYSTEM ASSY ( EA0252-A00X3XG )"),
        # Add more test cases as needed
    ]
    
    from tci_fix_enhanced_integration import EnhancedTCIFixIntegration
    enhanced_integration = EnhancedTCIFixIntegration()
    
    results = []
    
    for customer, product in test_cases:
        print(f"\n🎯 Testing: {customer} - {product}")
        
        try:
            # Train and predict
            success, message = enhanced_integration.train_model(customer, product)
            if success:
                predictions = enhanced_integration.predict_future(customer, product, periods=6)
                if predictions is not None:
                    result = {
                        'customer': customer,
                        'product': product,
                        'success': True,
                        'predictions': len(predictions),
                        'mean_prediction': predictions['predicted_quantity'].mean(),
                        'has_uncertainty': 'lower_bound' in predictions.columns
                    }
                    print(f"   ✅ Success: {len(predictions)} predictions, mean={result['mean_prediction']:,.0f}")
                else:
                    result = {'customer': customer, 'product': product, 'success': False, 'error': 'Prediction failed'}
                    print(f"   ❌ Prediction failed")
            else:
                result = {'customer': customer, 'product': product, 'success': False, 'error': message}
                print(f"   ❌ Training failed: {message}")
            
            results.append(result)
            
        except Exception as e:
            result = {'customer': customer, 'product': product, 'success': False, 'error': str(e)}
            results.append(result)
            print(f"   ❌ Error: {e}")
    
    # Summary
    successful = [r for r in results if r['success']]
    print(f"\n📊 Multi-Product Test Summary:")
    print(f"   Successful: {len(successful)}/{len(results)}")
    
    return results

if __name__ == "__main__":
    try:
        # Test enhanced model
        test_enhanced_model()
        
        # Test multiple products
        test_multiple_products()
        
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")
        import traceback
        traceback.print_exc()
