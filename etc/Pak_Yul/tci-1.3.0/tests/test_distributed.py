"""
Tests for distributed training module.

This module contains tests for the distributed training capabilities in the TCI framework.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Optional, Union, Any

from tci.distributed import (
    ParallelCausalDiscovery,
    DistributedReplayBuffer,
    ParameterServer
)


# Define a simple model for testing
class SimpleModel(nn.Module):
    def __init__(self, input_dim=10, hidden_dim=20, output_dim=5):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
    
    def forward(self, x):
        return self.net(x)


class TestParallelCausalDiscovery:
    """Tests for ParallelCausalDiscovery."""
    
    def test_initialization(self):
        """Test initialization of ParallelCausalDiscovery."""
        # Test initialization with default parameters
        discovery = ParallelCausalDiscovery()
        assert discovery.discovery_method == "pc"
        assert discovery.n_workers > 0
        assert not discovery.use_gpu
        assert discovery.device_ids is None
        assert discovery.chunk_size == 1000
        assert discovery.backend == "process"
        assert len(discovery.discovery_instances) == discovery.n_workers
        
        # Test initialization with custom parameters
        discovery = ParallelCausalDiscovery(
            discovery_method="lingam",
            n_workers=2,
            use_gpu=False,
            device_ids=[0, 1],
            chunk_size=500,
            backend="thread"
        )
        assert discovery.discovery_method == "lingam"
        assert discovery.n_workers == 2
        assert not discovery.use_gpu
        assert discovery.device_ids == [0, 1]
        assert discovery.chunk_size == 500
        assert discovery.backend == "thread"
        assert len(discovery.discovery_instances) == discovery.n_workers
    
    def test_discover(self):
        """Test discover method."""
        # Create discovery instance
        discovery = ParallelCausalDiscovery(
            discovery_method="pc",
            n_workers=2,
            use_gpu=False,
            chunk_size=100,
            backend="thread"
        )
        
        # Generate synthetic data
        np.random.seed(42)
        n_samples = 200
        n_variables = 3
        
        # Create causal graph
        G = nx.DiGraph()
        G.add_nodes_from(range(n_variables))
        G.add_edges_from([(0, 1), (1, 2)])
        
        # Generate data
        data = np.zeros((n_samples, n_variables))
        data[:, 0] = np.random.randn(n_samples)
        data[:, 1] = 0.5 * data[:, 0] + 0.1 * np.random.randn(n_samples)
        data[:, 2] = 0.5 * data[:, 1] + 0.1 * np.random.randn(n_samples)
        
        # Discover causal graph
        variables = [f"X{i}" for i in range(n_variables)]
        graph = discovery.discover(data, variables=variables, alpha=0.05)
        
        # Check that graph is a DiGraph
        assert isinstance(graph, nx.DiGraph)
        
        # Check that graph has the correct number of nodes
        assert len(graph.nodes) == n_variables
    
    def test_combine_graphs(self):
        """Test _combine_graphs method."""
        # Create discovery instance
        discovery = ParallelCausalDiscovery()
        
        # Create test graphs
        G1 = nx.DiGraph()
        G1.add_nodes_from(["X0", "X1", "X2"])
        G1.add_edges_from([("X0", "X1"), ("X1", "X2")])
        
        G2 = nx.DiGraph()
        G2.add_nodes_from(["X0", "X1", "X2"])
        G2.add_edges_from([("X0", "X1"), ("X0", "X2")])
        
        G3 = nx.DiGraph()
        G3.add_nodes_from(["X0", "X1", "X2"])
        G3.add_edges_from([("X0", "X1")])
        
        # Combine graphs
        variables = ["X0", "X1", "X2"]
        combined_graph = discovery._combine_graphs([G1, G2, G3], variables)
        
        # Check that combined graph is a DiGraph
        assert isinstance(combined_graph, nx.DiGraph)
        
        # Check that combined graph has the correct number of nodes
        assert len(combined_graph.nodes) == 3
        
        # Check that combined graph has the correct edges
        # Edge (X0, X1) appears in all graphs, so it should be in the combined graph
        assert ("X0", "X1") in combined_graph.edges
        
        # Edge (X1, X2) appears in only one graph, so it should not be in the combined graph
        assert ("X1", "X2") not in combined_graph.edges
        
        # Edge (X0, X2) appears in only one graph, so it should not be in the combined graph
        assert ("X0", "X2") not in combined_graph.edges


class TestDistributedReplayBuffer:
    """Tests for DistributedReplayBuffer."""
    
    def test_initialization(self):
        """Test initialization of DistributedReplayBuffer."""
        # Test initialization with default parameters
        buffer = DistributedReplayBuffer(capacity=1000, n_workers=2, shared_memory=False)
        assert buffer.capacity == 1000
        assert buffer.n_workers == 2
        assert buffer.alpha == 0.6
        assert buffer.beta == 0.4
        assert not buffer.use_prioritized
        assert not buffer.shared_memory
        assert len(buffer.worker_buffers) == buffer.n_workers
        assert buffer.worker_capacity == buffer.capacity // buffer.n_workers
        
        # Test initialization with custom parameters
        buffer = DistributedReplayBuffer(
            capacity=2000,
            n_workers=4,
            alpha=0.7,
            beta=0.5,
            use_prioritized=True,
            shared_memory=False
        )
        assert buffer.capacity == 2000
        assert buffer.n_workers == 4
        assert buffer.alpha == 0.7
        assert buffer.beta == 0.5
        assert buffer.use_prioritized
        assert not buffer.shared_memory
        assert len(buffer.worker_buffers) == buffer.n_workers
        assert buffer.worker_capacity == buffer.capacity // buffer.n_workers
    
    def test_add_sample(self):
        """Test add and sample methods."""
        # Create buffer
        buffer = DistributedReplayBuffer(capacity=1000, n_workers=2, shared_memory=False)
        
        # Add transitions
        for i in range(100):
            state = np.random.randn(4, 84, 84)
            action = np.random.randint(0, 4)
            reward = np.random.randn()
            next_state = np.random.randn(4, 84, 84)
            done = np.random.randint(0, 2)
            
            buffer.add(state, action, reward, next_state, done)
        
        # Check buffer size
        assert len(buffer) > 0
        
        # Sample from buffer
        batch = buffer.sample(32)
        
        # Check batch
        assert isinstance(batch, dict)
        assert "states" in batch
        assert "actions" in batch
        assert "rewards" in batch
        assert "next_states" in batch
        assert "dones" in batch
        
        assert batch["states"].shape[0] == 32
        assert batch["actions"].shape[0] == 32
        assert batch["rewards"].shape[0] == 32
        assert batch["next_states"].shape[0] == 32
        assert batch["dones"].shape[0] == 32


class TestParameterServer:
    """Tests for ParameterServer."""
    
    def test_initialization(self):
        """Test initialization of ParameterServer."""
        # Create model
        model = SimpleModel()
        
        # Test initialization with default parameters
        server = ParameterServer(model, n_workers=2)
        assert server.model == model
        assert server.n_workers == 2
        assert isinstance(server.optimizer, torch.optim.Adam)
        assert server.lr == 0.001
        assert not server.use_gpu
        assert server.device_ids is None
        assert server.update_method == "async"
        assert server.device == torch.device("cpu")
        assert len(server.worker_models) == server.n_workers
        assert len(server.grad_queues) == server.n_workers
        assert len(server.param_queues) == server.n_workers
        
        # Close server
        server.close()
        
        # Test initialization with custom parameters
        optimizer = torch.optim.SGD(model.parameters(), lr=0.01)
        server = ParameterServer(
            model=model,
            n_workers=4,
            optimizer=optimizer,
            lr=0.01,
            use_gpu=False,
            device_ids=[0, 1],
            update_method="sync"
        )
        assert server.model == model
        assert server.n_workers == 4
        assert server.optimizer == optimizer
        assert server.lr == 0.01
        assert not server.use_gpu
        assert server.device_ids == [0, 1]
        assert server.update_method == "sync"
        assert server.device == torch.device("cpu")
        assert len(server.worker_models) == server.n_workers
        assert len(server.grad_queues) == server.n_workers
        assert len(server.param_queues) == server.n_workers
        
        # Close server
        server.close()
    
    def test_get_set_parameters(self):
        """Test _get_parameters and _set_parameters methods."""
        # Create model
        model = SimpleModel()
        
        # Create server
        server = ParameterServer(model, n_workers=2)
        
        # Get parameters
        parameters = server._get_parameters()
        
        # Check parameters
        assert isinstance(parameters, list)
        assert len(parameters) > 0
        assert all(isinstance(param, torch.Tensor) for param in parameters)
        
        # Create new model
        new_model = SimpleModel()
        
        # Set parameters
        server._set_parameters(new_model, parameters)
        
        # Check that parameters were set correctly
        for param1, param2 in zip(model.parameters(), new_model.parameters()):
            assert torch.allclose(param1.data, param2.data)
        
        # Close server
        server.close()
    
    def test_push_pull_parameters(self):
        """Test push_gradients and pull_parameters methods."""
        # Create model
        model = SimpleModel()
        
        # Create server
        server = ParameterServer(model, n_workers=2)
        
        # Create dummy gradients
        gradients = [torch.randn_like(param) for param in model.parameters()]
        
        # Push gradients
        server.push_gradients(0, gradients)
        
        # Pull parameters
        parameters = server.pull_parameters(0)
        
        # Check parameters
        assert isinstance(parameters, list)
        assert len(parameters) > 0
        assert all(isinstance(param, torch.Tensor) for param in parameters)
        
        # Close server
        server.close()
