"""
Example of Reinforcement Learning from Human Feedback (RLHF) in TCI.

This script demonstrates how to use the RLHF tools in the TCI framework.
"""

import sys
import os
import argparse
import logging
import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable

# Add the parent directory to the path so we can import the tci package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tci.rlhf import (
    BinaryFeedbackInterface,
    ScalarFeedbackInterface,
    PreferenceFeedbackInterface,
    RankingFeedbackInterface,
    RewardModel,
    PreferenceRewardModel,
    RLHFAgent,
    ActiveLearningRLHFAgent,
    CausalRLHFAgent,
    collect_binary_feedback,
    collect_scalar_feedback,
    collect_preference_feedback,
    collect_ranking_feedback,
    visualize_feedback,
    create_rlhf_agent,
    train_rlhf_agent
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def generate_random_state(n_vars: int = 5) -> Dict[str, float]:
    """
    Generate a random state.

    Parameters
    ----------
    n_vars : int, optional
        Number of state variables, by default 5

    Returns
    -------
    Dict[str, float]
        Random state
    """
    state = {}

    for i in range(n_vars):
        state[i] = np.random.uniform(0.0, 1.0)

    return state


def generate_random_trajectory(n_steps: int = 10, n_vars: int = 5, n_actions: int = 3) -> Dict[str, Any]:
    """
    Generate a random trajectory.

    Parameters
    ----------
    n_steps : int, optional
        Number of steps in the trajectory, by default 10
    n_vars : int, optional
        Number of state variables, by default 5
    n_actions : int, optional
        Number of actions, by default 3

    Returns
    -------
    Dict[str, Any]
        Random trajectory
    """
    states = []
    actions = []
    rewards = []

    state = generate_random_state(n_vars)

    for _ in range(n_steps):
        # Add state
        states.append(state)

        # Choose random action
        action = np.random.randint(0, n_actions)
        actions.append(action)

        # Generate random reward
        reward = np.random.uniform(0.0, 1.0)
        rewards.append(reward)

        # Generate next state
        next_state = {}

        for i in range(n_vars):
            next_state[i] = np.clip(state[i] + np.random.normal(0, 0.1), 0.0, 1.0)

        state = next_state

    return {
        "states": states,
        "actions": actions,
        "rewards": rewards
    }


def generate_random_causal_graph(n_nodes: int = 5, edge_probability: float = 0.3) -> nx.DiGraph:
    """
    Generate a random causal graph.

    Parameters
    ----------
    n_nodes : int, optional
        Number of nodes, by default 5
    edge_probability : float, optional
        Probability of an edge between two nodes, by default 0.3

    Returns
    -------
    nx.DiGraph
        Random causal graph
    """
    # Create graph
    G = nx.DiGraph()

    # Add nodes
    for i in range(n_nodes):
        G.add_node(i)

    # Add edges (ensuring acyclicity)
    for i in range(n_nodes):
        for j in range(i + 1, n_nodes):
            if np.random.random() < edge_probability:
                G.add_edge(i, j)

                # Add random weight
                G[i][j]["weight"] = np.random.uniform(0.1, 1.0)

    return G


def binary_feedback_example():
    """Example of binary feedback collection."""
    logger.info("Running binary feedback example")

    # Create feedback directory
    feedback_dir = "./feedback_binary"
    os.makedirs(feedback_dir, exist_ok=True)

    # Create feedback interface
    interface = BinaryFeedbackInterface(feedback_dir)

    # Generate random state, action, and next state
    state = generate_random_state()
    action = np.random.randint(0, 3)
    next_state = generate_random_state()
    reward = np.random.uniform(0.0, 1.0)
    done = False
    info = {}

    # Collect feedback
    feedback = interface.collect_feedback(
        state, action, next_state, reward, done, info,
        "Was this action good or bad?"
    )

    # Print feedback
    logger.info(f"Collected feedback: {feedback}")

    # Visualize feedback
    interface.visualize_feedback()


def scalar_feedback_example():
    """Example of scalar feedback collection."""
    logger.info("Running scalar feedback example")

    # Create feedback directory
    feedback_dir = "./feedback_scalar"
    os.makedirs(feedback_dir, exist_ok=True)

    # Create feedback interface
    interface = ScalarFeedbackInterface(feedback_dir, 0.0, 10.0)

    # Generate random state, action, and next state
    state = generate_random_state()
    action = np.random.randint(0, 3)
    next_state = generate_random_state()
    reward = np.random.uniform(0.0, 1.0)
    done = False
    info = {}

    # Collect feedback
    feedback = interface.collect_feedback(
        state, action, next_state, reward, done, info,
        "Rate this action from 0 to 10"
    )

    # Print feedback
    logger.info(f"Collected feedback: {feedback}")

    # Visualize feedback
    interface.visualize_feedback()


def preference_feedback_example():
    """Example of preference feedback collection."""
    logger.info("Running preference feedback example")

    # Create feedback directory
    feedback_dir = "./feedback_preference"
    os.makedirs(feedback_dir, exist_ok=True)

    # Create feedback interface
    interface = PreferenceFeedbackInterface(feedback_dir)

    # Generate random trajectories
    trajectory_a = generate_random_trajectory()
    trajectory_b = generate_random_trajectory()

    # Collect feedback
    feedback = interface.collect_feedback(
        trajectory_a["states"], trajectory_a["actions"], trajectory_a["rewards"],
        trajectory_b["states"], trajectory_b["actions"], trajectory_b["rewards"],
        "Which trajectory do you prefer?"
    )

    # Print feedback
    logger.info(f"Collected feedback: {feedback}")

    # Visualize feedback
    interface.visualize_feedback()


def ranking_feedback_example():
    """Example of ranking feedback collection."""
    logger.info("Running ranking feedback example")

    # Create feedback directory
    feedback_dir = "./feedback_ranking"
    os.makedirs(feedback_dir, exist_ok=True)

    # Create feedback interface
    interface = RankingFeedbackInterface(feedback_dir)

    # Generate random trajectories
    trajectories = [generate_random_trajectory() for _ in range(3)]

    # Collect feedback
    feedback = interface.collect_feedback(
        trajectories,
        "Rank these trajectories from best to worst"
    )

    # Print feedback
    logger.info(f"Collected feedback: {feedback}")

    # Visualize feedback
    interface.visualize_feedback()


def reward_model_example():
    """Example of reward model training."""
    logger.info("Running reward model example")

    # Create reward model
    state_dim = 5
    action_dim = 3
    model = RewardModel(state_dim, action_dim)

    # Generate random training data
    n_samples = 100
    states = np.random.rand(n_samples, state_dim)
    actions = np.random.rand(n_samples, action_dim)  # Use one-hot encoding for actions
    rewards = np.random.rand(n_samples)

    # Train model
    model.train(states, actions, rewards, n_epochs=10, batch_size=32)

    # Test model
    test_state = np.random.rand(state_dim)
    test_action = np.random.rand(action_dim)  # Use one-hot encoding for actions

    pred_reward = model.predict(test_state, test_action)

    logger.info(f"Predicted reward: {pred_reward}")

    # Save and load model
    model.save("./reward_model.npz")

    new_model = RewardModel(state_dim, action_dim)
    new_model.load("./reward_model.npz")

    new_pred_reward = new_model.predict(test_state, test_action)

    logger.info(f"Predicted reward after loading: {new_pred_reward}")


def preference_reward_model_example():
    """Example of preference reward model training."""
    logger.info("Running preference reward model example")

    # Create preference reward model
    state_dim = 5
    action_dim = 3
    model = PreferenceRewardModel(state_dim, action_dim)

    # Generate random preference data
    n_preferences = 10
    preference_data = []

    for _ in range(n_preferences):
        # Generate random trajectories
        n_steps = 5

        states_a = np.random.rand(n_steps, state_dim)
        actions_a = np.random.rand(n_steps, action_dim)  # Use one-hot encoding for actions
        rewards_a = np.random.rand(n_steps)

        states_b = np.random.rand(n_steps, state_dim)
        actions_b = np.random.rand(n_steps, action_dim)  # Use one-hot encoding for actions
        rewards_b = np.random.rand(n_steps)

        # Generate random preference
        preference = np.random.choice(["A", "B", "Equal"])

        # Create preference data
        preference_data.append({
            "preference": preference,
            "trajectory_a": {
                "states": states_a,
                "actions": actions_a,
                "rewards": rewards_a
            },
            "trajectory_b": {
                "states": states_b,
                "actions": actions_b,
                "rewards": rewards_b
            }
        })

    # Train model
    model.train_on_preferences(preference_data, n_epochs=10, batch_size=5)

    # Test model
    test_states_a = np.random.rand(n_steps, state_dim)
    test_actions_a = np.random.rand(n_steps, action_dim)  # Use one-hot encoding for actions

    test_states_b = np.random.rand(n_steps, state_dim)
    test_actions_b = np.random.rand(n_steps, action_dim)  # Use one-hot encoding for actions

    pred_preference = model.compare_trajectories(
        test_states_a, test_actions_a,
        test_states_b, test_actions_b
    )

    logger.info(f"Predicted preference: {pred_preference}")


def rlhf_agent_example():
    """Example of RLHF agent training."""
    logger.info("Running RLHF agent example")

    # Create RLHF agent
    state_dim = 5
    action_dim = 3
    feedback_dir = "./feedback_agent"

    agent = RLHFAgent(state_dim, action_dim, feedback_dir=feedback_dir)

    # Generate random binary feedback
    n_feedback = 5

    for _ in range(n_feedback):
        state = {i: np.random.rand() for i in range(state_dim)}
        action = np.random.randint(0, action_dim)
        next_state = {i: np.random.rand() for i in range(state_dim)}
        reward = np.random.rand()
        done = False
        info = {}

        # Add feedback
        agent.binary_feedback.add_feedback({
            "type": "binary",
            "value": np.random.randint(0, 2),
            "comment": "",
            "state": state,
            "action": action,
            "next_state": next_state,
            "reward": reward,
            "done": done,
            "info": info
        })

    # Generate random scalar feedback
    for _ in range(n_feedback):
        state = {i: np.random.rand() for i in range(state_dim)}
        action = np.random.randint(0, action_dim)
        next_state = {i: np.random.rand() for i in range(state_dim)}
        reward = np.random.rand()
        done = False
        info = {}

        # Add feedback
        agent.scalar_feedback.add_feedback({
            "type": "scalar",
            "value": np.random.rand(),
            "comment": "",
            "state": state,
            "action": action,
            "next_state": next_state,
            "reward": reward,
            "done": done,
            "info": info
        })

    # Train agent
    agent.train_on_feedback(n_epochs=10, batch_size=5)

    # Test agent
    test_state = np.random.rand(state_dim)
    action = agent.select_action(test_state)

    logger.info(f"Selected action: {action}")

    # Save and load agent
    agent.save("./rlhf_agent")

    new_agent = RLHFAgent(state_dim, action_dim, feedback_dir=feedback_dir)
    new_agent.load("./rlhf_agent")

    new_action = new_agent.select_action(test_state)

    logger.info(f"Selected action after loading: {new_action}")


def active_learning_agent_example():
    """Example of active learning RLHF agent."""
    logger.info("Running active learning RLHF agent example")

    # Create active learning RLHF agent
    state_dim = 5
    action_dim = 3
    feedback_dir = "./feedback_active"

    agent = ActiveLearningRLHFAgent(state_dim, action_dim, feedback_dir=feedback_dir)

    # Generate random states and actions
    n_samples = 10
    states = np.random.rand(n_samples, state_dim)
    actions = np.random.rand(n_samples, action_dim)  # Use one-hot encoding for actions

    # Select informative queries
    query_indices = agent.select_informative_queries(states, actions, n_queries=3)

    logger.info(f"Selected query indices: {query_indices}")

    # Check if should query feedback
    test_state = np.random.rand(state_dim)
    test_action = np.random.rand(action_dim)  # Use one-hot encoding for actions

    should_query = agent.should_query_feedback(test_state, test_action)

    logger.info(f"Should query feedback: {should_query}")


def causal_agent_example():
    """Example of causal RLHF agent."""
    logger.info("Running causal RLHF agent example")

    # Create causal graph
    causal_graph = generate_random_causal_graph()

    # Create causal RLHF agent
    state_dim = 5
    action_dim = 3
    feedback_dir = "./feedback_causal"

    agent = CausalRLHFAgent(state_dim, action_dim, causal_graph, feedback_dir=feedback_dir)

    # Generate random training data
    n_samples = 100
    states = np.random.rand(n_samples, state_dim)
    actions = np.random.rand(n_samples, action_dim)  # Use one-hot encoding for actions
    rewards = np.random.rand(n_samples)

    # Train causal reward model
    agent.train_causal_reward_model(states, actions, rewards, n_epochs=10, batch_size=32)

    # Generate counterfactual explanation
    test_state = np.random.rand(state_dim)
    test_action = np.random.rand(action_dim)  # Use one-hot encoding for actions
    test_reward = np.random.rand()

    explanation = agent.generate_counterfactual_explanation(test_state, test_action, test_reward)

    logger.info(f"Counterfactual explanation: {explanation}")


def convenience_functions_example():
    """Example of convenience functions."""
    logger.info("Running convenience functions example")

    # Create feedback directory
    feedback_dir = "./feedback_convenience"
    os.makedirs(feedback_dir, exist_ok=True)

    # Generate random state, action, and next state
    state = generate_random_state()
    action = np.random.randint(0, 3)
    next_state = generate_random_state()
    reward = np.random.uniform(0.0, 1.0)
    done = False
    info = {}

    # Collect binary feedback
    binary_feedback = collect_binary_feedback(
        state, action, next_state, reward, done, info, feedback_dir,
        "Was this action good or bad?"
    )

    logger.info(f"Collected binary feedback: {binary_feedback}")

    # Collect scalar feedback
    scalar_feedback = collect_scalar_feedback(
        state, action, next_state, reward, done, info, feedback_dir, 0.0, 10.0,
        "Rate this action from 0 to 10"
    )

    logger.info(f"Collected scalar feedback: {scalar_feedback}")

    # Generate random trajectories
    trajectory_a = generate_random_trajectory()
    trajectory_b = generate_random_trajectory()

    # Collect preference feedback
    preference_feedback = collect_preference_feedback(
        trajectory_a["states"], trajectory_a["actions"], trajectory_a["rewards"],
        trajectory_b["states"], trajectory_b["actions"], trajectory_b["rewards"],
        feedback_dir, "Which trajectory do you prefer?"
    )

    logger.info(f"Collected preference feedback: {preference_feedback}")

    # Generate random trajectories
    trajectories = [generate_random_trajectory() for _ in range(3)]

    # Collect ranking feedback
    ranking_feedback = collect_ranking_feedback(
        trajectories, feedback_dir, "Rank these trajectories from best to worst"
    )

    logger.info(f"Collected ranking feedback: {ranking_feedback}")

    # Visualize feedback
    visualize_feedback(feedback_dir)

    # Create RLHF agent
    state_dim = 5
    action_dim = 3

    agent = create_rlhf_agent(state_dim, action_dim, "basic", feedback_dir=feedback_dir)

    # Train agent
    train_rlhf_agent(agent, n_epochs=10, batch_size=5)

    # Test agent
    test_state = np.random.rand(state_dim)
    action = agent.select_action(test_state)

    logger.info(f"Selected action: {action}")


def main():
    """Main function to run RLHF examples."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Run RLHF examples")

    parser.add_argument("--example", type=str, default="all",
                        choices=["binary", "scalar", "preference", "ranking",
                                "reward", "preference_reward", "agent",
                                "active", "causal", "convenience", "all"],
                        help="Example to run")

    args = parser.parse_args()

    # Run selected example
    if args.example == "binary" or args.example == "all":
        binary_feedback_example()

    if args.example == "scalar" or args.example == "all":
        scalar_feedback_example()

    if args.example == "preference" or args.example == "all":
        preference_feedback_example()

    if args.example == "ranking" or args.example == "all":
        ranking_feedback_example()

    if args.example == "reward" or args.example == "all":
        reward_model_example()

    if args.example == "preference_reward" or args.example == "all":
        preference_reward_model_example()

    if args.example == "agent" or args.example == "all":
        rlhf_agent_example()

    if args.example == "active" or args.example == "all":
        active_learning_agent_example()

    if args.example == "causal" or args.example == "all":
        causal_agent_example()

    if args.example == "convenience" or args.example == "all":
        convenience_functions_example()


if __name__ == "__main__":
    main()
