{"learner": {"attributes": {"best_iteration": "99", "best_score": "0.02570146247587503"}, "feature_names": ["date", "quantity", "Unit_Price", "Total_Amount", "is_golden_week", "yen_to_dollar", "is_ram<PERSON>han", "rp_to_dollar", "lag_1", "lag_2", "lag_3", "lag_4", "lag_5", "lag_6", "rolling_mean_3", "rolling_mean_6", "month", "quarter", "year", "day_of_month", "day_of_week", "day_of_year", "week_of_year", "month_sin", "month_cos", "quarter_sin", "quarter_cos", "day_of_week_sin", "day_of_week_cos", "is_month_1", "is_month_2", "is_month_3", "is_month_4", "is_month_5", "is_month_6", "is_month_7", "is_month_8", "is_month_9", "is_month_10", "is_month_11", "is_month_12", "is_high_season", "is_low_season", "rolling_std_3", "rolling_min_3", "rolling_max_3", "rolling_std_6", "rolling_min_6", "rolling_max_6", "rolling_mean_12", "rolling_std_12", "rolling_min_12", "rolling_max_12", "diff_1", "diff_2", "momentum", "momentum_3", "momentum_6", "momentum_12", "yoy_diff", "yoy_ratio", "mom_diff", "mom_ratio", "month_target_mean", "quarter_target_mean", "seasonal_pattern", "seasonal_strength", "trend", "residual"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "100"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.010434375, -0.2959753, 2.5256202, -0.44451588, 0.3470637, 1.8356055, 3.7737405, -0.026507331, -0.012434368, 0.007300049, 0.03533991, 0.06976471, 0.112459496, 0.16185758, 0.22358702], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3945.8447, 436.09558, 471.91162, 62.047424, 61.882927, 62.4198, 59.308594, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0880593, -0.04051145, 2.7871163, -0.38900417, 0.41835797, 1.8321718, 3.8784814, -0.026507331, -0.012434368, 0.007300049, 0.03533991, 0.06976471, 0.112459496, 0.16185758, 0.22358702], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5118.0, 4563.0, 555.0, 3707.0, 856.0, 359.0, 196.0, 2578.0, 1129.0, 550.0, 306.0, 176.0, 183.0, 114.0, 82.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0063764136, -0.27762607, 2.437486, -0.41666916, 0.37783805, 1.8049042, 3.6463094, -0.025033817, -0.010703978, 0.00917984, 0.03755755, 0.06909677, 0.112769306, 0.16220908, 0.21731761], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3457.0208, 427.95807, 391.81665, 65.80731, 59.467598, 62.428833, 36.007812, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1773528, -0.0033058238, 2.9793453, -0.36916116, 0.5051711, 1.8954214, 4.0657496, -0.025033817, -0.010703978, 0.00917984, 0.03755755, 0.06909677, 0.112769306, 0.16220908, 0.21731761], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5213.0, 4693.0, 520.0, 3872.0, 821.0, 343.0, 177.0, 2736.0, 1136.0, 541.0, 280.0, 179.0, 164.0, 116.0, 61.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0059576416, -0.25223726, 2.2058434, -0.34032512, 0.64652485, 1.5792257, 3.2284067, -0.01977848, 0.0041897795, 0.02490407, 0.11542068, 0.06726101, 0.11226495, 0.13161896, 0.19400212], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2832.5637, 370.65656, 328.7356, 99.89319, 101.96936, 47.63562, 66.299805, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9549822, 0.45282102, 2.0726488, 0.24142724, 1.4298241, 1.6844897, 2.110349, -0.01977848, 0.0041897795, 0.02490407, 0.11542068, 0.06726101, 0.11226495, 0.13161896, 0.19400212], "split_indices": [59, 53, 59, 59, 53, 53, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5198.0, 4678.0, 520.0, 4261.0, 417.0, 324.0, 196.0, 3770.0, 491.0, 384.0, 33.0, 242.0, 82.0, 105.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035206748, -0.257154, 2.1569974, -0.39327085, 0.2628558, 1.5953712, 3.2503753, -0.02342973, -0.011922453, 0.0045458507, 0.030007282, 0.060071986, 0.09768828, 0.14326084, 0.20221698], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2858.2668, 330.51422, 331.3247, 43.036743, 56.140854, 49.043213, 44.985474, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0880593, -0.090615034, 2.7871163, -0.42248923, 0.3960346, 1.8321718, 4.0657496, -0.02342973, -0.011922453, 0.0045458507, 0.030007282, 0.060071986, 0.09768828, 0.14326084, 0.20221698], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5214.0, 4667.0, 547.0, 3699.0, 968.0, 363.0, 184.0, 2487.0, 1212.0, 642.0, 326.0, 175.0, 188.0, 127.0, 57.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.01229132, -0.23222095, 2.2002177, -0.3538851, 0.3756201, 1.6157306, 3.1751544, -0.021410234, -0.007819313, 0.009882993, 0.03432565, 0.06282327, 0.098812066, 0.13488509, 0.18216689], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2513.294, 347.56158, 260.98413, 57.397522, 43.24034, 35.595398, 29.066406, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2492837, 0.027698867, 2.9793453, -0.34807798, 0.5423767, 2.0442438, 3.8784814, -0.021410234, -0.007819313, 0.009882993, 0.03432565, 0.06282327, 0.098812066, 0.13488509, 0.18216689], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5163.0, 4697.0, 466.0, 3914.0, 783.0, 293.0, 173.0, 2843.0, 1071.0, 499.0, 284.0, 149.0, 144.0, 89.0, 84.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.003389607, -0.23779276, 1.9175586, -0.3623036, 0.21239848, 1.3567458, 2.8564677, -0.021553691, -0.011981628, 0.0019581167, 0.024093762, 0.051512867, 0.08396292, 0.12207576, 0.17501234], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2335.1384, 259.15714, 293.1001, 30.443878, 46.757607, 35.650208, 47.55298, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0248097, -0.12732458, 2.6382937, -0.45150962, 0.3067411, 1.6771483, 3.8784814, -0.021553691, -0.011981628, 0.0019581167, 0.024093762, 0.051512867, 0.08396292, 0.12207576, 0.17501234], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5184.0, 4621.0, 563.0, 3620.0, 1001.0, 354.0, 209.0, 2318.0, 1302.0, 610.0, 391.0, 178.0, 176.0, 130.0, 79.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.006289245, -0.20254622, 1.8329984, -0.2800679, 0.5272548, 1.2799387, 2.6307147, -0.019007314, -0.0041322545, 0.018186828, 0.07159991, 0.045414235, 0.08007344, 0.11413331, 0.16017817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1883.2067, 266.84406, 218.54883, 84.17612, 66.49688, 34.27527, 33.559937, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.95545053, 0.44303977, 1.463893, -0.36503997, 1.1444067, 0.6899671, 2.3652892, -0.019007314, -0.0041322545, 0.018186828, 0.07159991, 0.045414235, 0.08007344, 0.11413331, 0.16017817], "split_indices": [53, 59, 45, 68, 59, 45, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5215.0, 4713.0, 502.0, 4261.0, 452.0, 298.0, 204.0, 2827.0, 1434.0, 384.0, 68.0, 140.0, 158.0, 130.0, 74.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.010951246, -0.19800419, 1.9429675, -0.29673225, 0.39583415, 1.5188867, 2.9243796, -0.018064095, -0.004658456, 0.011349159, 0.032635037, 0.060428858, 0.093702674, 0.13047117, 0.17582308], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1886.659, 276.3163, 183.26367, 53.051697, 28.99794, 32.67212, 15.998779, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3360968, 0.095909186, 3.2072918, -0.3071518, 0.6415917, 2.2042282, 4.4737716, -0.018064095, -0.004658456, 0.011349159, 0.032635037, 0.060428858, 0.093702674, 0.13047117, 0.17582308], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5160.0, 4710.0, 450.0, 4039.0, 671.0, 316.0, 134.0, 3066.0, 973.0, 406.0, 265.0, 171.0, 145.0, 91.0, 43.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00900245, -0.19554308, 1.7312044, -0.30781424, 0.20604756, 1.2763344, 2.6123672, -0.018573929, -0.009852211, 0.0029986794, 0.023385832, 0.050846256, 0.08105745, 0.11710117, 0.16844665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1703.2913, 213.73094, 200.27942, 26.04776, 39.571022, 28.495422, 27.28955, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1773528, -0.11492271, 2.9793453, -0.44481263, 0.35634857, 2.0442438, 4.4737716, -0.018573929, -0.009852211, 0.0029986794, 0.023385832, 0.050846256, 0.08105745, 0.11710117, 0.16844665], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5245.0, 4738.0, 507.0, 3703.0, 1035.0, 336.0, 171.0, 2350.0, 1353.0, 665.0, 370.0, 194.0, 142.0, 129.0, 42.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0022322005, -0.19192918, 1.591359, -0.30085936, 0.15208429, 1.0939922, 2.3505225, -0.018103814, -0.010712451, 0.00039405687, 0.018778209, 0.041578174, 0.067807876, 0.09752683, 0.14012405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1581.3004, 171.18701, 208.11975, 18.315125, 35.413963, 22.195404, 34.304688, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0248097, -0.16204984, 2.6382937, -0.48077807, 0.25093263, 1.6771483, 3.5808363, -0.018103814, -0.010712451, 0.00039405687, 0.018778209, 0.041578174, 0.067807876, 0.09752683, 0.14012405], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5123.0, 4566.0, 557.0, 3468.0, 1098.0, 338.0, 219.0, 2030.0, 1438.0, 668.0, 430.0, 171.0, 167.0, 119.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00487797, -0.16400258, 1.6703061, -0.25270075, 0.33697283, 1.2640885, 2.409932, -0.015602651, -0.0040714317, 0.01062457, 0.029905349, 0.050728, 0.07858905, 0.10949504, 0.15089427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1457.1924, 209.11023, 140.26794, 40.60562, 22.887413, 22.206512, 15.864197, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3360968, 0.095909186, 3.134369, -0.31831348, 0.66949594, 2.2042282, 4.4737716, -0.015602651, -0.0040714317, 0.01062457, 0.029905349, 0.050728, 0.07858905, 0.10949504, 0.15089427], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5179.0, 4703.0, 476.0, 3996.0, 707.0, 309.0, 167.0, 2967.0, 1029.0, 480.0, 227.0, 173.0, 136.0, 126.0, 41.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0068742395, -0.15986, 1.3830858, -0.20681995, 0.4736465, 0.9702228, 2.0468352, -0.014899151, -0.0030728902, 0.017719448, 0.07518502, 0.03158832, 0.05816966, 0.08184581, 0.121522725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1195.4866, 138.34358, 152.29736, 57.32161, 38.899292, 22.08255, 29.830872, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9195261, 0.5248193, 2.0726488, -0.3950051, 1.3439696, 0.62850827, 2.0375087, -0.014899151, -0.0030728902, 0.017719448, 0.07518502, 0.03158832, 0.05816966, 0.08184581, 0.121522725], "split_indices": [59, 53, 59, 68, 53, 45, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5208.0, 4646.0, 562.0, 4326.0, 320.0, 348.0, 214.0, 2658.0, 1668.0, 288.0, 32.0, 128.0, 220.0, 106.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015055629, -0.16945979, 1.3146112, -0.26433715, 0.112073496, 0.8743117, 1.9134438, -0.015825612, -0.009493067, 0.00021077844, 0.015562306, 0.033804156, 0.054203045, 0.08037828, 0.119779326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1156.3005, 123.9426, 154.41724, 13.423584, 25.133038, 13.528442, 33.01819, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9640405, -0.18313302, 2.514275, -0.486979, 0.25093263, 1.5841343, 3.5808363, -0.015825612, -0.009493067, 0.00021077844, 0.015562306, 0.033804156, 0.054203045, 0.08037828, 0.119779326], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5229.0, 4638.0, 591.0, 3469.0, 1169.0, 342.0, 249.0, 2038.0, 1431.0, 759.0, 410.0, 178.0, 164.0, 155.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0035229265, -0.1421984, 1.4356357, -0.23022877, 0.24660563, 1.1242242, 2.1883395, -0.014280525, -0.006110225, 0.0062381416, 0.024225928, 0.0452975, 0.0715252, 0.09533765, 0.13285302], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1089.1453, 162.18799, 110.520935, 23.07576, 25.304031, 21.634613, 13.411621, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3360968, 0.0028951142, 3.2072918, -0.41008735, 0.5924803, 2.3592515, 4.4737716, -0.014280525, -0.006110225, 0.0062381416, 0.024225928, 0.0452975, 0.0715252, 0.09533765, 0.13285302], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5217.0, 4736.0, 481.0, 3862.0, 874.0, 342.0, 139.0, 2552.0, 1310.0, 579.0, 295.0, 202.0, 140.0, 90.0, 49.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0033670275, -0.15463066, 1.2280426, -0.24020316, 0.10081635, 0.8411667, 1.8297442, -0.014522954, -0.00889068, 0.00057155633, 0.014710444, 0.032490257, 0.054750193, 0.08368846, 0.12682904], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1007.52954, 100.83903, 136.96735, 10.781311, 20.014328, 16.969666, 21.332031, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9640405, -0.18933396, 2.6382937, -0.5006211, 0.28193733, 1.8321718, 4.4737716, -0.014522954, -0.00889068, 0.00057155633, 0.014710444, 0.032490257, 0.054750193, 0.08368846, 0.12682904], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5205.0, 4611.0, 594.0, 3454.0, 1157.0, 363.0, 231.0, 1911.0, 1543.0, 792.0, 365.0, 209.0, 154.0, 192.0, 39.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0030379465, -0.14971572, 1.1159526, -0.22962779, 0.07736539, 0.73282707, 1.666448, -0.013849049, -0.008651376, -0.00027785514, 0.011839889, 0.027696287, 0.045581665, 0.06921874, 0.10406778], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [867.68616, 81.45936, 128.69855, 8.850342, 15.456118, 11.170898, 26.611328, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9020311, -0.20421621, 2.514275, -0.50868225, 0.20752607, 1.4911202, 3.5808363, -0.013849049, -0.008651376, -0.00027785514, 0.011839889, 0.027696287, 0.045581665, 0.06921874, 0.10406778], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5102.0, 4487.0, 615.0, 3319.0, 1168.0, 364.0, 251.0, 1805.0, 1514.0, 769.0, 399.0, 184.0, 180.0, 152.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.001408335, -0.12119524, 1.2815614, -0.19031475, 0.24680421, 0.99502707, 1.8676184, -0.011587733, -0.0028727066, 0.006401988, 0.021612009, 0.04054339, 0.061250556, 0.08262078, 0.11450862], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [795.4648, 120.46072, 72.364746, 21.93254, 16.440033, 11.670441, 9.310272, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3981062, 0.058703557, 3.2072918, -0.30095086, 0.5924803, 2.3592515, 4.4737716, -0.011587733, -0.0028727066, 0.006401988, 0.021612009, 0.04054339, 0.061250556, 0.08262078, 0.11450862], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5174.0, 4733.0, 441.0, 3985.0, 748.0, 298.0, 143.0, 3037.0, 948.0, 457.0, 291.0, 168.0, 130.0, 98.0, 45.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.006897634, -0.12505569, 1.1260295, -0.19187267, 0.15765646, 0.8318936, 1.7127973, -0.011889851, -0.005726203, 0.0035128954, 0.017320571, 0.03272267, 0.05428977, 0.07584732, 0.110177875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [691.6845, 88.41645, 82.79883, 13.417786, 14.7403965, 14.02153, 12.228088, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1773528, -0.06283483, 3.134369, -0.4373715, 0.4853281, 2.1422188, 4.4737716, -0.011889851, -0.005726203, 0.0035128954, 0.017320571, 0.03272267, 0.05428977, 0.07584732, 0.110177875], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5165.0, 4678.0, 487.0, 3784.0, 894.0, 326.0, 161.0, 2373.0, 1411.0, 612.0, 282.0, 194.0, 132.0, 118.0, 43.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0062555303, -0.12856743, 0.9558451, -0.20023917, 0.06429542, 0.6702675, 1.487511, -0.012151315, -0.007704818, -0.00046882607, 0.0104957875, 0.02531204, 0.043313205, 0.0628119, 0.09239825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [614.5111, 64.053986, 88.431946, 6.6318054, 13.476502, 11.938705, 14.768005, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9020311, -0.22033866, 2.6382937, -0.51922387, 0.20752607, 1.6771483, 3.8784814, -0.012151315, -0.007704818, -0.00046882607, 0.0104957875, 0.02531204, 0.043313205, 0.0628119, 0.09239825], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5220.0, 4632.0, 588.0, 3377.0, 1255.0, 384.0, 204.0, 1750.0, 1627.0, 834.0, 421.0, 211.0, 173.0, 127.0, 77.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0055527473, -0.10033101, 1.1078535, -0.1591865, 0.2474338, 0.8807546, 1.6714138, -0.009882966, -0.001897507, 0.007707856, 0.02083881, 0.03531884, 0.053608518, 0.07321162, 0.10008627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [603.2981, 96.56584, 56.504578, 18.808311, 10.724213, 10.090683, 5.8970337, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3981062, 0.12815407, 3.401009, -0.28854898, 0.716003, 2.3592515, 4.4737716, -0.009882966, -0.001897507, 0.007707856, 0.02083881, 0.03531884, 0.053608518, 0.07321162, 0.10008627], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5167.0, 4715.0, 452.0, 4033.0, 682.0, 324.0, 128.0, 3061.0, 972.0, 441.0, 241.0, 172.0, 152.0, 82.0, 46.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.004606452, -0.11737861, 0.86508805, -0.17461473, 0.07438405, 0.5963089, 1.3539622, -0.011018473, -0.0064752223, 0.0008073953, 0.01059405, 0.022705808, 0.038948208, 0.061213017, 0.09309454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [510.1036, 50.54534, 77.63794, 7.290428, 8.476302, 9.69754, 11.621094, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9020311, -0.16763069, 2.6382937, -0.51500726, 0.33154485, 1.6771483, 4.4737716, -0.011018473, -0.0064752223, 0.0008073953, 0.01059405, 0.022705808, 0.038948208, 0.061213017, 0.09309454], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5199.0, 4603.0, 596.0, 3545.0, 1058.0, 386.0, 210.0, 1758.0, 1787.0, 744.0, 314.0, 219.0, 167.0, 170.0, 40.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0042912792, -0.09175748, 0.99481106, -0.14542784, 0.21427187, 0.7967292, 1.521383, -0.009190932, -0.0024509346, 0.0064736395, 0.017935865, 0.0311535, 0.048541244, 0.06652551, 0.09169238], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [488.62585, 76.931656, 46.14206, 14.736656, 8.515495, 9.432556, 4.8959045, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3981062, 0.114512, 3.401009, -0.3381565, 0.66949594, 2.3592515, 4.4737716, -0.009190932, -0.0024509346, 0.0064736395, 0.017935865, 0.0311535, 0.048541244, 0.06652551, 0.09169238], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5134.0, 4681.0, 453.0, 3983.0, 698.0, 331.0, 122.0, 2848.0, 1135.0, 441.0, 257.0, 168.0, 163.0, 79.0, 43.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-9.0158326e-05, -0.09997593, 0.83421814, -0.15585896, 0.0901216, 0.60527945, 1.2900501, -0.009835409, -0.0054210075, 0.0018010896, 0.012236527, 0.023915987, 0.0400607, 0.056247856, 0.07983831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [431.0114, 49.08273, 56.928925, 6.8967667, 8.773714, 8.820801, 7.5208435, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0248097, -0.13972646, 2.9793453, -0.49317992, 0.4555636, 2.0442438, 2.5109694, -0.009835409, -0.0054210075, 0.0018010896, 0.012236527, 0.023915987, 0.0400607, 0.056247856, 0.07983831], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5170.0, 4618.0, 552.0, 3569.0, 1049.0, 369.0, 183.0, 1916.0, 1653.0, 778.0, 271.0, 226.0, 143.0, 122.0, 61.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0012825723, -0.08825369, 0.75120735, -0.13842331, 0.09511621, 0.56905204, 1.1927929, -0.009192045, -0.004334227, 0.0029110124, 0.030181592, 0.023401173, 0.04118989, 0.05307847, 0.080016576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [346.87262, 42.469307, 43.64212, 8.499924, 18.576935, 9.703186, 6.857727, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9195261, -0.009463087, 2.2283013, -0.5520219, 0.9980155, 1.8034906, 2.9115899, -0.009192045, -0.004334227, 0.0029110124, 0.030181592, 0.023401173, 0.04118989, 0.05307847, 0.080016576], "split_indices": [59, 68, 59, 68, 53, 53, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5164.0, 4614.0, 550.0, 3623.0, 991.0, 391.0, 159.0, 1928.0, 1695.0, 925.0, 66.0, 282.0, 109.0, 123.0, 36.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004563824, -0.09592303, 0.6983602, -0.15047394, 0.048032314, 0.49712473, 1.1564671, -0.009484119, -0.0059799957, -0.00042163764, 0.007955897, 0.019480674, 0.034771893, 0.049869116, 0.075508334], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [346.9114, 35.934216, 57.042175, 3.9938278, 7.8920918, 9.024956, 9.184723, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.87102646, -0.22901997, 2.7871163, -0.53782666, 0.19016345, 1.8954214, 2.5109694, -0.009484119, -0.0059799957, -0.00042163764, 0.007955897, 0.019480674, 0.034771893, 0.049869116, 0.075508334], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5198.0, 4574.0, 624.0, 3317.0, 1257.0, 435.0, 189.0, 1459.0, 1858.0, 834.0, 423.0, 284.0, 151.0, 133.0, 56.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0039868793, -0.07676252, 0.78131723, -0.12858385, 0.1265376, 0.62310106, 1.248201, -0.008226162, -0.0040337113, 0.0026556924, 0.012653596, 0.023891237, 0.038612198, 0.05688845, 0.07952641], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [321.49982, 48.899975, 34.826904, 6.3540993, 8.747615, 7.4744263, 2.7646027, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3360968, -0.05787408, 3.401009, -0.467136, 0.4853281, 2.2042282, 4.821024, -0.008226162, -0.0040337113, 0.0026556924, 0.012653596, 0.023891237, 0.038612198, 0.05688845, 0.07952641], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5120.0, 4639.0, 481.0, 3697.0, 942.0, 361.0, 120.0, 2111.0, 1586.0, 597.0, 345.0, 185.0, 176.0, 94.0, 26.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00087484275, -0.0739802, 0.7462244, -0.12163651, 0.12334703, 0.58082384, 1.1375387, -0.0075208307, -0.0031335272, 0.003013256, 0.012413754, 0.023588417, 0.0369815, 0.05215208, 0.0761472], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [286.10678, 43.837128, 29.629944, 6.361286, 7.1328316, 5.383545, 3.4374237, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3360968, -0.028109577, 3.2072918, -0.4036384, 0.5423767, 2.3592515, 4.821024, -0.0075208307, -0.0031335272, 0.003013256, 0.012413754, 0.023588417, 0.0369815, 0.05215208, 0.0761472], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5126.0, 4659.0, 467.0, 3753.0, 906.0, 330.0, 137.0, 2521.0, 1232.0, 603.0, 303.0, 198.0, 132.0, 113.0, 24.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027144032, -0.08293643, 0.6053731, -0.13004081, 0.039013408, 0.4409152, 1.0221804, -0.008250008, -0.0051655117, -0.0003350504, 0.006769432, 0.015744714, 0.02888213, 0.045606, 0.066173546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [251.13123, 26.130476, 40.677658, 3.0489922, 5.587383, 7.261688, 4.3969116, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9020311, -0.23739123, 2.9793453, -0.54278743, 0.19016345, 1.6771483, 4.4737716, -0.008250008, -0.0051655117, -0.0003350504, 0.006769432, 0.015744714, 0.02888213, 0.045606, 0.066173546], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5146.0, 4547.0, 599.0, 3280.0, 1267.0, 431.0, 168.0, 1419.0, 1861.0, 860.0, 407.0, 226.0, 205.0, 126.0, 42.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005431313, -0.068656586, 0.571487, -0.10670467, 0.06879513, 0.4178185, 0.9011511, -0.0072471476, -0.0033808425, -0.0005360238, 0.009785914, 0.014569285, 0.027102029, 0.016844856, 0.048204888], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [201.39977, 24.163668, 27.485931, 5.39785, 10.114654, 5.76313, 5.855316, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9195261, -0.025589662, 1.7598059, -0.55967164, -0.18411402, 1.4801764, 0.9044887, -0.0072471476, -0.0033808425, -0.0005360238, 0.009785914, 0.014569285, 0.027102029, 0.016844856, 0.048204888], "split_indices": [59, 68, 45, 68, 45, 59, 68, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5167.0, 4618.0, 549.0, 3617.0, 1001.0, 376.0, 173.0, 1827.0, 1790.0, 616.0, 385.0, 188.0, 188.0, 18.0, 155.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0025943487, -0.062074836, 0.65384066, -0.09792062, 0.13849773, 0.5130788, 0.96386135, -0.0060252924, -0.0010120536, 0.0040050657, 0.011446732, 0.020150816, 0.0311687, 0.045065984, 0.06548404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [218.19922, 33.898525, 19.928558, 7.013447, 3.7577152, 3.681923, 1.9556427, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3981062, 0.071105435, 3.2072918, -0.2761471, 0.6539936, 2.2042282, 4.821024, -0.0060252924, -0.0010120536, 0.0040050657, 0.011446732, 0.020150816, 0.0311687, 0.045065984, 0.06548404], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5179.0, 4712.0, 467.0, 3998.0, 714.0, 323.0, 144.0, 3097.0, 901.0, 435.0, 279.0, 164.0, 159.0, 125.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019820405, -0.0706978, 0.5218343, -0.11274796, 0.035053354, 0.386969, 0.90465105, -0.007209559, -0.004483204, -0.00023495876, 0.0059993127, 0.014919749, 0.027423862, 0.040968683, 0.0646482], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [184.36441, 20.139301, 30.317383, 2.338768, 4.352611, 6.145035, 4.0401764, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9020311, -0.24514242, 3.134369, -0.5452678, 0.20752607, 2.0442438, 4.821024, -0.007209559, -0.004483204, -0.00023495876, 0.0059993127, 0.014919749, 0.027423862, 0.040968683, 0.0646482], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5120.0, 4527.0, 593.0, 3239.0, 1288.0, 440.0, 153.0, 1369.0, 1870.0, 878.0, 410.0, 286.0, 154.0, 128.0, 25.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0024900422, -0.054249503, 0.6180106, -0.087321356, 0.14539608, 0.491846, 0.93403894, -0.0054248692, -0.00060182315, 0.004616594, 0.011368258, 0.01967108, 0.02941897, 0.041422762, 0.057852954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [179.99873, 31.1706, 16.88472, 6.4537506, 2.897603, 2.7424088, 1.9377518, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, 0.15196566, 3.401009, -0.26808587, 0.716003, 2.3592515, 2.2323828, -0.0054248692, -0.00060182315, 0.004616594, 0.011368258, 0.01967108, 0.02941897, 0.041422762, 0.057852954], "split_indices": [1, 1, 1, 1, 1, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5152.0, 4718.0, 434.0, 4048.0, 670.0, 312.0, 122.0, 3159.0, 889.0, 408.0, 262.0, 157.0, 155.0, 86.0, 36.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0014169638, -0.052563243, 0.5959492, -0.086395435, 0.114501365, 0.46743837, 0.88891685, -0.0056826957, -0.0020830703, 0.002997852, 0.009747296, 0.017696863, 0.026884336, 0.040229693, 0.058367904], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [165.79152, 26.77853, 15.773773, 4.7984104, 3.489771, 2.2096481, 2.1130219, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, 0.052502617, 3.401009, -0.42745, 0.5924803, 2.1422188, 4.821024, -0.0056826957, -0.0020830703, 0.002997852, 0.009747296, 0.017696863, 0.026884336, 0.040229693, 0.058367904], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5164.0, 4735.0, 429.0, 3938.0, 797.0, 300.0, 129.0, 2446.0, 1492.0, 476.0, 321.0, 117.0, 183.0, 102.0, 27.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00033454353, -0.055599008, 0.5105308, -0.08805136, 0.07508212, 0.40921062, 0.8314871, -0.006178756, -0.003136518, 0.001743575, 0.008029102, 0.015062443, 0.025297478, 0.036380842, 0.051294874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [144.60803, 19.608095, 15.919434, 3.3230858, 3.1573653, 3.8372269, 1.6565628, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2740874, -0.07771708, 3.401009, -0.53410614, 0.4853281, 2.1422188, 2.9115899, -0.006178756, -0.003136518, 0.001743575, 0.008029102, 0.015062443, 0.025297478, 0.036380842, 0.051294874], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5120.0, 4621.0, 499.0, 3702.0, 919.0, 381.0, 118.0, 1539.0, 2163.0, 626.0, 293.0, 182.0, 199.0, 80.0, 38.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.001190433, -0.04609045, 0.52573377, -0.07560751, 0.11909869, 0.42358023, 0.8427536, -0.004862498, -0.0009948838, 0.004071513, 0.010208089, 0.017325452, 0.025943283, 0.038158428, 0.05814069], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [130.4768, 23.540085, 13.730362, 4.9353943, 2.328844, 2.2509956, 1.7871399, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, 0.12815407, 3.5808363, -0.335056, 0.80901706, 2.4832704, 3.1301103, -0.004862498, -0.0009948838, 0.004071513, 0.010208089, 0.017325452, 0.025943283, 0.038158428, 0.05814069], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5259.0, 4825.0, 434.0, 4094.0, 731.0, 330.0, 104.0, 2948.0, 1146.0, 508.0, 223.0, 185.0, 145.0, 86.0, 18.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0011721845, -0.04518972, 0.5136476, -0.07089965, 0.11764347, 0.4141869, 0.81471586, -0.0044371854, -0.00041293437, 0.003211356, 0.008713905, 0.016110914, 0.024652673, 0.03690402, 0.056137294], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [122.28876, 19.768291, 12.448845, 4.556408, 1.9370451, 2.1839256, 1.6499557, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, 0.15791857, 3.5808363, -0.26808587, 0.5924803, 2.3592515, 2.4305584, -0.0044371854, -0.00041293437, 0.003211356, 0.008713905, 0.016110914, 0.024652673, 0.03690402, 0.056137294], "split_indices": [1, 1, 1, 1, 1, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5145.0, 4719.0, 426.0, 4076.0, 643.0, 322.0, 104.0, 3172.0, 904.0, 332.0, 311.0, 151.0, 171.0, 86.0, 18.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072488625, -0.04891991, 0.40951106, -0.07978047, 0.044106066, 0.315849, 0.69303286, -0.0056119743, -0.0030098173, 0.0005979955, 0.0055472483, 0.011926364, 0.021235058, 0.03158654, 0.04675204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [102.11918, 13.272511, 14.2006, 2.1996918, 2.473876, 3.3495178, 1.3972778, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0248097, -0.16763069, 3.2072918, -0.5489884, 0.3439467, 2.1422188, 4.821024, -0.0056119743, -0.0030098173, 0.0005979955, 0.0055472483, 0.011926364, 0.021235058, 0.03158654, 0.04675204], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5163.0, 4621.0, 542.0, 3470.0, 1151.0, 409.0, 133.0, 1304.0, 2166.0, 778.0, 373.0, 241.0, 168.0, 109.0, 24.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041971414, -0.041733768, 0.4421381, -0.07114357, 0.070551686, 0.34995526, 0.6803051, -0.005081346, -0.0024480752, 0.0015924256, 0.0071548834, 0.013864979, 0.021471774, 0.030981688, 0.047129963], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [94.4363, 15.605049, 9.444031, 2.526657, 2.7498546, 1.7288208, 1.3574104, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3981062, -0.06283483, 3.401009, -0.5285253, 0.5051711, 2.3592515, 4.821024, -0.005081346, -0.0024480752, 0.0015924256, 0.0071548834, 0.013864979, 0.021471774, 0.030981688, 0.047129963], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5163.0, 4723.0, 440.0, 3743.0, 980.0, 319.0, 121.0, 1575.0, 2168.0, 640.0, 340.0, 169.0, 150.0, 101.0, 20.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015497338, -0.047906276, 0.33636364, -0.076146446, 0.02091194, 0.2187398, 0.5189179, -0.005162155, -0.0030254822, -9.385831e-05, 0.0035928448, 0.008681788, 0.014299634, 0.022666605, 0.03700367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [80.76658, 8.81314, 13.242538, 1.3559647, 1.5326419, 1.1030235, 3.1891327, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.25134334, 2.4832704, -0.5576697, 0.19016345, 1.5841343, 4.0657496, -0.005162155, -0.0030254822, -9.385831e-05, 0.0035928448, 0.008681788, 0.014299634, 0.022666605, 0.03700367], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5154.0, 4533.0, 621.0, 3214.0, 1319.0, 379.0, 242.0, 1174.0, 2040.0, 912.0, 407.0, 229.0, 150.0, 189.0, 53.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0010249022, -0.038099784, 0.39005035, -0.06417562, 0.06208318, 0.30635658, 0.6303884, -0.004223185, -0.0018287568, 0.0012003725, 0.006109879, 0.011520232, 0.018745242, 0.028301075, 0.042314034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [79.28322, 12.368259, 9.381523, 2.1001234, 2.2356107, 1.7618828, 1.2163391, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3360968, -0.06283483, 3.401009, -0.467136, 0.4555636, 2.1422188, 4.821024, -0.004223185, -0.0018287568, 0.0012003725, 0.006109879, 0.011520232, 0.018745242, 0.028301075, 0.042314034], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5207.0, 4732.0, 475.0, 3755.0, 977.0, 354.0, 121.0, 2163.0, 1592.0, 599.0, 378.0, 170.0, 184.0, 96.0, 25.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-7.2096416e-05, -0.03561077, 0.31321716, -0.047031574, 0.12669514, 0.23718497, 0.5301478, -0.0038882706, -0.000904518, 0.004907263, 0.019899016, 0.008551586, 0.015683318, 0.022501543, 0.0336994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [58.02982, 8.682962, 8.63242, 3.8891554, 2.3464484, 1.9448509, 1.2754784, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.89658403, 0.56718063, 2.4087744, -0.5176138, 1.5422248, 0.91759235, 2.5109694, -0.0038882706, -0.000904518, 0.004907263, 0.019899016, 0.008551586, 0.015683318, 0.022501543, 0.0336994], "split_indices": [53, 59, 53, 68, 59, 45, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5210.0, 4680.0, 530.0, 4373.0, 307.0, 394.0, 136.0, 2120.0, 2253.0, 279.0, 28.0, 213.0, 181.0, 90.0, 46.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.000851163, -0.041423004, 0.2945132, -0.06632744, 0.01982871, 0.21215177, 0.51193976, -0.0045180484, -0.0026121023, -9.3530456e-05, 0.0030937658, 0.007963042, 0.01460363, 0.02378215, 0.045824863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [62.014706, 6.942063, 11.083622, 1.0903969, 1.2008114, 1.8768082, 2.075058, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.25134334, 2.9793453, -0.5589099, 0.16163912, 1.8321718, 3.2848954, -0.0045180484, -0.0026121023, -9.3530456e-05, 0.0030937658, 0.007963042, 0.01460363, 0.02378215, 0.045824863], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5173.0, 4549.0, 624.0, 3234.0, 1315.0, 454.0, 170.0, 1193.0, 2041.0, 868.0, 447.0, 275.0, 179.0, 158.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00012573335, -0.031229656, 0.3186729, -0.042770892, 0.11271373, 0.22124404, 0.45524117, -0.0035136603, -0.0008358823, 0.0047100307, 0.016145576, 0.007879063, 0.013221261, 0.02008279, 0.031256825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [51.423836, 7.8543906, 6.033203, 3.1336193, 1.3354683, 0.70331955, 1.4877586, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0985415, 0.56718063, 1.5777057, -0.5249992, 1.5422248, 0.77191216, 2.615145, -0.0035136603, -0.0008358823, 0.0047100307, 0.016145576, 0.007879063, 0.013221261, 0.02008279, 0.031256825], "split_indices": [53, 59, 45, 68, 59, 45, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5184.0, 4724.0, 460.0, 4374.0, 350.0, 270.0, 190.0, 2127.0, 2247.0, 323.0, 27.0, 111.0, 159.0, 147.0, 43.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0015492314, -0.03788218, 0.2717875, -0.060108595, 0.01963141, 0.18797784, 0.43220705, -0.0041674497, -0.0023805485, 0.00022010847, 0.0034185324, 0.006855906, 0.011840901, 0.019624244, 0.035980556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [54.813465, 5.739563, 8.72504, 0.9365082, 0.9290105, 1.0406151, 2.2748146, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.24514242, 2.6382937, -0.5613902, 0.33154485, 1.4911202, 4.821024, -0.0041674497, -0.0023805485, 0.00022010847, 0.0034185324, 0.006855906, 0.011840901, 0.019624244, 0.035980556], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5142.0, 4488.0, 654.0, 3237.0, 1251.0, 431.0, 223.0, 1130.0, 2107.0, 954.0, 297.0, 213.0, 218.0, 198.0, 25.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0011391179, -0.022867559, 0.32100332, -0.04363289, 0.06533644, 0.27343005, 0.5375007, -0.0034888913, -0.0012488263, 0.00056758814, 0.0070456774, 0.0094963815, 0.015608706, 0.022752022, 0.038826738], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [39.707127, 8.812777, 3.571148, 1.8976312, 3.7397656, 0.90480614, 0.90813637, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4167986, 0.20611219, 3.0757031, -0.59078413, 0.02757749, 1.1383889, 2.4305584, -0.0034888913, -0.0012488263, 0.00056758814, 0.0070456774, 0.0094963815, 0.015608706, 0.022752022, 0.038826738], "split_indices": [59, 68, 59, 68, 45, 45, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5169.0, 4809.0, 360.0, 3893.0, 916.0, 297.0, 63.0, 1620.0, 2273.0, 535.0, 381.0, 96.0, 201.0, 49.0, 14.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00075644924, -0.027491355, 0.30640376, -0.05101559, 0.043527447, 0.24180469, 0.47936192, -0.0036561757, -0.0019166507, 0.0008794148, 0.004877473, 0.009456385, 0.013908577, 0.021707842, 0.034483742], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [44.291523, 7.847653, 4.7331467, 0.9863968, 1.6363869, 0.5565796, 0.8129158, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, -0.13972646, 3.401009, -0.5489884, 0.4853281, 1.1383889, 3.1301103, -0.0036561757, -0.0019166507, 0.0008794148, 0.004877473, 0.009456385, 0.013908577, 0.021707842, 0.034483742], "split_indices": [1, 1, 1, 1, 1, 45, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5128.0, 4695.0, 433.0, 3527.0, 1168.0, 317.0, 116.0, 1284.0, 2243.0, 790.0, 378.0, 132.0, 185.0, 98.0, 18.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.00010840014, -0.029110622, 0.25483775, -0.050710656, 0.027704662, 0.18944114, 0.40900013, -0.0036132708, -0.0020151767, 0.0004720805, 0.0037550384, 0.0073100924, 0.0120234145, 0.019248748, 0.036183644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [38.367516, 5.718986, 5.2658195, 0.7544012, 1.1107183, 0.7900276, 0.90833473, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0880593, -0.20421621, 3.134369, -0.5638706, 0.35634857, 2.0442438, 3.2848954, -0.0036132708, -0.0020151767, 0.0004720805, 0.0037550384, 0.0073100924, 0.0120234145, 0.019248748, 0.036183644], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5187.0, 4658.0, 529.0, 3375.0, 1283.0, 373.0, 156.0, 1097.0, 2278.0, 927.0, 356.0, 204.0, 169.0, 147.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0020474936, -0.02422044, 0.28702286, -0.04297389, 0.049110036, 0.22213359, 0.44023025, -0.0027174158, -0.0009150612, 0.0013967034, 0.0046704295, 0.0089573255, 0.0134759, 0.020089028, 0.034333494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [38.760494, 6.5219297, 4.2466507, 1.0586925, 0.90356135, 0.5811224, 0.9464531, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, -0.028109577, 3.401009, -0.39762348, 0.6539936, 2.4832704, 2.4305584, -0.0027174158, -0.0009150612, 0.0013967034, 0.0046704295, 0.0089573255, 0.0134759, 0.020089028, 0.034333494], "split_indices": [1, 1, 1, 1, 1, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5176.0, 4740.0, 436.0, 3775.0, 965.0, 308.0, 128.0, 2583.0, 1192.0, 654.0, 311.0, 164.0, 144.0, 113.0, 15.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00071657135, -0.027056063, 0.22530924, -0.04622261, 0.021213662, 0.17976874, 0.43481946, -0.0033145614, -0.0018373017, 7.921473e-05, 0.0026098017, 0.0066652643, 0.012033314, 0.01706862, 0.026447093], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [31.9672, 4.2206535, 5.30307, 0.6185069, 0.7887065, 1.2836409, 0.69898033, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9640405, -0.21413772, 3.5808363, -0.566351, 0.16163912, 2.1422188, 2.7294896, -0.0033145614, -0.0018373017, 7.921473e-05, 0.0026098017, 0.0066652643, 0.012033314, 0.01706862, 0.026447093], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5123.0, 4560.0, 563.0, 3264.0, 1296.0, 464.0, 99.0, 1045.0, 2219.0, 794.0, 502.0, 265.0, 199.0, 52.0, 47.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0007690082, -0.026261942, 0.21232626, -0.0448331, 0.017400745, 0.16858093, 0.40965334, -0.003187945, -0.0017969682, 0.000117236705, 0.0027488673, 0.005933184, 0.011016101, 0.018802185, 0.031058265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [29.67936, 3.732389, 5.0066376, 0.5412178, 0.7772167, 1.2213345, 0.5199528, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9640405, -0.25134334, 3.5808363, -0.5675912, 0.28193733, 2.0442438, 2.4305584, -0.003187945, -0.0017969682, 0.000117236705, 0.0027488673, 0.005933184, 0.011016101, 0.018802185, 0.031058265], "split_indices": [1, 1, 1, 1, 1, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5188.0, 4601.0, 587.0, 3228.0, 1373.0, 482.0, 105.0, 1030.0, 2198.0, 981.0, 392.0, 247.0, 235.0, 93.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0006534645, -0.01849432, 0.2288376, -0.029574655, 0.063002124, 0.17427896, 0.3737239, -0.002679774, -0.00080239726, 0.0022526884, 0.0124023, 0.00629036, 0.010686422, 0.01674071, 0.02811552], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [22.462025, 4.28483, 3.0809765, 1.3560171, 1.8752851, 0.5295572, 0.6042175, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2203499, 0.49272573, 2.0939074, -0.60449946, 0.8354771, 1.1383889, 3.1301103, -0.002679774, -0.00080239726, 0.0022526884, 0.0124023, 0.00629036, 0.010686422, 0.01674071, 0.02811552], "split_indices": [53, 68, 43, 68, 67, 45, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5139.0, 4742.0, 397.0, 4175.0, 567.0, 290.0, 107.0, 1503.0, 2672.0, 518.0, 49.0, 132.0, 158.0, 91.0, 16.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0003401525, -0.020607146, 0.22823071, -0.03508927, 0.044088896, 0.18325265, 0.37134996, -0.002260513, -0.0006476492, 0.0010543747, 0.003533651, 0.007476841, 0.011178491, 0.016713863, 0.03025726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [24.894733, 4.4763536, 2.7578926, 0.87384653, 0.53293645, 0.4233017, 0.69912624, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, 0.0090960525, 3.5808363, -0.38379538, 0.4555636, 2.514275, 1.9847567, -0.002260513, -0.0006476492, 0.0010543747, 0.003533651, 0.007476841, 0.011178491, 0.016713863, 0.03025726], "split_indices": [1, 1, 1, 1, 1, 1, 52, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5213.0, 4775.0, 438.0, 3902.0, 873.0, 335.0, 103.0, 2677.0, 1225.0, 469.0, 404.0, 185.0, 150.0, 91.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00078690733, -0.023924109, 0.17418294, -0.039498042, 0.012558729, 0.12627193, 0.3100347, -0.0027968308, -0.0016067518, -4.3380624e-05, 0.0019229196, 0.0049540456, 0.008697575, 0.012619731, 0.019764533], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [22.225098, 2.5800388, 4.172041, 0.38334322, 0.47260892, 0.60506344, 0.7258091, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.26808587, 3.134369, -0.57069165, 0.15196566, 1.8954214, 4.0657496, -0.0027968308, -0.0016067518, -4.3380624e-05, 0.0019229196, 0.0049540456, 0.008697575, 0.012619731, 0.019764533], "split_indices": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5185.0, 4539.0, 646.0, 3181.0, 1358.0, 479.0, 167.0, 982.0, 2199.0, 895.0, 463.0, 307.0, 172.0, 102.0, 65.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00015566431, -0.02327808, 0.16516504, -0.036952794, 0.014338047, 0.11980224, 0.29886335, -0.002649298, -0.001490169, 3.816285e-05, 0.0018554935, 0.0045783767, 0.008310271, 0.013746819, 0.028951356], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.211678, 2.354892, 3.9064007, 0.38322592, 0.37741056, 0.622839, 0.9401741, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.22901997, 3.134369, -0.5688314, 0.15196566, 1.8954214, 2.4305584, -0.002649298, -0.001490169, 3.816285e-05, 0.0018554935, 0.0045783767, 0.008310271, 0.013746819, 0.028951356], "split_indices": [1, 1, 1, 1, 1, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5225.0, 4576.0, 649.0, 3356.0, 1220.0, 486.0, 163.0, 1033.0, 2323.0, 765.0, 455.0, 304.0, 182.0, 152.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0007305482, -0.021574311, 0.1565565, -0.035176035, 0.01105553, 0.10804565, 0.24919765, -0.0024721422, -0.0014019773, 1.6497754e-05, 0.0017420443, 0.0040232735, 0.006945697, 0.010954505, 0.01747951], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.153357, 2.028212, 2.9122162, 0.326967, 0.3431136, 0.35575533, 0.6004648, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.26064476, 2.6382937, -0.5638706, 0.16163912, 1.5841343, 2.615145, -0.0024721422, -0.0014019773, 1.6497754e-05, 0.0017420443, 0.0040232735, 0.006945697, 0.010954505, 0.01747951], "split_indices": [1, 1, 1, 1, 1, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5221.0, 4568.0, 653.0, 3224.0, 1344.0, 430.0, 223.0, 1073.0, 2151.0, 927.0, 417.0, 229.0, 201.0, 174.0, 49.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00045010343, -0.018828033, 0.16384111, -0.031560775, 0.021558395, 0.13014846, 0.3091242, -0.002219383, -0.0011286497, 0.00043266267, 0.0022941453, 0.005070296, 0.009022146, 0.012336946, 0.018910293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.445427, 2.4021344, 2.6568975, 0.40837598, 0.3511836, 0.6307726, 0.3505783, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0880593, -0.15212834, 3.5808363, -0.53782666, 0.3439467, 2.4832704, 2.7294896, -0.002219383, -0.0011286497, 0.00043266267, 0.0022941453, 0.005070296, 0.009022146, 0.012336946, 0.018910293], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5219.0, 4669.0, 550.0, 3550.0, 1119.0, 448.0, 102.0, 1461.0, 2089.0, 732.0, 387.0, 287.0, 161.0, 56.0, 46.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00041608483, -0.016035847, 0.17780025, -0.028827172, 0.027973568, 0.15093109, 0.3510301, -0.00190413, -0.0008153257, 0.0006220312, 0.002735214, 0.00577482, 0.009191335, 0.014566769, 0.02533562], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.300644, 2.701786, 2.0182886, 0.4302361, 0.44815797, 0.42987347, 0.40068817, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, -0.11492271, 4.0657496, -0.47767758, 0.4555636, 2.514275, 1.9847567, -0.00190413, -0.0008153257, 0.0006220312, 0.002735214, 0.00577482, 0.009191335, 0.014566769, 0.02533562], "split_indices": [1, 1, 1, 1, 1, 1, 52, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5241.0, 4797.0, 444.0, 3717.0, 1080.0, 386.0, 58.0, 2136.0, 1581.0, 684.0, 396.0, 188.0, 198.0, 44.0, 14.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0007090901, -0.014641904, 0.17272565, -0.027785104, 0.026270881, 0.14045617, 0.30724055, -0.0019903562, -0.000954492, 0.00052071764, 0.0025286477, 0.011408402, 0.006455242, 0.013751688, 0.025420258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.633435, 2.5495458, 1.7929726, 0.37431026, 0.44377792, 0.3120494, 0.38495922, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, -0.12112365, 3.8784814, -0.53410614, 0.3960346, -0.12835729, 2.0512626, -0.0019903562, -0.000954492, 0.00052071764, 0.0025286477, 0.011408402, 0.006455242, 0.013751688, 0.025420258], "split_indices": [1, 1, 1, 1, 1, 58, 52, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5161.0, 4739.0, 422.0, 3587.0, 1152.0, 342.0, 80.0, 1504.0, 2083.0, 698.0, 454.0, 37.0, 305.0, 71.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.000339598, -0.018058414, 0.12768218, -0.030019658, 0.008793184, 0.08868748, 0.216236, -0.0021420445, -0.0012320344, 2.2675149e-05, 0.0014867191, 0.0036503181, 0.0065895277, 0.007948759, 0.012996419], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.304613, 1.4741522, 2.2702484, 0.21790838, 0.24710628, 0.3026464, 0.4640541, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.2761471, 2.7871163, -0.5737921, 0.20752607, 2.1422188, 1.8025012, -0.0021420445, -0.0012320344, 2.2675149e-05, 0.0014867191, 0.0036503181, 0.0065895277, 0.007948759, 0.012996419], "split_indices": [1, 1, 1, 1, 1, 1, 14, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5250.0, 4588.0, 662.0, 3174.0, 1414.0, 461.0, 201.0, 936.0, 2238.0, 1012.0, 402.0, 340.0, 121.0, 89.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010298215, -0.017392702, 0.11845467, -0.027981447, 0.008442268, 0.094202004, 0.23858988, -0.0020026383, -0.0011281734, -9.402765e-05, 0.0011180259, 0.0034726362, 0.006492243, 0.009983268, 0.017459055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.209693, 1.2566968, 1.8101788, 0.21217632, 0.19199264, 0.454103, 0.38227558, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.80901706, -0.26808587, 3.401009, -0.5713118, 0.052502617, 2.0442438, 2.9115899, -0.0020026383, -0.0011281734, -9.402765e-05, 0.0011180259, 0.0034726362, 0.006492243, 0.009983268, 0.017459055], "split_indices": [1, 1, 1, 1, 1, 1, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5220.0, 4592.0, 628.0, 3257.0, 1335.0, 524.0, 104.0, 1007.0, 2250.0, 767.0, 568.0, 311.0, 213.0, 79.0, 25.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00063118484, -0.012304099, 0.14693864, -0.023765232, 0.02307752, 0.11724253, 0.25918025, -0.0015475375, -0.0006435405, 0.0004091765, 0.0022366352, 0.004218437, 0.006769144, 0.02069401, 0.011167568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [9.668843, 1.9040284, 1.3528652, 0.27718997, 0.37022328, 0.18537092, 0.38423443, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5841343, -0.12112365, 3.8784814, -0.467136, 0.3960346, 1.1383889, -0.12823197, -0.0015475375, -0.0006435405, 0.0004091765, 0.0022366352, 0.004218437, 0.006769144, 0.02069401, 0.011167568], "split_indices": [1, 1, 1, 1, 1, 45, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5107.0, 4693.0, 414.0, 3545.0, 1148.0, 329.0, 85.0, 2135.0, 1410.0, 681.0, 467.0, 119.0, 210.0, 14.0, 71.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008071141, -0.015212635, 0.110509634, -0.025446173, 0.0074651605, 0.07329763, 0.17870735, -0.0018545183, -0.0010406624, -3.8373124e-05, 0.0012123666, 0.005156341, 0.0028873577, 0.015012912, 0.007751784], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.313014, 1.06541, 1.4943385, 0.16986132, 0.19731294, 0.1730938, 0.55480814, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9020311, -0.2761471, 2.6382937, -0.5737921, 0.15196566, -0.12729953, -0.12802584, -0.0018545183, -0.0010406624, -3.8373124e-05, 0.0012123666, 0.005156341, 0.0028873577, 0.015012912, 0.007751784], "split_indices": [1, 1, 1, 1, 1, 58, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5182.0, 4589.0, 593.0, 3162.0, 1427.0, 385.0, 208.0, 898.0, 2264.0, 958.0, 469.0, 130.0, 255.0, 32.0, 176.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0007734366, -0.011353303, 0.13518576, -0.021895776, 0.019731, 0.11114836, 0.22176644, -0.0015435315, -0.0007303281, 0.00036017323, 0.001971618, 0.009784346, 0.005097225, 0.009547598, 0.018360868], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.430179, 1.5550662, 0.8660941, 0.23133075, 0.29638088, 0.24226427, 0.3422227, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5841343, -0.13972646, 3.8784814, -0.5248047, 0.3960346, -0.12833369, 1.9847567, -0.0015435315, -0.0007303281, 0.00036017323, 0.001971618, 0.009784346, 0.005097225, 0.009547598, 0.018360868], "split_indices": [1, 1, 1, 1, 1, 58, 52, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5170.0, 4743.0, 427.0, 3542.0, 1201.0, 336.0, 91.0, 1586.0, 1956.0, 735.0, 466.0, 31.0, 305.0, 77.0, 14.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0013647219, -0.010700557, 0.13645867, -0.018973809, 0.0255887, 0.11263667, 0.26702946, -0.0012538624, -0.00028658696, 0.0023931004, 0.0006362611, 0.0044495105, 0.0073505477, 0.011377514, 0.021121664], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.469029, 1.4325833, 1.2949772, 0.3138504, 0.25324076, 0.28116226, 0.3026638, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5841343, 0.0090960525, 2.5109694, -0.38379538, -0.12802584, 2.7871163, 2.0512626, -0.0012538624, -0.00028658696, 0.0023931004, 0.0006362611, 0.0044495105, 0.0073505477, 0.011377514, 0.021121664], "split_indices": [1, 1, 45, 1, 58, 1, 52, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5194.0, 4769.0, 425.0, 3884.0, 885.0, 361.0, 64.0, 2658.0, 1226.0, 323.0, 562.0, 216.0, 145.0, 53.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [8.983886e-05, -0.01351953, 0.09183667, -0.021682918, 0.006286118, 0.067071676, 0.17280267, -0.0016276287, -0.0008716434, 0.000756535, -0.00016307329, 0.004958888, 0.0023899616, 0.014675026, 0.0075479667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.5052843, 0.7336838, 1.3378572, 0.1478622, 0.11193816, 0.3151598, 0.36961555, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.75320864, -0.26808587, 3.134369, -0.5750323, -0.12786321, -0.12715171, -0.12829064, -0.0016276287, -0.0008716434, 0.000756535, -0.00016307329, 0.004958888, 0.0023899616, 0.014675026, 0.0075479667], "split_indices": [1, 1, 1, 1, 60, 58, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5208.0, 4536.0, 672.0, 3212.0, 1324.0, 516.0, 156.0, 901.0, 2311.0, 687.0, 637.0, 192.0, 324.0, 22.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0009382807, -0.009675254, 0.116662614, -0.016884815, 0.024380093, 0.08999745, 0.18859586, -0.0011888309, -0.00034927722, 0.002474008, 0.0006204943, 0.006322723, 0.0036904241, 0.015392809, 0.008332753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.374488, 1.1676333, 0.81932354, 0.2675444, 0.24906233, 0.1795311, 0.2548442, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, 0.052502617, 3.401009, -0.4373715, -0.12812671, -0.12715171, -0.12829064, -0.0011888309, -0.00034927722, 0.002474008, 0.0006204943, 0.006322723, 0.0036904241, 0.015392809, 0.008332753], "split_indices": [1, 1, 1, 1, 58, 58, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5188.0, 4753.0, 435.0, 3923.0, 830.0, 319.0, 116.0, 2312.0, 1611.0, 267.0, 563.0, 96.0, 223.0, 16.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00043206077, -0.012179427, 0.08403609, -0.02055823, 0.0037627951, 0.06804889, 0.18008313, -0.0013902687, -0.0007487752, -0.00013526714, 0.00074579933, 0.0048669884, 0.002424733, 0.01654135, 0.007730493], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.4457955, 0.59956974, 1.0272951, 0.11861348, 0.11164151, 0.3286264, 0.31748462, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.716003, -0.32798696, 3.5808363, -0.47539973, 0.22955365, -0.12689097, -0.12823197, -0.0013902687, -0.0007487752, -0.00013526714, 0.00074579933, 0.0048669884, 0.002424733, 0.01654135, 0.007730493], "split_indices": [1, 1, 1, 67, 67, 58, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5163.0, 4487.0, 676.0, 2941.0, 1546.0, 581.0, 95.0, 1278.0, 1663.0, 979.0, 567.0, 231.0, 350.0, 12.0, 83.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-4.855504e-06, -0.011092665, 0.08124634, -0.017905863, 0.008293514, 0.05726349, 0.13346896, -0.0014062534, -0.0006932004, 0.00085450057, -0.00021836763, 0.00421242, 0.0021200117, 0.0062627676, 0.0154902665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.666651, 0.60217756, 0.7713795, 0.13893032, 0.13216096, 0.16809165, 0.24362516, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.87102646, -0.20421621, 2.7871163, -0.5713118, -0.12715171, -0.12715171, 3.2848954, -0.0014062534, -0.0006932004, 0.00085450057, -0.00021836763, 0.00421242, 0.0021200117, 0.0062627676, 0.0154902665], "split_indices": [1, 1, 1, 1, 58, 58, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5178.0, 4557.0, 621.0, 3372.0, 1185.0, 427.0, 194.0, 954.0, 2418.0, 699.0, 486.0, 150.0, 277.0, 187.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0007084607, -0.010672813, 0.083393194, -0.00096180645, 0.0034044785, 0.070655994, 0.19894277, -0.00019649653, 0.00075892656, 0.006614525, 0.0029476164, 0.0041741626, 0.011543962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [4.9000363, 0.5518151, 0.91316366, 0.0, 0.14962776, 0.4026494, 0.20934224, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [0.87102646, -0.35551912, 4.0657496, -0.00096180645, 0.20368527, -0.12823197, 1.3753006, -0.00019649653, 0.00075892656, 0.006614525, 0.0029476164, 0.0041741626, 0.011543962], "split_indices": [1, 1, 1, 0, 67, 58, 50, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5205.0, 4576.0, 629.0, 2845.0, 1731.0, 568.0, 61.0, 1067.0, 664.0, 89.0, 479.0, 14.0, 47.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.00036461637, -0.008433027, 0.09324094, -0.014016796, 0.019524962, 0.08096861, 0.20380412, -0.0010282934, -0.00034216748, 0.00015720575, 0.0017269863, 0.0031211488, 0.005502564, 0.017045334, 0.008193363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.221071, 0.73696125, 0.5900183, 0.18467629, 0.1932117, 0.21103024, 0.18505585, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4911202, 0.052502617, 2.7376428, -0.47767758, -0.3728814, 2.7871163, -0.12823197, -0.0010282934, -0.00034216748, 0.00015720575, 0.0017269863, 0.0031211488, 0.005502564, 0.017045334, 0.008193363], "split_indices": [1, 1, 43, 1, 8, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5164.0, 4718.0, 446.0, 3933.0, 785.0, 403.0, 43.0, 2055.0, 1878.0, 376.0, 409.0, 248.0, 155.0, 8.0, 35.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00020293794, -0.0077044447, 0.092823476, -0.013554353, 0.019007077, 0.08431211, 0.013627618, -0.0011000911, -0.00043266205, 0.0016799996, 0.00016931286, 0.0027144034, 0.0052996054], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.7988687, 0.74702406, 0.6011536, 0.16214675, 0.19569805, 0.2487557, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5841343, 0.033899806, 3.1301103, -0.53782666, -0.12725195, 1.2106531, 0.013627618, -0.0011000911, -0.00043266205, 0.0016799996, 0.00016931286, 0.0027144034, 0.0052996054], "split_indices": [1, 1, 45, 1, 60, 14, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5185.0, 4778.0, 407.0, 3920.0, 858.0, 390.0, 17.0, 1438.0, 2482.0, 443.0, 415.0, 165.0, 225.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.00027274305, -0.010102749, 0.062483717, -0.0008088519, 9.020356e-05, 0.044659637, 0.12568252, 0.0033100601, 0.0014846975, 0.0122797005, 0.0052687577], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [3.3777275, 0.3244088, 0.8369796, 0.0, 0.0, 0.18662906, 0.37347484, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [0.6415917, -0.32798696, 3.134369, -0.0008088519, 9.020356e-05, -0.12677158, 1.7904178, 0.0033100601, 0.0014846975, 0.0122797005, 0.0052687577], "split_indices": [1, 1, 1, 0, 0, 60, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5231.0, 4484.0, 747.0, 2969.0, 1515.0, 584.0, 163.0, 238.0, 346.0, 22.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [2.2760565e-05, -0.0072032125, 0.08383344, -0.012899055, 0.013890867, 0.07005889, 0.17969187, -0.0010488451, -0.0003792685, 0.0015207045, 0.00023153695, 0.003067113, 0.005726908, 0.00748028, 0.017371666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.1358662, 0.5729274, 0.53009844, 0.16097432, 0.15500844, 0.13293707, 0.20540428, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5841343, -0.07771708, 4.0657496, -0.5333259, -0.1281672, 0.9407251, 2.6287339, -0.0010488451, -0.0003792685, 0.0015207045, 0.00023153695, 0.003067113, 0.005726908, 0.00748028, 0.017371666], "split_indices": [1, 1, 1, 67, 60, 8, 48, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5176.0, 4766.0, 410.0, 3753.0, 1013.0, 360.0, 50.0, 1488.0, 2265.0, 363.0, 650.0, 303.0, 57.0, 44.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001593619, -0.0068682223, 0.07639248, -0.01104681, 0.018176692, 0.066594146, 0.008507794, -0.00082136627, -0.00017045827, 9.6741445e-05, 0.0016394496, 0.0022506795, 0.004206942], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.6706042, 0.5005437, 0.3736143, 0.1683492, 0.16227095, 0.14046848, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5841343, 0.15791857, 2.9115899, -0.08259691, -0.36295992, 2.514275, 0.008507794, -0.00082136627, -0.00017045827, 9.6741445e-05, 0.0016394496, 0.0022506795, 0.004206942], "split_indices": [1, 1, 45, 67, 8, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5198.0, 4780.0, 418.0, 4097.0, 683.0, 380.0, 38.0, 2403.0, 1694.0, 324.0, 359.0, 172.0, 208.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.000118584256, -0.0076944926, 0.06450649, -0.013221287, 0.005311305, 0.051072616, 0.12731649, -0.0011100535, -0.00048056137, 0.00064761005, -0.00026684013, 0.005138023, 0.0020487902, 0.005554088, 0.014803598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.6033685, 0.3317982, 0.46601534, 0.10478711, 0.11210331, 0.23742092, 0.23560226, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9640405, -0.25134334, 3.5808363, -0.77948827, -0.12715171, 0.80872566, 2.0512626, -0.0011100535, -0.00048056137, 0.00064761005, -0.00026684013, 0.005138023, 0.0020487902, 0.005554088, 0.014803598], "split_indices": [1, 1, 1, 67, 58, 59, 52, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5173.0, 4614.0, 559.0, 3238.0, 1376.0, 462.0, 97.0, 927.0, 2311.0, 801.0, 575.0, 74.0, 388.0, 90.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.00032323084, -0.006028613, 0.073376246, -0.0102316635, 0.017490266, 0.06797451, 0.0143755, -0.0008870565, -0.00026359974, 0.0019981896, 0.00036768356, 0.0019216047, 0.0041575227], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.4305222, 0.47654206, 0.46511197, 0.15219915, 0.16623157, 0.18062115, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5841343, 0.12815407, 3.2848954, -0.5258669, -0.12813525, 1.0686778, 0.0143755, -0.0008870565, -0.00026359974, 0.0019981896, 0.00036768356, 0.0019216047, 0.0041575227], "split_indices": [1, 1, 45, 67, 60, 14, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5236.0, 4818.0, 418.0, 4088.0, 730.0, 409.0, 9.0, 1625.0, 2463.0, 226.0, 504.0, 140.0, 269.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.00015498722, -0.005507292, 0.07284036, -0.009727962, 0.017410584, 0.06690662, 0.014284906, -0.001060428, -0.0003274467, 6.3492145e-05, 0.0015946863, 0.0020641393, 0.0042055785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.163998, 0.47201243, 0.46084118, 0.15022936, 0.17738989, 0.16008544, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.8321718, 0.15791857, 3.2848954, -0.5768926, -0.36295992, 1.2737608, 0.014284906, -0.001060428, -0.0003274467, 6.3492145e-05, 0.0015946863, 0.0020641393, 0.0042055785], "split_indices": [1, 1, 45, 1, 8, 14, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5256.0, 4877.0, 379.0, 4119.0, 758.0, 370.0, 9.0, 892.0, 3227.0, 359.0, 399.0, 150.0, 220.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [2.5408368e-05, -0.006709303, 0.03777088, -0.000600448, 0.003072676, 0.014065123, 0.07536984, -0.0007445969, 0.0005446823, -0.00011222794, 0.0014444618, 0.007904085, 0.0031409003], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3078831, 0.22637525, 0.69327176, 0.0, 0.21583077, 0.11587581, 0.30256367, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [0.74737585, 0.20368527, 0.576791, -0.000600448, -0.62402284, 0.06727754, -0.12820742, -0.0007445969, 0.0005446823, -0.00011222794, 0.0014444618, 0.007904085, 0.0031409003], "split_indices": [68, 67, 48, 0, 68, 14, 58, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5143.0, 4365.0, 778.0, 2831.0, 1534.0, 478.0, 300.0, 465.0, 1069.0, 228.0, 250.0, 38.0, 262.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.00047657808, -0.0048465896, 0.069126785, -0.000536473, 0.009335794, 0.060828935, 0.010917186, 0.0010177518, 5.900393e-05, 0.006157974, 0.0026308277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [1.8688122, 0.39607376, 0.4423685, 0.0, 0.12507473, 0.17267859, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [1.8321718, -0.19863537, 3.1301103, -0.000536473, -0.12798801, -0.12825282, 0.010917186, 0.0010177518, 5.900393e-05, 0.006157974, 0.0026308277], "split_indices": [1, 1, 45, 0, 58, 58, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5112.0, 4745.0, 367.0, 3354.0, 1391.0, 349.0, 18.0, 591.0, 800.0, 39.0, 310.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [2.5862573e-05, -0.0044060876, 0.06845737, -0.00766528, 0.01891928, 0.060819857, 0.010273638, -0.0008124327, -0.0001991007, 0.00014158398, 0.0016694494, 0.0070554167, 0.002475628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5506947, 0.36520198, 0.3129512, 0.13316023, 0.13701455, 0.2617358, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0442438, 0.33154485, 3.1301103, -0.5489884, -0.36295992, -0.12825282, 0.010273638, -0.0008124327, -0.0001991007, 0.00014158398, 0.0016694494, 0.0070554167, 0.002475628], "split_indices": [1, 1, 45, 1, 8, 58, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5111.0, 4801.0, 310.0, 4213.0, 588.0, 295.0, 15.0, 1264.0, 2949.0, 279.0, 309.0, 35.0, 260.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.000713433, -0.0039803647, 0.070482045, -0.0073693395, 0.019105282, 0.15440919, 0.0571237, -0.00060787913, -4.477823e-05, 0.0004213919, 0.001996092, 0.0031114079, 0.011283244, 0.0025212402, 0.009747612], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.7247889, 0.3862388, 0.36339402, 0.13340451, 0.14013669, 0.28153634, 0.2560796, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.1422188, 0.3439467, -0.12823197, -0.43241075, -0.082677566, 1.6097668, 3.1301103, -0.00060787913, -4.477823e-05, 0.0004213919, 0.001996092, 0.0031114079, 0.011283244, 0.0025212402, 0.009747612], "split_indices": [1, 1, 58, 1, 8, 14, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5265.0, 4934.0, 331.0, 4303.0, 631.0, 44.0, 287.0, 2473.0, 1830.0, 418.0, 213.0, 20.0, 24.0, 275.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-9.578861e-05, -0.005669102, 0.03273649, -0.00051441335, 0.0036201486, 0.016275685, 0.08099951, -0.000669341, 0.0005055016, -0.00023731792, 0.0013951593, 0.0086796, 0.0032678552], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9482287, 0.19012223, 0.59610677, 0.0, 0.16260475, 0.13750425, 0.26529312, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7870908, 0.23859966, 1.5777057, -0.00051441335, -0.62828684, 0.039919488, -0.12823197, -0.000669341, 0.0005055016, -0.00023731792, 0.0013951593, 0.0086796, 0.0032678552], "split_indices": [68, 67, 45, 0, 68, 14, 58, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5180.0, 4429.0, 751.0, 2958.0, 1471.0, 561.0, 190.0, 406.0, 1065.0, 200.0, 361.0, 26.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.00046613574, -0.006043596, 0.041379876, -0.00052668474, 8.067846e-05, 0.031170337, 0.09522364, 0.004052024, 0.0012321813, 0.003898351, 0.010584176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.3814763, 0.15387385, 0.38843262, 0.0, 0.0, 0.19329572, 0.21059215, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [0.66949594, -0.35551912, 3.5808363, -0.00052668474, 8.067846e-05, -0.12848987, 1.9847567, 0.004052024, 0.0012321813, 0.003898351, 0.010584176], "split_indices": [1, 1, 1, 0, 0, 58, 52, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5185.0, 4474.0, 711.0, 2820.0, 1654.0, 599.0, 112.0, 68.0, 531.0, 99.0, 13.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00051228097, -0.0053487252, 0.039353553, -0.00049602846, 9.97787e-05, 0.029009119, 0.09306897, 0.002935487, 0.00086585333, 0.0018009149, 0.006527851], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.1876273, 0.15222429, 0.37736857, 0.0, 0.0, 0.19865844, 0.23055667, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [0.75320864, -0.36047986, 3.5808363, -0.00049602846, 9.97787e-05, -0.1277232, 1.9020847, 0.002935487, 0.00086585333, 0.0018009149, 0.006527851], "split_indices": [1, 1, 1, 0, 0, 58, 46, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5215.0, 4532.0, 683.0, 2793.0, 1739.0, 574.0, 109.0, 161.0, 413.0, 44.0, 65.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.00011211837, -0.0035528338, 0.056748748, -0.00039024602, 0.009213456, 0.041162238, 0.11607078, -5.309952e-05, 0.00097624626, 0.0022949856, -0.0072466256, -0.00057195534, 0.007090077], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9977805, 0.26112065, 0.2642877, 0.0, 0.12737855, 0.21118796, 0.19847757, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.1422188, -0.12112365, 4.0657496, -0.00039024602, -0.35675898, 2.7294896, 1.3453997, -5.309952e-05, 0.00097624626, 0.0022949856, -0.0072466256, -0.00057195534, 0.007090077], "split_indices": [1, 1, 1, 0, 8, 45, 50, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5098.0, 4808.0, 290.0, 3607.0, 1201.0, 231.0, 59.0, 602.0, 599.0, 226.0, 5.0, 10.0, 49.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [9.308856e-05, -0.0040502963, 0.04561202, -0.0004186502, 0.0045537236, 0.03741691, 0.12639359, 0.00061450404, -0.00035691386, 0.00202554, -0.005769013, 0.004567815, 0.010943304], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.96752673, 0.174918, 0.27861273, 0.0, 0.14237109, 0.18904191, 0.10636461, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3981062, -0.27782136, 2.7376428, -0.0004186502, -0.12706298, 3.5312235, 0.27995324, 0.00061450404, -0.00035691386, 0.00202554, -0.005769013, 0.004567815, 0.010943304], "split_indices": [1, 1, 43, 0, 60, 9, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5128.0, 4701.0, 427.0, 3129.0, 1572.0, 389.0, 38.0, 946.0, 626.0, 382.0, 7.0, 29.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.00010174835, -0.0027506007, 0.056580126, -0.005610315, 0.013866933, 0.101917624, 0.038130794, -0.00074090343, -0.00013767766, 0.00013280977, 0.0014355457, 0.0095655415, 0.004133065, 0.0021546863, -0.0056488295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.84107697, 0.23625918, 0.2060619, 0.11151597, 0.121404484, 0.108125746, 0.13985705, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.4832704, 0.28193733, -0.12715171, -0.84952337, -0.24514212, -0.10168353, 3.878475, -0.00074090343, -0.00013767766, 0.00013280977, 0.0014355457, 0.0095655415, 0.004133065, 0.0021546863, -0.0056488295], "split_indices": [1, 1, 58, 67, 8, 55, 9, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5219.0, 4969.0, 250.0, 4240.0, 729.0, 71.0, 179.0, 1003.0, 3237.0, 416.0, 313.0, 11.0, 60.0, 174.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [4.5946297e-05, -0.0051216483, 0.03007269, -0.0006804624, -0.002109184, 0.022747178, 0.08012234, -0.00067473867, 8.465654e-05, 0.0021805435, 0.00053606177, 0.0030860163, 0.009643219], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7978627, 0.11214071, 0.27499014, 0.0, 0.1402443, 0.16513011, 0.1872657, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [0.5924803, -0.7993895, 3.5808363, -0.0006804624, -0.5551893, -0.12715171, 1.9847567, -0.00067473867, 8.465654e-05, 0.0021805435, 0.00053606177, 0.0030860163, 0.009643219], "split_indices": [1, 67, 1, 0, 1, 58, 52, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5140.0, 4386.0, 754.0, 1148.0, 3238.0, 659.0, 95.0, 810.0, 2428.0, 240.0, 419.0, 83.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-7.983396e-06, -0.0032327042, 0.037886437, -0.00039040155, 0.0001636621, 0.001732038, 0.008212476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.63885623, 0.14344215, 0.16246516, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [1.4911202, -0.36047986, 3.2848954, -0.00039040155, 0.0001636621, 0.001732038, 0.008212476], "split_indices": [1, 1, 45, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5226.0, 4817.0, 409.0, 2828.0, 1989.0, 400.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.00021534853, -0.0024075871, 0.049939327, -0.0049748328, 0.013613901, 0.13726236, 0.038382486, -0.0004580103, 4.404422e-05, 8.201383e-05, 0.0015751271, 0.005069091, 0.013194377, 0.001674963, 0.007147591], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6767537, 0.2028029, 0.25711787, 0.104144104, 0.14581299, 0.10954797, 0.11316803, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.514275, 0.3439467, -0.12838349, -0.08259691, -0.24514212, 1.4911203, 3.2848954, -0.0004580103, 4.404422e-05, 8.201383e-05, 0.0015751271, 0.005069091, 0.013194377, 0.001674963, 0.007147591], "split_indices": [1, 1, 58, 67, 8, 8, 45, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5187.0, 4928.0, 259.0, 4248.0, 680.0, 29.0, 230.0, 2477.0, 1771.0, 408.0, 272.0, 24.0, 5.0, 221.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.000111052475, -0.0035493115, 0.021411987, -0.00047982676, 0.0007705083, 0.00028027812, 0.046913315, -0.0005931496, 0.00024188201, 0.006521085, 0.0016849177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.40036973, 0.11447801, 0.30326, 0.0, 0.13144146, 0.0, 0.3134302, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [0.7870908, -0.36859342, 1.0686778, -0.00047982676, -0.6393729, 0.00028027812, -0.12828101, -0.0005931496, 0.00024188201, 0.006521085, 0.0016849177], "split_indices": [68, 67, 14, 0, 68, 0, 60, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5133.0, 4381.0, 752.0, 1825.0, 2556.0, 465.0, 287.0, 622.0, 1934.0, 38.0, 249.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00032012907, -0.0034371654, 0.031106643, -0.0003216775, 0.00023994924, 0.09637059, 0.025513733, 0.008759937, 0.0017529425, 0.0013877, -0.0045645633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.5976788, 0.11367617, 0.20267993, 0.0, 0.0, 0.20651373, 0.13834268, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [1.0880593, -0.0433969, -0.12850955, -0.0003216775, 0.00023994924, -0.10129125, 3.5312235, 0.008759937, 0.0017529425, 0.0013877, -0.0045645633], "split_indices": [1, 44, 60, 0, 0, 55, 9, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5165.0, 4604.0, 561.0, 3376.0, 1228.0, 43.0, 518.0, 18.0, 25.0, 509.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00037019706, -0.00018082288, 0.025095778, 0.020767106, 0.0048243725, 0.0024237798, 0.00062627834], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5125684, 0.0, 0.22111669, 0.15513474, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [0.6415917, -0.00018082288, 2.8649352, -0.12792496, 0.0048243725, 0.0024237798, 0.00062627834], "split_indices": [1, 0, 43, 58, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5198.0, 4477.0, 721.0, 681.0, 40.0, 155.0, 526.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.00037555475, -0.002292625, 0.039518557, -0.00026196113, 0.0057223444, 0.10834507, 0.030662052, 0.0008681146, -0.00012657235, 0.0040180646, 0.011976719, 0.0012911529, 0.007070998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5368107, 0.113674454, 0.19661433, 0.0, 0.12436331, 0.11658409, 0.15284747, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0442438, -0.15212834, -0.12835729, -0.00026196113, -0.12795964, 1.4911203, 3.2848954, 0.0008681146, -0.00012657235, 0.0040180646, 0.011976719, 0.0012911529, 0.007070998], "split_indices": [1, 1, 58, 0, 58, 8, 45, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5138.0, 4811.0, 327.0, 3518.0, 1293.0, 36.0, 291.0, 536.0, 757.0, 31.0, 5.0, 280.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.00024152051, -0.001967396, 0.041744523, -0.00023083806, 0.0076446575, 0.08177101, 0.026356885, 0.0009883529, -0.00015866238, 0.007988321, 0.003093884, 0.0009297885, 0.0057831756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.47350037, 0.12491977, 0.15859812, 0.0, 0.13907003, 0.10182831, 0.12954175, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.4832704, 0.0090960525, -0.12715171, -0.00023083806, -0.10094524, -0.10168353, 1.9182507, 0.0009883529, -0.00015866238, 0.007988321, 0.003093884, 0.0009297885, 0.0057831756], "split_indices": [1, 1, 58, 0, 55, 55, 52, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5163.0, 4903.0, 260.0, 3844.0, 1059.0, 71.0, 189.0, 499.0, 560.0, 13.0, 58.0, 175.0, 14.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.00035284698, -0.0015285346, 0.046464514, -0.00020387028, 0.008920294, 0.006674811, 0.030867573, 0.0012754393, -1.015291e-05, 0.0010498683, 0.0076427134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.2853568, 0.13471803, 0.1682727, 0.0, 0.1501371, 0.0, 0.12803015, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.2072918, 0.15791857, 2.1494112, -0.00020387028, -0.10122555, 0.006674811, 3.2848954, 0.0012754393, -1.015291e-05, 0.0010498683, 0.0076427134], "split_indices": [1, 1, 59, 0, 55, 0, 45, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5182.0, 5056.0, 126.0, 4065.0, 991.0, 18.0, 108.0, 351.0, 640.0, 101.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-3.554931e-05, -0.0019030428, 0.02464794, -0.00034358812, 0.0030481075, 0.0010396744, 0.004922023, -0.00048193426, 0.00037288477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.24117643, 0.11967959, 0.10262327, 0.0, 0.13640626, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [1.4167986, -0.21306567, 3.1301103, -0.00034358812, -0.6338924, 0.0010396744, 0.004922023, -0.00048193426, 0.00037288477], "split_indices": [59, 67, 45, 0, 68, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5230.0, 4863.0, 367.0, 2427.0, 2436.0, 350.0, 17.0, 628.0, 1808.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0001767471, -9.191249e-05, 0.031945083, 0.003175923, 0.0010199499], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 97, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.33055836, 0.0, 0.11098534, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.2042282, -9.191249e-05, -0.12715171, 0.003175923, 0.0010199499], "split_indices": [1, 0, 58, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [5162.0, 4855.0, 307.0, 81.0, 226.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.00035341724, -0.0016084603, 0.036231674, -0.00028832967, 0.00016807327, 0.031078953, 0.007156855, 0.0041375817, 0.0011663857], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.36248994, 0.10092957, 0.14262944, 0.0, 0.0, 0.100604564, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.4832704, -0.38379538, 3.2848954, -0.00028832967, 0.00016807327, -0.12820742, 0.007156855, 0.0041375817, 0.0011663857], "split_indices": [1, 1, 45, 0, 0, 58, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5148.0, 4882.0, 266.0, 2658.0, 2224.0, 255.0, 11.0, 32.0, 223.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [3.9000233e-06, -0.00016077129, 0.0154538015, 0.02998282, 0.0003171024, 0.00067746436, 0.0034661342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.2598824, 0.0, 0.119088605, 0.22339377, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [0.35634857, -0.00016077129, -0.12715171, 0.018397707, 0.0003171024, 0.00067746436, 0.0034661342], "split_indices": [1, 0, 58, 8, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [5223.0, 4323.0, 900.0, 346.0, 554.0, 245.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "69", "num_nodes": "7", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-1.0208753E-9", "boost_from_average": "1", "num_class": "0", "num_feature": "69", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 0]}