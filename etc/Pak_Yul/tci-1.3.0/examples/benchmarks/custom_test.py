#!/usr/bin/env python3
"""
Custom test for TCI.

This script runs a test with a custom environment and agent.
"""

import os
import sys
import time
import numpy as np
import torch
import matplotlib.pyplot as plt
import scipy.stats as stats

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Create a custom environment
class CustomEnv:
    def __init__(self, seed=42):
        np.random.seed(seed)
        self.state_dim = 4
        self.action_dim = 2
        self.state = None
    
    def reset(self):
        self.state = np.random.randn(self.state_dim)
        return self.state
    
    def step(self, action):
        # Simple dynamics
        self.state = self.state + 0.1 * np.random.randn(self.state_dim)
        
        # Reward is negative distance to origin
        reward = -np.linalg.norm(self.state)
        
        # Done if state is far from origin
        done = np.linalg.norm(self.state) > 5.0
        
        return self.state, reward, done

# Create a custom agent
class CustomAgent:
    def __init__(self, state_dim, action_dim, hidden_dim=32, device='cpu'):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dim = hidden_dim
        self.device = device
        self.buffer = []
        
        # Create simple neural network
        self.model = torch.nn.Sequential(
            torch.nn.Linear(state_dim, hidden_dim),
            torch.nn.ReLU(),
            torch.nn.Linear(hidden_dim, action_dim)
        ).to(device)
        
        # Create optimizer
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=0.01)
    
    def act(self, state):
        # Convert state to tensor
        state_tensor = torch.FloatTensor(state).to(self.device)
        
        # Forward pass
        with torch.no_grad():
            q_values = self.model(state_tensor)
        
        # Epsilon-greedy action selection
        if np.random.rand() < 0.1:
            return np.random.randint(self.action_dim)
        else:
            return q_values.argmax().item()
    
    def store(self, state, action, reward, next_state):
        self.buffer.append((state, action, reward, next_state))
        
        # Keep buffer size limited
        if len(self.buffer) > 1000:
            self.buffer.pop(0)
    
    def train(self, batch_size=32):
        # Check if buffer has enough samples
        if len(self.buffer) < batch_size:
            return {"loss": 0.0}
        
        # Sample batch
        indices = np.random.choice(len(self.buffer), batch_size, replace=False)
        states, actions, rewards, next_states = zip(*[self.buffer[i] for i in indices])
        
        # Convert to tensors
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        
        # Forward pass
        q_values = self.model(states)
        q_values_taken = q_values.gather(1, actions.unsqueeze(1)).squeeze(1)
        
        # Target values
        with torch.no_grad():
            next_q_values = self.model(next_states)
            next_q_values = next_q_values.max(1)[0]
            target_values = rewards + 0.99 * next_q_values
        
        # Compute loss
        loss = torch.nn.functional.mse_loss(q_values_taken, target_values)
        
        # Backward pass
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        return {"loss": loss.item()}

def train_and_evaluate(agent, env, episodes=20, max_steps=100, eval_interval=5):
    """
    Train and evaluate an agent.
    
    Parameters
    ----------
    agent : CustomAgent
        Agent to train and evaluate
    env : CustomEnv
        Environment to train and evaluate in
    episodes : int, optional
        Number of episodes to train, by default 20
    max_steps : int, optional
        Maximum steps per episode, by default 100
    eval_interval : int, optional
        Interval for evaluation, by default 5
        
    Returns
    -------
    dict
        Dictionary with metrics
    """
    # Initialize metrics
    eval_rewards = []
    train_rewards = []
    losses = []
    
    # Training loop
    start_time = time.time()
    for episode in range(episodes):
        state = env.reset()
        episode_reward = 0
        
        for step in range(max_steps):
            # Select action
            action = agent.act(state)
            
            # Take action
            next_state, reward, done = env.step(action)
            
            # Store experience
            agent.store(state, action, reward, next_state)
            
            # Update state and reward
            state = next_state
            episode_reward += reward
            
            if done:
                break
        
        # Train agent
        loss_dict = agent.train(batch_size=min(32, len(agent.buffer)))
        losses.append(loss_dict["loss"])
        
        # Store training reward
        train_rewards.append(episode_reward)
        
        # Evaluate agent
        if episode % eval_interval == 0:
            eval_reward = evaluate_agent(agent, env, n_episodes=3)
            eval_rewards.append(eval_reward)
            
            # Print progress
            print(f"Episode {episode}: Train reward = {episode_reward:.2f}, Eval reward = {eval_reward:.2f}")
            
            # Print loss if available
            if losses:
                print(f"Loss: {losses[-1]:.4f}")
    
    # Calculate training time
    training_time = time.time() - start_time
    
    # Return metrics
    return {
        "eval_rewards": eval_rewards,
        "train_rewards": train_rewards,
        "losses": losses,
        "training_time": training_time
    }

def evaluate_agent(agent, env, n_episodes=3):
    """
    Evaluate an agent.
    
    Parameters
    ----------
    agent : CustomAgent
        Agent to evaluate
    env : CustomEnv
        Environment to evaluate in
    n_episodes : int, optional
        Number of episodes to evaluate, by default 3
        
    Returns
    -------
    float
        Mean reward
    """
    total_reward = 0
    
    for _ in range(n_episodes):
        state = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            # Select action (without exploration)
            action = agent.act(state)
            
            # Take action
            next_state, reward, done = env.step(action)
            
            # Update state and reward
            state = next_state
            episode_reward += reward
        
        total_reward += episode_reward
    
    return total_reward / n_episodes

def main():
    """
    Run a custom test.
    """
    # Set device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    # Create output directory
    output_dir = "custom_test_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create environment
    env = CustomEnv(seed=42)
    print(f"Created environment with state_dim={env.state_dim}, action_dim={env.action_dim}")
    
    # Create baseline agent
    baseline_agent = CustomAgent(
        state_dim=env.state_dim,
        action_dim=env.action_dim,
        hidden_dim=32,
        device=device
    )
    print("Created baseline agent")
    
    # Create agent with larger hidden dimension
    large_hidden_agent = CustomAgent(
        state_dim=env.state_dim,
        action_dim=env.action_dim,
        hidden_dim=64,
        device=device
    )
    print("Created large hidden agent")
    
    # Train and evaluate baseline agent
    print("Training baseline agent...")
    baseline_metrics = train_and_evaluate(baseline_agent, env)
    
    # Train and evaluate large hidden agent
    print("Training large hidden agent...")
    large_hidden_metrics = train_and_evaluate(large_hidden_agent, env)
    
    # Plot learning curves
    plt.figure(figsize=(12, 6))
    plt.plot(baseline_metrics["eval_rewards"], label="Baseline")
    plt.plot(large_hidden_metrics["eval_rewards"], label="Large Hidden")
    plt.title("Learning Curves")
    plt.xlabel("Evaluation Step")
    plt.ylabel("Reward")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, "learning_curves.png"))
    plt.close()
    
    # Plot losses
    plt.figure(figsize=(12, 6))
    plt.plot(baseline_metrics["losses"], label="Baseline")
    plt.plot(large_hidden_metrics["losses"], label="Large Hidden")
    plt.title("Loss Values")
    plt.xlabel("Training Step")
    plt.ylabel("Loss")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, "losses.png"))
    plt.close()
    
    # Perform statistical test
    baseline_rewards = baseline_metrics["eval_rewards"]
    large_hidden_rewards = large_hidden_metrics["eval_rewards"]
    
    # Calculate mean and std
    baseline_mean = np.mean(baseline_rewards)
    baseline_std = np.std(baseline_rewards)
    large_hidden_mean = np.mean(large_hidden_rewards)
    large_hidden_std = np.std(large_hidden_rewards)
    
    # Calculate improvement
    improvement = (large_hidden_mean - baseline_mean) / baseline_mean * 100
    
    # Perform t-test
    t_stat, p_value = stats.ttest_ind(
        baseline_rewards,
        large_hidden_rewards,
        equal_var=False  # Welch's t-test
    )
    
    # Calculate effect size (Cohen's d)
    n1 = len(baseline_rewards)
    n2 = len(large_hidden_rewards)
    pooled_std = np.sqrt(((n1 - 1) * baseline_std**2 + (n2 - 1) * large_hidden_std**2) / (n1 + n2 - 2))
    cohens_d = (large_hidden_mean - baseline_mean) / pooled_std if pooled_std > 0 else 0
    
    # Interpret effect size
    if abs(cohens_d) < 0.2:
        effect_interpretation = "negligible"
    elif abs(cohens_d) < 0.5:
        effect_interpretation = "small"
    elif abs(cohens_d) < 0.8:
        effect_interpretation = "medium"
    else:
        effect_interpretation = "large"
    
    # Print results
    print("\nResults:")
    print(f"Baseline mean reward: {baseline_mean:.2f} ± {baseline_std:.2f}")
    print(f"Large hidden mean reward: {large_hidden_mean:.2f} ± {large_hidden_std:.2f}")
    print(f"Improvement: {improvement:.2f}%")
    print(f"Statistical significance: p-value = {p_value:.4f} ({'significant' if p_value < 0.05 else 'not significant'})")
    print(f"Effect size (Cohen's d): {cohens_d:.2f} ({effect_interpretation})")
    print(f"Results saved to {output_dir}/")
    
    # Generate HTML report
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Custom Test Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #2c3e50; }}
            h2 {{ color: #3498db; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .result {{ font-weight: bold; font-size: 18px; margin: 10px 0; }}
            .significant {{ color: green; }}
            .not-significant {{ color: orange; }}
            .negative {{ color: red; }}
            .images {{ display: flex; flex-wrap: wrap; justify-content: space-between; }}
            .image-container {{ width: 48%; margin-bottom: 20px; }}
            img {{ max-width: 100%; border: 1px solid #ddd; border-radius: 5px; }}
            table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Custom Test Report</h1>
            
            <div class="summary">
                <h2>Summary</h2>
                <p>Comparison between Baseline Agent and Large Hidden Agent</p>
                <p class="result">Improvement: <span class="{'significant' if p_value < 0.05 and improvement > 0 else 'not-significant' if p_value >= 0.05 else 'negative'}">{improvement:.2f}%</span></p>
                <p class="result">Statistical Significance: <span class="{'significant' if p_value < 0.05 else 'not-significant'}">p-value = {p_value:.4f} ({'significant' if p_value < 0.05 else 'not significant'})</span></p>
                <p class="result">Effect Size: <span class="{'significant' if abs(cohens_d) >= 0.8 else 'not-significant' if abs(cohens_d) >= 0.2 else 'negative'}">{cohens_d:.2f} ({effect_interpretation})</span></p>
            </div>
            
            <h2>Performance Metrics</h2>
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Baseline Agent</th>
                    <th>Large Hidden Agent</th>
                </tr>
                <tr>
                    <td>Mean Reward</td>
                    <td>{baseline_mean:.2f}</td>
                    <td>{large_hidden_mean:.2f}</td>
                </tr>
                <tr>
                    <td>Standard Deviation</td>
                    <td>{baseline_std:.2f}</td>
                    <td>{large_hidden_std:.2f}</td>
                </tr>
                <tr>
                    <td>Training Time (seconds)</td>
                    <td>{baseline_metrics["training_time"]:.2f}</td>
                    <td>{large_hidden_metrics["training_time"]:.2f}</td>
                </tr>
            </table>
            
            <h2>Visualizations</h2>
            <div class="images">
                <div class="image-container">
                    <h3>Learning Curves</h3>
                    <img src="learning_curves.png" alt="Learning Curves">
                </div>
                <div class="image-container">
                    <h3>Loss Values</h3>
                    <img src="losses.png" alt="Loss Values">
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    
    with open(os.path.join(output_dir, "report.html"), "w") as f:
        f.write(html_report)
    
    print(f"HTML report saved to {os.path.join(output_dir, 'report.html')}")

if __name__ == "__main__":
    main()
