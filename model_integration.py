"""
Model Integration Module

This module integrates different forecasting models and provides a unified interface
for training models and generating forecasts.

Available models:
1. TCI (Temporal Causality Infusion)
2. TCI Premium (Enhanced TCI with additional features)
3. TCI-fix (Improved TCI with pattern recognition)
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import json
import pickle
import traceback

# Import API data fetcher if available
try:
    from api_data_fetcher import APIDataFetcher
    API_FETCHER_AVAILABLE = True
except ImportError:
    API_FETCHER_AVAILABLE = False

# Import our custom models
from tci_model import TCIOrderPredictor
from tci_premium import TCIPremiumPredictor
from tci_fix import TCIFixPredictor
from tci_fix_integration import train_tci_fix_model, predict_with_tci_fix, visualize_tci_fix_results

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelIntegration:
    """
    Integrates different forecasting models and provides a unified interface.

    This class is responsible for:
    1. Training different forecasting models
    2. Generating forecasts
    3. Evaluating model performance
    4. Visualizing results
    """

    def __init__(self, data_manager=None):
        """
        Initialize the model integration.

        Parameters:
        -----------
        data_manager : object, optional
            Data manager object that provides access to customer order data
        """
        self.data_manager = data_manager

        # Initialize TCI model
        try:
            self.tci_predictor = TCIOrderPredictor()
            logger.info("Initialized TCI predictor")
        except Exception as e:
            logger.error(f"Error initializing TCI predictor: {str(e)}")

        # Initialize TCI Premium model
        try:
            self.tci_premium_predictor = TCIPremiumPredictor()
            logger.info("Initialized TCI Premium predictor")
        except Exception as e:
            logger.error(f"Error initializing TCI Premium predictor: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

        # Pattern Copy Forecaster removed

        # Initialize TCI-fix predictor
        try:
            from tci_fix_integration import TCIFixIntegration
            self.tci_fix_predictor = TCIFixIntegration(data_manager=self.data_manager)
            logger.info("Initialized TCI-fix predictor")
        except Exception as e:
            logger.error(f"Error initializing TCI-fix predictor: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

        # Initialize Enhanced TCI-fix integration
        try:
            from production_enhanced_tci_fix import ProductionEnhancedTCIFix
            self.enhanced_tci_fix = ProductionEnhancedTCIFix()
            logger.info("Initialized Enhanced TCI-fix predictor")
            ENHANCED_TCI_FIX_AVAILABLE = True
        except ImportError:
            logger.warning("Enhanced TCI-fix not available")
            self.enhanced_tci_fix = None
            ENHANCED_TCI_FIX_AVAILABLE = False
        except Exception as e:
            logger.error(f"Error initializing Enhanced TCI-fix predictor: {str(e)}")
            self.enhanced_tci_fix = None
            ENHANCED_TCI_FIX_AVAILABLE = False

        # Initialize API data fetcher if available
        if API_FETCHER_AVAILABLE:
            try:
                self.api_fetcher = APIDataFetcher()
                logger.info("Initialized API data fetcher")
            except Exception as e:
                logger.error(f"Error initializing API data fetcher: {str(e)}")
                self.api_fetcher = None
        else:
            self.api_fetcher = None
            logger.warning("API data fetcher not available")

    def get_available_models(self):
        """
        Get list of available forecasting models.

        Returns:
        --------
        list
            List of available model names
        """
        models = [
            "TCI",
            "TCI Premium",
            "TCI-fix"  # Our enhanced TCI model
        ]

        # Add Enhanced TCI-fix if available
        if hasattr(self, 'enhanced_tci_fix') and self.enhanced_tci_fix is not None:
            models.append("Enhanced TCI-fix")

        logger.info(f"Available models: {models}")
        return models

    def train_global_model(self, model_name="TCI", periods=12, **kwargs):
        """
        Train a global model using data from all customers and products.

        Parameters:
        -----------
        model_name : str, optional
            Name of the model to train
        periods : int, optional
            Number of periods to forecast
        **kwargs : dict
            Additional parameters for the model

        Returns:
        --------
        tuple
            (success, message)
        """
        logger.info(f"Training global model: {model_name}")

        # Check if data manager is available
        if not self.data_manager:
            return False, "Data manager not available"

        # Get all customers and products
        customers = self.data_manager.get_customers()

        # Collect data for all customers and products
        all_data = []

        for customer in customers:
            products = self.data_manager.get_products(customer)

            for product in products:
                # Get data for this customer and product
                data = self.data_manager.get_product_data(customer, product)

                if data is not None and not data.empty:
                    # Convert to standard format
                    data_formatted = data.rename(columns={'date': 'ds', 'quantity': 'y'})
                    data_formatted['ds'] = pd.to_datetime(data_formatted['ds'])
                    data_formatted = data_formatted.sort_values('ds')
                    data_formatted['customer'] = customer
                    data_formatted['product'] = product
                    all_data.append(data_formatted)
                    logger.info(f"Added data for {customer}/{product}: {len(data_formatted)} rows")

        if not all_data:
            return False, "No data available"

        # Combine all data
        combined_data = pd.concat(all_data, ignore_index=True)
        logger.info(f"Combined data: {len(combined_data)} rows")

        # Train the model
        if model_name == "TCI":
            # Train the TCI model
            success, message = self.tci_predictor.train_global_model(combined_data)
            return success, message
        elif model_name == "TCI Premium":
            # Train the TCI Premium model
            success, message = self.tci_premium_predictor.train_global_model(combined_data)
            return success, message
        else:
            return False, f"Global model training not supported for {model_name}"

    def train_model(self, model_name, customer, product, periods=12, cutoff_date=None, **kwargs):
        """
        Train a model for a specific customer and product.

        Parameters:
        -----------
        model_name : str
            Name of the model to train
        customer : str
            Customer name
        product : str
            Product name
        periods : int, optional
            Number of periods to forecast
        cutoff_date : str, optional
            If provided, only use data up to this date (format: 'YYYY-MM-DD')
        **kwargs : dict
            Additional parameters for the model

        Returns:
        --------
        tuple
            (success, message)
        """
        logger.info(f"Training model: {model_name} for {customer}/{product}")

        if model_name == "TCI":
            # Use the existing TCI model
            data = self._prepare_data_for_model(customer, product, cutoff_date=cutoff_date)

            if data is None or data.empty:
                return False, f"No data available for {customer}/{product}"

            success, message = self.tci_predictor.train_model(data, customer, product)

            if not success:
                logger.warning(f"Failed to train TCI model: {message}")

            return success, message

        elif model_name == "TCI Premium":
            # Use the TCI Premium model
            data = self._prepare_data_for_model(customer, product, cutoff_date=cutoff_date)

            if data is None or data.empty:
                return False, f"No data available for {customer}/{product}"

            # Get external factors if available
            external_factors = self._get_external_factors(customer, product)

            success, message = self.tci_premium_predictor.train_model(
                data, customer, product, external_factors=external_factors, **kwargs
            )

            if not success:
                logger.warning(f"Failed to train TCI Premium model: {message}")

            return success, message

        # Pattern Copy model removed

        elif model_name == "TCI-fix":
            # Use the TCI-fix model
            data = self._prepare_data_for_model(customer, product, cutoff_date=cutoff_date)

            if data is None or data.empty:
                return False, f"No data available for {customer}/{product}"

            # Get external factors if available
            external_factors = self._get_external_factors(customer, product)

            # Get industry data for Japan and Indonesia
            try:
                from tci_fix_integration import load_external_data
                industry_data = load_external_data(start_year=2016, end_year=2025)
                if industry_data:
                    logger.info(f"Loaded {len(industry_data)} industry data sources for {customer}/{product}")
                    # Merge industry data with external factors
                    if external_factors is None:
                        external_factors = {}
                    external_factors.update(industry_data)
            except Exception as e:
                logger.warning(f"Failed to load industry data: {str(e)}")

            # Check for incremental learning and hyperparameter tuning flags
            incremental = kwargs.pop('incremental', False)
            tune_hyperparams = kwargs.pop('tune_hyperparams', False)

            # Log what we're doing
            if incremental:
                logger.info(f"Using incremental learning for {customer}/{product}")
            if tune_hyperparams:
                logger.info(f"Tuning hyperparameters for {customer}/{product}")

            success, message = train_tci_fix_model(
                data, customer, product, external_factors=external_factors,
                incremental=incremental, tune_hyperparams=tune_hyperparams, **kwargs
            )

            if not success:
                logger.warning(f"Failed to train TCI-fix model: {message}")

            return success, message

        elif model_name == "Enhanced TCI-fix":
            # Use the Enhanced TCI-fix model
            if not hasattr(self, 'enhanced_tci_fix') or self.enhanced_tci_fix is None:
                return False, "Enhanced TCI-fix not available"

            success, message, model_info = self.enhanced_tci_fix.train_enhanced_model(customer, product)

            if not success:
                logger.warning(f"Failed to train Enhanced TCI-fix model: {message}")
            else:
                logger.info(f"Enhanced TCI-fix model trained successfully: {message}")
                if model_info:
                    logger.info(f"Model info: volatility={model_info.get('volatility_level', 'unknown')}, "
                              f"bias_correction={model_info.get('bias_correction_factor', 1.0):.3f}")

            return success, message

        else:
            logger.error(f"Available models: TCI, TCI Premium, TCI-fix, Enhanced TCI-fix")
            return False, f"Unknown model: {model_name}"

    def generate_forecast(self, model_name, customer, product, periods=12, start_year=None, optimize_hyperparams=False, **kwargs):
        """
        Generate forecasts for a specific customer and product.
        This is an alias for predict_future to maintain compatibility with the GUI.

        Parameters:
        -----------
        model_name : str
            Name of the model to use
        customer : str
            Customer name
        product : str
            Product name
        periods : int, optional
            Number of periods to forecast
        start_year : int, optional
            If provided, start forecasting from this year
        optimize_hyperparams : bool, optional
            Whether to optimize hyperparameters
        **kwargs : dict
            Additional parameters for the model

        Returns:
        --------
        DataFrame
            Forecast results
        """
        # Convert start_year to cutoff_date if provided
        cutoff_date = None
        if start_year is not None:
            cutoff_date = f"{start_year}-12-31"

        # Add optimize_hyperparams to kwargs if provided
        if optimize_hyperparams:
            kwargs['optimize_hyperparams'] = optimize_hyperparams

        return self.predict_future(model_name, customer, product, periods=periods, cutoff_date=cutoff_date, **kwargs)

    def predict_future(self, model_name, customer, product, periods=12, cutoff_date=None, **kwargs):
        """
        Generate forecasts for a specific customer and product.

        Parameters:
        -----------
        model_name : str
            Name of the model to use
        customer : str
            Customer name
        product : str
            Product name
        periods : int, optional
            Number of periods to forecast
        cutoff_date : str, optional
            If provided, only use data up to this date (format: 'YYYY-MM-DD')
        **kwargs : dict
            Additional parameters for the model

        Returns:
        --------
        DataFrame
            Forecast results
        """
        logger.info(f"Generating forecast with {model_name} for {customer}/{product}, periods={periods}")

        if model_name == "TCI":
            # Use the existing TCI model
            # Check if the model exists
            model_dir = f"models/tci/{customer.replace('/', '_')}_{product.replace('/', '_')}"
            model_path = f"{model_dir}/model.pkl"

            if not os.path.exists(model_path):
                logger.info(f"No pre-trained TCI model found. Training a new one.")

                # Train a temporary model with cutoff date
                data_with_cutoff = self._prepare_data_for_model(customer, product, cutoff_date=cutoff_date)

                if data_with_cutoff is None or data_with_cutoff.empty:
                    logger.warning(f"No data available for {customer}/{product}")
                    return None

                temp_success, temp_message = self.tci_predictor.train_model(data_with_cutoff, customer, product)

                if not temp_success:
                    logger.warning(f"Failed to train temporary TCI model with cutoff date: {temp_message}")
                    return None

            # Generate predictions
            forecast_df = self.tci_predictor.predict_future(customer, product, periods)

            if forecast_df is None:
                logger.warning(f"Failed to generate TCI forecast for {customer}, {product}")
                return None

            return forecast_df

        elif model_name == "TCI Premium":
            # Use the TCI Premium model
            # Check if the model exists
            model_dir = f"models/tci_premium/{customer.replace('/', '_')}_{product.replace('/', '_')}"
            model_path = f"{model_dir}/model.pkl"

            if not os.path.exists(model_path):
                logger.info(f"No pre-trained TCI Premium model found. Training a new one.")

                # Train a temporary model with cutoff date
                data_with_cutoff = self._prepare_data_for_model(customer, product, cutoff_date=cutoff_date)

                if data_with_cutoff is None or data_with_cutoff.empty:
                    logger.warning(f"No data available for {customer}/{product}")
                    return None

                # Get external factors if available
                external_factors = self._get_external_factors(customer, product)

                temp_success, temp_message = self.tci_premium_predictor.train_model(
                    data_with_cutoff, customer, product, external_factors=external_factors, **kwargs
                )

                if not temp_success:
                    logger.warning(f"Failed to train temporary TCI Premium model with cutoff date: {temp_message}")
                    return None

            # Generate predictions
            forecast_df = self.tci_premium_predictor.predict_future(customer, product, periods)

            if forecast_df is None:
                logger.warning(f"Failed to generate TCI Premium forecast for {customer}, {product}")
                return None

            return forecast_df

        # Pattern Copy model removed

        elif model_name == "TCI-fix":
            # Use the TCI-fix model
            # Check if the model exists
            model_dir = f"models/tci_fix/{customer.replace('/', '_')}_{product.replace('/', '_')}"
            model_path = os.path.join(model_dir, "model.pkl")

            # Debug info
            logger.info(f"Looking for TCI-fix model at: {model_path}")

            if not os.path.exists(model_path):
                logger.info(f"No pre-trained TCI-fix model found. Training a new one.")

                # Train a temporary model with cutoff date
                data_with_cutoff = self._prepare_data_for_model(customer, product, cutoff_date=cutoff_date)

                if data_with_cutoff is None or data_with_cutoff.empty:
                    logger.warning(f"No data available for {customer}/{product}")
                    return None

                # Get external factors if available
                external_factors = self._get_external_factors(customer, product)

                # Get industry data for Japan and Indonesia
                try:
                    from tci_fix_integration import load_external_data
                    industry_data = load_external_data(start_year=2016, end_year=2025)
                    if industry_data:
                        logger.info(f"Loaded {len(industry_data)} industry data sources for prediction")
                        # Merge industry data with external factors
                        if external_factors is None:
                            external_factors = {}
                        external_factors.update(industry_data)
                except Exception as e:
                    logger.warning(f"Failed to load industry data for prediction: {str(e)}")

                # Check for incremental learning and hyperparameter tuning flags
                incremental = kwargs.pop('incremental', False)
                tune_hyperparams = kwargs.pop('tune_hyperparams', False)

                # Log what we're doing
                if incremental:
                    logger.info(f"Using incremental learning for {customer}/{product}")
                if tune_hyperparams:
                    logger.info(f"Tuning hyperparameters for {customer}/{product}")

                temp_success, temp_message = train_tci_fix_model(
                    data_with_cutoff, customer, product, external_factors=external_factors,
                    incremental=incremental, tune_hyperparams=tune_hyperparams, **kwargs
                )

                if not temp_success:
                    logger.warning(f"Failed to train temporary TCI-fix model with cutoff date: {temp_message}")
                    return None

            # Generate predictions
            forecast_df = predict_with_tci_fix(customer, product, periods)

            if forecast_df is None:
                logger.warning(f"Failed to generate TCI-fix forecast for {customer}, {product}")
                return None

            return forecast_df

        elif model_name == "Enhanced TCI-fix":
            # Use the Enhanced TCI-fix model
            if not hasattr(self, 'enhanced_tci_fix') or self.enhanced_tci_fix is None:
                logger.error("Enhanced TCI-fix not available")
                return None

            # Generate enhanced predictions
            forecast_df = self.enhanced_tci_fix.predict_enhanced(customer, product, periods)

            if forecast_df is None:
                logger.warning(f"Failed to generate Enhanced TCI-fix forecast for {customer}, {product}")
                return None

            logger.info(f"Enhanced TCI-fix forecast generated for {customer}, {product}: {len(forecast_df)} periods")
            return forecast_df

        else:
            logger.error(f"Available models: TCI, TCI Premium, TCI-fix, Enhanced TCI-fix")
            return None

    def get_model_components(self, model_name, customer, product):
        """
        Get model components for visualization.

        Parameters:
        -----------
        model_name : str
            Name of the model
        customer : str
            Customer name
        product : str
            Product name

        Returns:
        --------
        dict
            Dictionary of model components
        """
        logger.info(f"Getting model components for {model_name}, {customer}/{product}")

        if model_name == "TCI":
            # Get TCI model components
            return {
                "seasonality_path": f"results/tci/{customer}_{product}_seasonality.png",
                "components_path": f"results/tci/{customer}_{product}_components.png",
                "forecast_path": f"results/tci/{customer}_{product}_forecast.png",
                "metrics": self.tci_predictor.get_metrics(customer, product)
            }

        elif model_name == "TCI Premium":
            # Get TCI Premium model components
            return {
                "model_type": "TCI Premium",
                "seasonality_path": f"models/tci_premium/{customer.replace('/', '_')}_{product.replace('/', '_')}/seasonality.png",
                "components_path": f"models/tci_premium/{customer.replace('/', '_')}_{product.replace('/', '_')}/components.png",
                "forecast_path": f"models/tci_premium/{customer.replace('/', '_')}_{product.replace('/', '_')}/forecast.png",
                "feature_importance": f"models/tci_premium/{customer.replace('/', '_')}_{product.replace('/', '_')}/feature_importance.png",
                "metrics": self.tci_premium_predictor.get_metrics(customer, product)
            }

        # Pattern Copy model removed

        elif model_name == "TCI-fix":
            # Get TCI-fix model components
            return {
                "model_type": "TCI-fix",
                "seasonality_path": f"models/tci_fix/{customer.replace('/', '_')}_{product.replace('/', '_')}/seasonality.png",
                "components_path": f"models/tci_fix/{customer.replace('/', '_')}_{product.replace('/', '_')}/components.png",
                "forecast_path": f"models/tci_fix/{customer.replace('/', '_')}_{product.replace('/', '_')}/forecast.png",
                "metrics": {}  # TCI-fix doesn't have metrics yet
            }

        else:
            logger.error(f"Available models: TCI, TCI Premium, TCI-fix")
            return {}

    def visualize_forecast(self, model_name, customer, product, forecast_df, historical_data=None):
        """
        Visualize forecast results.

        Parameters:
        -----------
        model_name : str
            Name of the model
        customer : str
            Customer name
        product : str
            Product name
        forecast_df : DataFrame
            Forecast results
        historical_data : DataFrame, optional
            Historical data

        Returns:
        --------
        tuple
            (fig, ax) matplotlib figure and axes
        """
        logger.info(f"Visualizing forecast for {model_name}, {customer}/{product}")

        if model_name == "TCI-fix":
            # Use the TCI-fix visualization function
            return visualize_tci_fix_results(customer, product, forecast_df, historical_data)

        # For other models, use a standard visualization
        fig, ax = plt.subplots(figsize=(12, 6))

        # Plot historical data if available
        if historical_data is not None:
            ax.plot(historical_data['ds'], historical_data['y'], 'b-', label='Historical')

        # Plot forecast
        # Check if we have the new column names or the old ones
        if 'date' in forecast_df.columns:
            date_col = 'date'
            value_col = 'prediction'
            lower_col = 'lower_bound'
            upper_col = 'upper_bound'
        else:
            date_col = 'ds'
            value_col = 'yhat'
            lower_col = 'yhat_lower'
            upper_col = 'yhat_upper'

        ax.plot(forecast_df[date_col], forecast_df[value_col], 'r-', label='Forecast')

        # Plot confidence intervals if available
        if lower_col in forecast_df.columns and upper_col in forecast_df.columns:
            ax.fill_between(forecast_df[date_col], forecast_df[lower_col], forecast_df[upper_col], color='r', alpha=0.2)

        # Set labels and title
        ax.set_xlabel('Date')
        ax.set_ylabel('Quantity')
        ax.set_title(f'{model_name} Forecast for {customer} - {product}')
        ax.legend()

        # Format x-axis
        fig.autofmt_xdate()

        return fig, ax

    def _get_external_factors(self, customer, product):
        """
        Get external factors for a specific customer and product.

        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name

        Returns:
        --------
        dict
            Dictionary of external factors
        """
        # Check if API fetcher is available
        if not self.api_fetcher:
            return {}

        try:
            # Get customer country
            country = self._get_customer_country(customer)

            if not country:
                logger.warning(f"No country information for customer: {customer}")
                return {}

            # Get exchange rate data
            exchange_rates = self.api_fetcher.get_exchange_rates(country)

            # Get holiday data
            holidays = self.api_fetcher.get_holidays(country)

            # Get industrial production data
            industrial_production = self.api_fetcher.get_industrial_production(country)

            # Get manufacturing data
            manufacturing_data = self.api_fetcher.get_manufacturing_data(country)

            # Combine all external factors
            external_factors = {
                "exchange_rates": exchange_rates,
                "holidays": holidays,
                "industrial_production": industrial_production,
                "manufacturing_data": manufacturing_data
            }

            return external_factors

        except Exception as e:
            logger.error(f"Error getting external factors: {str(e)}")
            return {}

    def _get_customer_country(self, customer):
        """
        Get country for a specific customer.

        Parameters:
        -----------
        customer : str
            Customer name

        Returns:
        --------
        str
            Country code
        """
        # Check if data manager is available
        if not self.data_manager:
            return None

        # Check if data manager has customer_countries attribute
        if hasattr(self.data_manager, 'customer_countries'):
            return self.data_manager.customer_countries.get(customer)

        # Try to infer country from customer name
        if '(JPN)' in customer:
            return 'JP'
        elif '(IND)' in customer:
            return 'IN'
        elif '(INT)' in customer:
            return 'ID'  # Indonesia

        return None

    def _prepare_data_for_model(self, customer, product, cutoff_date=None):
        """
        Prepare data for model training and prediction.

        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name
        cutoff_date : str, optional
            If provided, only use data up to this date (format: 'YYYY-MM-DD')

        Returns:
        --------
        DataFrame
            DataFrame in model format (ds, y)
        """
        # Get data from data manager
        key = f"{customer}_{product}"

        # Try to get data from the data manager
        try:
            # First try to access through the data_manager attribute
            if hasattr(self, 'data_manager') and hasattr(self.data_manager, 'data'):
                if key in self.data_manager.data:
                    df = self.data_manager.data[key].copy()

                    # Convert to standard format
                    model_df = pd.DataFrame({
                        'ds': pd.to_datetime(df['date']),  # Ensure datetime format
                        'y': df['quantity']
                    })

                    # Apply cutoff date if provided
                    if cutoff_date is not None:
                        cutoff_date = pd.to_datetime(cutoff_date)
                        logger.info(f"Applying cutoff date: {cutoff_date} for {customer}_{product}")
                        model_df = model_df[model_df['ds'] <= cutoff_date]
                        logger.info(f"Data after cutoff: {len(model_df)} records")

                    return model_df

            # If that fails, try loading directly from file
            file_path = f"dataset/{customer}/{product}.csv"
            if os.path.exists(file_path):
                try:
                    df = pd.read_csv(file_path)

                    # Convert to standard format
                    if 'date' in df.columns and 'quantity' in df.columns:
                        model_df = pd.DataFrame({
                            'ds': pd.to_datetime(df['date']),  # Ensure datetime format
                            'y': df['quantity']
                        })

                        # Apply cutoff date if provided
                        if cutoff_date is not None:
                            cutoff_date = pd.to_datetime(cutoff_date)
                            model_df = model_df[model_df['ds'] <= cutoff_date]

                        return model_df
                except Exception as e:
                    logger.warning(f"Error loading data from file: {str(e)}")
        except Exception as e:
            logger.warning(f"Error preparing data: {str(e)}")

        logger.warning(f"No data found for customer: {customer}, product: {product}")
        return None

    def export_forecast(self, model_name, customer, product, forecast_df, file_path):
        """
        Export forecast to CSV file with organized folder structure.

        Creates a folder structure: forecasts/{customer}/{product}.csv

        Parameters:
        -----------
        model_name : str
            Name of the model used
        customer : str
            Customer name
        product : str
            Product name
        forecast_df : DataFrame
            Forecast data to export
        file_path : str
            Original file path (will be modified to use organized structure)

        Returns:
        --------
        bool
            True if export successful, False otherwise
        """
        try:
            # Create organized folder structure
            # Get the directory from the original file path
            base_dir = os.path.dirname(file_path) if os.path.dirname(file_path) else "."

            # Create customer folder
            customer_folder = os.path.join(base_dir, "forecasts", customer.replace('/', '_').replace('\\', '_'))
            os.makedirs(customer_folder, exist_ok=True)

            # Create filename based on product name
            # Clean product name for filename
            clean_product_name = product.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')

            # Create the final file path
            final_file_path = os.path.join(customer_folder, f"{clean_product_name}.csv")

            # Prepare export data based on model type
            if model_name == "TCI-fix":
                # TCI-fix model format
                if 'date' in forecast_df.columns and 'prediction' in forecast_df.columns:
                    export_data = forecast_df.copy()

                    # Rename columns for better readability
                    column_mapping = {
                        'date': 'Date',
                        'prediction': 'Predicted_Quantity'
                    }

                    # Add confidence intervals if available
                    if 'lower_bound' in forecast_df.columns:
                        column_mapping['lower_bound'] = 'Lower_Bound'
                    if 'upper_bound' in forecast_df.columns:
                        column_mapping['upper_bound'] = 'Upper_Bound'

                    export_data = export_data.rename(columns=column_mapping)

                    # Select only the columns we want to export
                    export_columns = ['Date', 'Predicted_Quantity']
                    if 'Lower_Bound' in export_data.columns:
                        export_columns.append('Lower_Bound')
                    if 'Upper_Bound' in export_data.columns:
                        export_columns.append('Upper_Bound')

                    export_data = export_data[export_columns]
                else:
                    export_data = forecast_df
            else:
                # Other models format (TCI, TCI Premium, etc.)
                if 'ds' in forecast_df.columns and 'yhat' in forecast_df.columns:
                    export_data = forecast_df[['ds', 'yhat', 'yhat_lower', 'yhat_upper']].rename(
                        columns={
                            'ds': 'Date',
                            'yhat': 'Predicted_Quantity',
                            'yhat_lower': 'Lower_Bound',
                            'yhat_upper': 'Upper_Bound'
                        }
                    )
                else:
                    export_data = forecast_df

            # Format dates if they exist
            if 'Date' in export_data.columns:
                export_data['Date'] = pd.to_datetime(export_data['Date']).dt.strftime('%Y-%m-%d')

            # Round numeric columns to integers for tidier presentation
            numeric_columns = ['Predicted_Quantity', 'Lower_Bound', 'Upper_Bound']
            for col in numeric_columns:
                if col in export_data.columns:
                    export_data[col] = export_data[col].round().astype(int)

            # Export to CSV
            export_data.to_csv(final_file_path, index=False)

            logger.info(f"Forecast exported to: {final_file_path}")
            logger.info(f"Folder structure: forecasts/{customer}/{clean_product_name}.csv")

            return True

        except Exception as e:
            logger.error(f"Error exporting forecast: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def export_report(self, model_name, customer, product, forecast_df, file_path):
        """
        Export a comprehensive report with forecast analysis.

        Parameters:
        -----------
        model_name : str
            Name of the model used
        customer : str
            Customer name
        product : str
            Product name
        forecast_df : DataFrame
            Forecast data
        file_path : str
            Path to save the report

        Returns:
        --------
        bool
            True if export successful, False otherwise
        """
        try:
            # Create organized folder structure for reports
            base_dir = os.path.dirname(file_path) if os.path.dirname(file_path) else "."
            customer_folder = os.path.join(base_dir, "reports", customer.replace('/', '_').replace('\\', '_'))
            os.makedirs(customer_folder, exist_ok=True)

            # Create filename based on product name
            clean_product_name = product.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
            final_file_path = os.path.join(customer_folder, f"{clean_product_name}_report.md")

            # Generate report content
            report_content = f"""# Forecast Report

## Model Information
- **Model**: {model_name}
- **Customer**: {customer}
- **Product**: {product}
- **Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Forecast Summary
- **Forecast Period**: {len(forecast_df)} periods
- **Start Date**: {forecast_df.iloc[0]['date'] if 'date' in forecast_df.columns else forecast_df.iloc[0]['ds']}
- **End Date**: {forecast_df.iloc[-1]['date'] if 'date' in forecast_df.columns else forecast_df.iloc[-1]['ds']}

## Forecast Data
"""

            # Add forecast table
            if 'date' in forecast_df.columns and 'prediction' in forecast_df.columns:
                report_content += "| Date | Predicted Quantity | Lower Bound | Upper Bound |\n"
                report_content += "|------|-------------------|-------------|-------------|\n"
                for _, row in forecast_df.iterrows():
                    date_str = row['date'].strftime('%Y-%m-%d') if hasattr(row['date'], 'strftime') else str(row['date'])
                    pred = int(row['prediction']) if 'prediction' in row else 'N/A'
                    lower = int(row['lower_bound']) if 'lower_bound' in row and pd.notna(row['lower_bound']) else 'N/A'
                    upper = int(row['upper_bound']) if 'upper_bound' in row and pd.notna(row['upper_bound']) else 'N/A'
                    report_content += f"| {date_str} | {pred} | {lower} | {upper} |\n"

            # Write report to file
            with open(final_file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"Report exported to: {final_file_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting report: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
