"""
Causal analysis tools for TCI.

This module provides functions for analyzing causal graphs in the TCI framework,
including graph metrics, comparison, and evaluation.
"""

import numpy as np
import networkx as nx
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix

def calculate_graph_metrics(
    G: nx.DiGraph
) -> Dict[str, float]:
    """
    Calculate various metrics for a causal graph.
    
    Parameters
    ----------
    G : nx.DiGraph
        Causal graph
    
    Returns
    -------
    Dict[str, float]
        Dictionary of graph metrics
    """
    # Basic metrics
    n_nodes = G.number_of_nodes()
    n_edges = G.number_of_edges()
    density = nx.density(G)
    
    # Degree metrics
    in_degrees = dict(G.in_degree())
    out_degrees = dict(G.out_degree())
    
    avg_in_degree = sum(in_degrees.values()) / n_nodes if n_nodes > 0 else 0
    avg_out_degree = sum(out_degrees.values()) / n_nodes if n_nodes > 0 else 0
    max_in_degree = max(in_degrees.values()) if in_degrees else 0
    max_out_degree = max(out_degrees.values()) if out_degrees else 0
    
    # Centrality metrics
    try:
        betweenness = nx.betweenness_centrality(G)
        avg_betweenness = sum(betweenness.values()) / n_nodes if n_nodes > 0 else 0
        max_betweenness = max(betweenness.values()) if betweenness else 0
    except:
        avg_betweenness = max_betweenness = 0
    
    try:
        closeness = nx.closeness_centrality(G)
        avg_closeness = sum(closeness.values()) / n_nodes if n_nodes > 0 else 0
        max_closeness = max(closeness.values()) if closeness else 0
    except:
        avg_closeness = max_closeness = 0
    
    # Connectivity metrics
    try:
        strongly_connected = nx.number_strongly_connected_components(G)
        weakly_connected = nx.number_weakly_connected_components(G)
    except:
        strongly_connected = weakly_connected = 0
    
    # Edge weight metrics
    edge_weights = [data.get('weight', 1.0) for _, _, data in G.edges(data=True)]
    
    if edge_weights:
        avg_weight = sum(edge_weights) / len(edge_weights)
        max_weight = max(edge_weights)
        min_weight = min(edge_weights)
        std_weight = np.std(edge_weights)
    else:
        avg_weight = max_weight = min_weight = std_weight = 0
    
    # Compile metrics
    metrics = {
        'n_nodes': n_nodes,
        'n_edges': n_edges,
        'density': density,
        'avg_in_degree': avg_in_degree,
        'avg_out_degree': avg_out_degree,
        'max_in_degree': max_in_degree,
        'max_out_degree': max_out_degree,
        'avg_betweenness': avg_betweenness,
        'max_betweenness': max_betweenness,
        'avg_closeness': avg_closeness,
        'max_closeness': max_closeness,
        'strongly_connected_components': strongly_connected,
        'weakly_connected_components': weakly_connected,
        'avg_weight': avg_weight,
        'max_weight': max_weight,
        'min_weight': min_weight,
        'std_weight': std_weight
    }
    
    return metrics

def compare_causal_graphs(
    G1: nx.DiGraph,
    G2: nx.DiGraph
) -> Dict[str, Any]:
    """
    Compare two causal graphs.
    
    Parameters
    ----------
    G1 : nx.DiGraph
        First causal graph
    G2 : nx.DiGraph
        Second causal graph
    
    Returns
    -------
    Dict[str, Any]
        Dictionary of comparison metrics
    """
    # Get edges from each graph
    edges1 = set(G1.edges())
    edges2 = set(G2.edges())
    
    # Calculate overlap
    common_edges = edges1.intersection(edges2)
    unique_edges1 = edges1 - edges2
    unique_edges2 = edges2 - edges1
    
    # Calculate Jaccard similarity
    jaccard = len(common_edges) / len(edges1.union(edges2)) if edges1 or edges2 else 0
    
    # Calculate precision, recall, and F1 score
    if edges1:
        precision = len(common_edges) / len(edges2) if edges2 else 0
    else:
        precision = 0
    
    if edges2:
        recall = len(common_edges) / len(edges1) if edges1 else 0
    else:
        recall = 0
    
    f1 = 2 * precision * recall / (precision + recall) if precision + recall > 0 else 0
    
    # Calculate graph metrics for each graph
    metrics1 = calculate_graph_metrics(G1)
    metrics2 = calculate_graph_metrics(G2)
    
    # Calculate differences in metrics
    metric_diffs = {}
    for key in metrics1:
        if key in metrics2:
            metric_diffs[key] = metrics2[key] - metrics1[key]
    
    # Compile comparison results
    comparison = {
        'common_edges': list(common_edges),
        'unique_edges1': list(unique_edges1),
        'unique_edges2': list(unique_edges2),
        'jaccard_similarity': jaccard,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'metrics1': metrics1,
        'metrics2': metrics2,
        'metric_diffs': metric_diffs
    }
    
    return comparison

def evaluate_causal_graph(
    predicted_graph: nx.DiGraph,
    true_graph: nx.DiGraph
) -> Dict[str, float]:
    """
    Evaluate a predicted causal graph against a ground truth.
    
    Parameters
    ----------
    predicted_graph : nx.DiGraph
        Predicted causal graph
    true_graph : nx.DiGraph
        Ground truth causal graph
    
    Returns
    -------
    Dict[str, float]
        Dictionary of evaluation metrics
    """
    # Get edges from each graph
    true_edges = set(true_graph.edges())
    pred_edges = set(predicted_graph.edges())
    
    # Calculate true positives, false positives, and false negatives
    true_positives = true_edges.intersection(pred_edges)
    false_positives = pred_edges - true_edges
    false_negatives = true_edges - pred_edges
    
    # Calculate precision, recall, and F1 score
    precision = len(true_positives) / len(pred_edges) if pred_edges else 0
    recall = len(true_positives) / len(true_edges) if true_edges else 0
    f1 = 2 * precision * recall / (precision + recall) if precision + recall > 0 else 0
    
    # Calculate structural Hamming distance (SHD)
    # SHD counts the number of edge additions, deletions, and reversals needed to transform one graph into another
    shd = len(false_positives) + len(false_negatives)
    
    # Check for edge reversals
    reversals = 0
    for u, v in false_positives:
        if (v, u) in false_negatives:
            reversals += 1
    
    # Adjust SHD for reversals (each reversal counts as one edit, not two)
    shd -= reversals
    
    # Calculate normalized SHD
    max_edges = len(true_graph.nodes()) * (len(true_graph.nodes()) - 1)
    normalized_shd = shd / max_edges if max_edges > 0 else 0
    
    # Compile evaluation metrics
    evaluation = {
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'true_positives': len(true_positives),
        'false_positives': len(false_positives),
        'false_negatives': len(false_negatives),
        'edge_reversals': reversals,
        'shd': shd,
        'normalized_shd': normalized_shd
    }
    
    return evaluation

def identify_key_causal_variables(
    G: nx.DiGraph,
    top_n: int = 5
) -> Dict[str, List[Tuple[int, float]]]:
    """
    Identify key causal variables in a graph.
    
    Parameters
    ----------
    G : nx.DiGraph
        Causal graph
    top_n : int, optional
        Number of top variables to return, by default 5
    
    Returns
    -------
    Dict[str, List[Tuple[int, float]]]
        Dictionary mapping metric names to lists of (node, score) tuples
    """
    # Calculate various centrality metrics
    in_degree = dict(G.in_degree())
    out_degree = dict(G.out_degree())
    
    try:
        betweenness = nx.betweenness_centrality(G)
    except:
        betweenness = {node: 0 for node in G.nodes()}
    
    try:
        closeness = nx.closeness_centrality(G)
    except:
        closeness = {node: 0 for node in G.nodes()}
    
    try:
        pagerank = nx.pagerank(G)
    except:
        pagerank = {node: 0 for node in G.nodes()}
    
    # Calculate weighted out-degree (sum of outgoing edge weights)
    weighted_out = {}
    for node in G.nodes():
        weighted_out[node] = sum(data.get('weight', 1.0) for _, _, data in G.out_edges(node, data=True))
    
    # Sort nodes by each metric
    top_in_degree = sorted(in_degree.items(), key=lambda x: x[1], reverse=True)[:top_n]
    top_out_degree = sorted(out_degree.items(), key=lambda x: x[1], reverse=True)[:top_n]
    top_betweenness = sorted(betweenness.items(), key=lambda x: x[1], reverse=True)[:top_n]
    top_closeness = sorted(closeness.items(), key=lambda x: x[1], reverse=True)[:top_n]
    top_pagerank = sorted(pagerank.items(), key=lambda x: x[1], reverse=True)[:top_n]
    top_weighted_out = sorted(weighted_out.items(), key=lambda x: x[1], reverse=True)[:top_n]
    
    # Compile results
    key_variables = {
        'in_degree': top_in_degree,
        'out_degree': top_out_degree,
        'betweenness': top_betweenness,
        'closeness': top_closeness,
        'pagerank': top_pagerank,
        'weighted_out': top_weighted_out
    }
    
    return key_variables

def analyze_causal_paths(
    G: nx.DiGraph,
    source: int,
    target: int,
    max_paths: int = 5
) -> List[Dict[str, Any]]:
    """
    Analyze causal paths between source and target nodes.
    
    Parameters
    ----------
    G : nx.DiGraph
        Causal graph
    source : int
        Source node
    target : int
        Target node
    max_paths : int, optional
        Maximum number of paths to return, by default 5
    
    Returns
    -------
    List[Dict[str, Any]]
        List of path information dictionaries
    """
    # Find all simple paths from source to target
    try:
        all_paths = list(nx.all_simple_paths(G, source, target))
    except:
        all_paths = []
    
    # Calculate path weights
    path_info = []
    
    for path in all_paths:
        # Calculate path length
        path_length = len(path) - 1
        
        # Calculate path weight (product of edge weights)
        path_weight = 1.0
        for i in range(path_length):
            u, v = path[i], path[i+1]
            edge_data = G.get_edge_data(u, v)
            weight = edge_data.get('weight', 1.0) if edge_data else 1.0
            path_weight *= weight
        
        # Calculate average edge weight
        avg_edge_weight = path_weight ** (1 / path_length) if path_length > 0 else 0
        
        # Add path info
        path_info.append({
            'path': path,
            'length': path_length,
            'total_weight': path_weight,
            'avg_weight': avg_edge_weight
        })
    
    # Sort paths by total weight
    path_info.sort(key=lambda x: abs(x['total_weight']), reverse=True)
    
    # Return top paths
    return path_info[:max_paths]

def analyze_causal_communities(
    G: nx.DiGraph,
    method: str = 'louvain'
) -> Dict[str, Any]:
    """
    Analyze communities in a causal graph.
    
    Parameters
    ----------
    G : nx.DiGraph
        Causal graph
    method : str, optional
        Community detection method, by default 'louvain'
    
    Returns
    -------
    Dict[str, Any]
        Dictionary with community analysis results
    """
    # Convert directed graph to undirected for community detection
    G_undirected = G.to_undirected()
    
    # Detect communities
    communities = {}
    
    if method == 'louvain':
        try:
            import community as community_louvain
            partition = community_louvain.best_partition(G_undirected)
            
            # Group nodes by community
            for node, community_id in partition.items():
                if community_id not in communities:
                    communities[community_id] = []
                communities[community_id].append(node)
        except ImportError:
            # Fallback to connected components
            communities = {i: list(comp) for i, comp in enumerate(nx.connected_components(G_undirected))}
    else:
        # Use connected components
        communities = {i: list(comp) for i, comp in enumerate(nx.connected_components(G_undirected))}
    
    # Calculate community metrics
    community_metrics = {}
    
    for community_id, nodes in communities.items():
        # Create subgraph for this community
        subgraph = G.subgraph(nodes)
        
        # Calculate metrics
        metrics = calculate_graph_metrics(subgraph)
        
        # Add to community metrics
        community_metrics[community_id] = {
            'nodes': nodes,
            'size': len(nodes),
            'metrics': metrics
        }
    
    # Calculate inter-community edges
    inter_community_edges = []
    
    for u, v, data in G.edges(data=True):
        # Find communities for source and target
        source_community = None
        target_community = None
        
        for community_id, nodes in communities.items():
            if u in nodes:
                source_community = community_id
            if v in nodes:
                target_community = community_id
        
        # Check if edge crosses communities
        if source_community is not None and target_community is not None and source_community != target_community:
            inter_community_edges.append({
                'source': u,
                'target': v,
                'source_community': source_community,
                'target_community': target_community,
                'weight': data.get('weight', 1.0)
            })
    
    # Compile results
    results = {
        'communities': community_metrics,
        'num_communities': len(communities),
        'inter_community_edges': inter_community_edges,
        'modularity': None  # Placeholder for modularity
    }
    
    # Calculate modularity if possible
    if method == 'louvain':
        try:
            import community as community_louvain
            partition = {node: community_id for community_id, nodes in communities.items() for node in nodes}
            modularity = community_louvain.modularity(partition, G_undirected)
            results['modularity'] = modularity
        except ImportError:
            pass
    
    return results

def create_causal_analysis_report(
    G: nx.DiGraph,
    true_G: Optional[nx.DiGraph] = None,
    output_path: Optional[str] = None,
    node_names: Optional[Dict[int, str]] = None
) -> Dict[str, Any]:
    """
    Create a comprehensive causal analysis report.
    
    Parameters
    ----------
    G : nx.DiGraph
        Causal graph to analyze
    true_G : Optional[nx.DiGraph], optional
        Ground truth causal graph, by default None
    output_path : Optional[str], optional
        Path to save the report, by default None
    node_names : Optional[Dict[int, str]], optional
        Dictionary mapping node indices to names, by default None
    
    Returns
    -------
    Dict[str, Any]
        Dictionary with analysis results
    """
    # Initialize report
    report = {
        'graph_metrics': None,
        'key_variables': None,
        'communities': None,
        'evaluation': None
    }
    
    # Calculate graph metrics
    report['graph_metrics'] = calculate_graph_metrics(G)
    
    # Identify key variables
    report['key_variables'] = identify_key_causal_variables(G)
    
    # Analyze communities
    try:
        report['communities'] = analyze_causal_communities(G)
    except:
        report['communities'] = None
    
    # Evaluate against ground truth if available
    if true_G is not None:
        report['evaluation'] = evaluate_causal_graph(G, true_G)
    
    # Create report file if output path is provided
    if output_path is not None:
        # Format node names
        def format_node(node):
            if node_names is not None and node in node_names:
                return f"{node} ({node_names[node]})"
            return str(node)
        
        # Create report content
        content = "# Causal Graph Analysis Report\n\n"
        
        # Add graph metrics
        content += "## Graph Metrics\n\n"
        metrics = report['graph_metrics']
        content += f"- Number of nodes: {metrics['n_nodes']}\n"
        content += f"- Number of edges: {metrics['n_edges']}\n"
        content += f"- Graph density: {metrics['density']:.4f}\n"
        content += f"- Average in-degree: {metrics['avg_in_degree']:.2f}\n"
        content += f"- Average out-degree: {metrics['avg_out_degree']:.2f}\n"
        content += f"- Average edge weight: {metrics['avg_weight']:.4f}\n"
        content += f"- Strongly connected components: {metrics['strongly_connected_components']}\n"
        content += f"- Weakly connected components: {metrics['weakly_connected_components']}\n\n"
        
        # Add key variables
        content += "## Key Causal Variables\n\n"
        
        # Add top variables by out-degree
        content += "### Top Variables by Out-degree (Causal Influence)\n\n"
        for node, degree in report['key_variables']['out_degree']:
            content += f"- {format_node(node)}: {degree}\n"
        content += "\n"
        
        # Add top variables by in-degree
        content += "### Top Variables by In-degree (Causal Dependence)\n\n"
        for node, degree in report['key_variables']['in_degree']:
            content += f"- {format_node(node)}: {degree}\n"
        content += "\n"
        
        # Add top variables by betweenness
        content += "### Top Variables by Betweenness Centrality (Causal Mediation)\n\n"
        for node, centrality in report['key_variables']['betweenness']:
            content += f"- {format_node(node)}: {centrality:.4f}\n"
        content += "\n"
        
        # Add top variables by weighted out-degree
        content += "### Top Variables by Weighted Out-degree (Strength of Causal Influence)\n\n"
        for node, weight in report['key_variables']['weighted_out']:
            content += f"- {format_node(node)}: {weight:.4f}\n"
        content += "\n"
        
        # Add community analysis if available
        if report['communities'] is not None:
            content += "## Causal Communities\n\n"
            content += f"Number of communities: {report['communities']['num_communities']}\n\n"
            
            # Add community details
            for community_id, community in report['communities']['communities'].items():
                content += f"### Community {community_id}\n\n"
                content += f"Size: {community['size']} nodes\n"
                content += "Nodes: "
                
                # Format node list
                node_strs = [format_node(node) for node in community['nodes']]
                content += ", ".join(node_strs)
                content += "\n\n"
            
            # Add inter-community edges
            content += "### Inter-community Causal Relationships\n\n"
            for edge in report['communities']['inter_community_edges'][:10]:  # Show top 10
                source = format_node(edge['source'])
                target = format_node(edge['target'])
                content += f"- {source} (Community {edge['source_community']}) → {target} (Community {edge['target_community']}), Weight: {edge['weight']:.4f}\n"
            content += "\n"
        
        # Add evaluation if available
        if report['evaluation'] is not None:
            content += "## Evaluation Against Ground Truth\n\n"
            eval_metrics = report['evaluation']
            content += f"- Precision: {eval_metrics['precision']:.4f}\n"
            content += f"- Recall: {eval_metrics['recall']:.4f}\n"
            content += f"- F1 Score: {eval_metrics['f1_score']:.4f}\n"
            content += f"- True Positives: {eval_metrics['true_positives']}\n"
            content += f"- False Positives: {eval_metrics['false_positives']}\n"
            content += f"- False Negatives: {eval_metrics['false_negatives']}\n"
            content += f"- Edge Reversals: {eval_metrics['edge_reversals']}\n"
            content += f"- Structural Hamming Distance (SHD): {eval_metrics['shd']}\n"
            content += f"- Normalized SHD: {eval_metrics['normalized_shd']:.4f}\n\n"
        
        # Write report to file
        with open(output_path, 'w') as f:
            f.write(content)
    
    return report
