"""
Demonstration of transfer learning with TCI.

This script demonstrates how to save a trained model and use it for transfer learning
on a different environment.
"""

import os
import argparse
import numpy as np
import matplotlib.pyplot as plt
from tabulate import tabulate

from tci.core.agent import TCIAgent
from tci.environments.simple_env import SimpleEnvironment
from tci.environments.tep_env import TEPEnv
from tci.extensions.gat import GAT
from tci.extensions.ensemble import Ensemble


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Transfer Learning Demo for TCI")

    parser.add_argument("--source-env", type=str, default="simple",
                        choices=["simple", "tep"],
                        help="Source environment for pre-training")

    parser.add_argument("--target-env", type=str, default="tep",
                        choices=["simple", "tep"],
                        help="Target environment for transfer learning")

    parser.add_argument("--tep-dataset-path", type=str,
                        default="/home/<USER>/Ai/S3/tci/TEP_dataset",
                        help="Path to TEP dataset")

    parser.add_argument("--source-episodes", type=int, default=5,
                        help="Number of episodes for source training")

    parser.add_argument("--target-episodes", type=int, default=5,
                        help="Number of episodes for target training")

    parser.add_argument("--model-dir", type=str, default="models",
                        help="Directory to save/load models")

    parser.add_argument("--extensions", type=str, nargs="+",
                        choices=["gat", "ensemble", "none"],
                        default=["none"],
                        help="Extensions to use")

    parser.add_argument("--verbose", action="store_true",
                        help="Print verbose output")

    return parser.parse_args()


def create_environment(env_name, args):
    """Create environment based on name."""
    if env_name == "simple":
        return SimpleEnvironment()
    elif env_name == "tep":
        return TEPEnv(data=args.tep_dataset_path)
    else:
        raise ValueError(f"Unknown environment: {env_name}")


def create_agent(state_dim, action_dim, args):
    """Create a TCI agent with specified extensions."""
    # Create extensions
    extensions = []
    if "gat" in args.extensions:
        extensions.append(GAT())
    if "ensemble" in args.extensions:
        extensions.append(Ensemble(num_models=3))

    # Create agent
    agent = TCIAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        history_length=3,
        buffer_size=1000,
        learning_rate=0.001,
        hidden_dim=64,
        latent_dim=16,
        dropout_rate=0.2,
        device="cuda" if torch.cuda.is_available() else "cpu",
        causal_discovery_params={
            "method": "pc",
            "alpha": 0.1,
            "sparsity_factor": 0.1
        }
    )

    # Add extensions
    for extension in extensions:
        agent._add_extension(extension)

    return agent


def train_agent(agent, env, episodes, verbose=False):
    """Train an agent on an environment."""
    rewards = []

    for episode in range(episodes):
        state = env.reset()
        done = False
        episode_reward = 0
        step_count = 0

        try:
            while not done and step_count < 100:  # Add step limit to prevent infinite loops
                # Select action
                action = agent.act(state)

                # Take action
                next_state, reward, done, _ = env.step(action)

                # Store transition
                agent.store(state, action, reward, next_state)

                # Update state and reward
                state = next_state
                episode_reward += reward
                step_count += 1

            # Train agent if we have enough samples
            if len(agent.buffer) >= 10:
                for _ in range(5):  # Reduce training iterations
                    loss_dict = agent.train(batch_size=min(10, len(agent.buffer)))
                    if verbose and loss_dict:
                        loss_str = ", ".join([f"{k}: {v:.4f}" for k, v in loss_dict.items()])
                        print(f"Training losses: {loss_str}")

            # Infer causal graph if we have enough samples
            if len(agent.buffer) >= 50:
                try:
                    agent.infer_causal_graph(verbose=False)
                except Exception as e:
                    if verbose:
                        print(f"Error inferring causal graph: {e}")

            # Print progress
            if verbose:
                print(f"Episode {episode+1}/{episodes}, Steps: {step_count}, Reward: {episode_reward:.2f}")

            rewards.append(episode_reward)

        except Exception as e:
            print(f"Error during episode {episode+1}: {e}")
            rewards.append(float('nan'))  # Add NaN reward for failed episodes

    return rewards


def run_transfer_learning(args):
    """Run transfer learning experiment."""
    # Create directories
    os.makedirs(args.model_dir, exist_ok=True)

    # Step 1: Train on source environment
    print(f"\n=== Training on source environment: {args.source_env} ===")
    source_env = create_environment(args.source_env, args)

    # Get state and action dimensions
    if hasattr(source_env, 'observation_space'):
        state_dim = source_env.observation_space.shape[0]
        action_dim = source_env.action_space.n if hasattr(source_env.action_space, 'n') else source_env.action_space.shape[0]
    else:
        state_dim = source_env.state_dim
        action_dim = source_env.action_dim

    # Create and train source agent
    source_agent = create_agent(state_dim, action_dim, args)
    source_rewards = train_agent(source_agent, source_env, args.source_episodes, args.verbose)

    # Save source agent
    source_model_path = os.path.join(args.model_dir, f"{args.source_env}_agent.pt")
    source_agent.save(source_model_path, metadata={"env": args.source_env})
    print(f"Source agent saved to {source_model_path}")

    # Visualize causal graph
    try:
        source_graph_path = os.path.join(args.model_dir, f"{args.source_env}_causal_graph.png")
        fig = source_agent.visualize_causal_graph()
        if fig:
            fig.savefig(source_graph_path)
            plt.close(fig)
            print(f"Source causal graph saved to {source_graph_path}")
    except Exception as e:
        print(f"Error visualizing source causal graph: {e}")

    # Step 2: Train on target environment from scratch
    print(f"\n=== Training on target environment from scratch: {args.target_env} ===")
    target_env = create_environment(args.target_env, args)

    # Get state and action dimensions
    if hasattr(target_env, 'observation_space'):
        target_state_dim = target_env.observation_space.shape[0]
        target_action_dim = target_env.action_space.n if hasattr(target_env.action_space, 'n') else target_env.action_space.shape[0]
    else:
        target_state_dim = target_env.state_dim
        target_action_dim = target_env.action_dim

    # Create and train target agent from scratch
    scratch_agent = create_agent(target_state_dim, target_action_dim, args)
    scratch_rewards = train_agent(scratch_agent, target_env, args.target_episodes, args.verbose)

    # Step 3: Transfer learning from source to target
    print(f"\n=== Transfer learning from {args.source_env} to {args.target_env} ===")

    # Check if dimensions match
    if state_dim != target_state_dim or action_dim != target_action_dim:
        print(f"Warning: Source dimensions ({state_dim}, {action_dim}) don't match target dimensions ({target_state_dim}, {target_action_dim})")
        print("Creating new agent with target dimensions")
        transfer_agent = create_agent(target_state_dim, target_action_dim, args)
    else:
        # Load source agent for transfer learning
        extensions = []
        if "gat" in args.extensions:
            extensions.append(GAT())
        if "ensemble" in args.extensions:
            extensions.append(Ensemble(num_models=3))

        transfer_agent = TCIAgent.load(
            source_model_path,
            device="cuda" if torch.cuda.is_available() else "cpu",
            extensions=extensions,
            transfer_learning=True  # This resets optimizers but keeps weights
        )

    # Train with transfer learning
    transfer_rewards = train_agent(transfer_agent, target_env, args.target_episodes, args.verbose)

    # Save transfer agent
    transfer_model_path = os.path.join(args.model_dir, f"{args.source_env}_to_{args.target_env}_agent.pt")
    transfer_agent.save(transfer_model_path, metadata={"source_env": args.source_env, "target_env": args.target_env})
    print(f"Transfer agent saved to {transfer_model_path}")

    # Visualize transfer causal graph
    try:
        transfer_graph_path = os.path.join(args.model_dir, f"{args.source_env}_to_{args.target_env}_causal_graph.png")
        fig = transfer_agent.visualize_causal_graph()
        if fig:
            fig.savefig(transfer_graph_path)
            plt.close(fig)
            print(f"Transfer causal graph saved to {transfer_graph_path}")
    except Exception as e:
        print(f"Error visualizing transfer causal graph: {e}")

    # Compare results
    print("\n=== Results Comparison ===")

    # Calculate metrics
    source_mean = np.mean(source_rewards)
    source_std = np.std(source_rewards)
    scratch_mean = np.mean(scratch_rewards)
    scratch_std = np.std(scratch_rewards)
    transfer_mean = np.mean(transfer_rewards)
    transfer_std = np.std(transfer_rewards)

    # Create table
    table = [
        ["Source", f"{source_mean:.2f} ± {source_std:.2f}"],
        ["Target (Scratch)", f"{scratch_mean:.2f} ± {scratch_std:.2f}"],
        ["Target (Transfer)", f"{transfer_mean:.2f} ± {transfer_std:.2f}"]
    ]

    print(tabulate(table, headers=["Training", "Mean Reward ± Std"], tablefmt="grid"))

    # Plot learning curves
    plt.figure(figsize=(10, 6))
    plt.plot(source_rewards, label=f"Source ({args.source_env})")
    plt.plot(scratch_rewards, label=f"Target Scratch ({args.target_env})")
    plt.plot(transfer_rewards, label=f"Target Transfer ({args.source_env} → {args.target_env})")
    plt.xlabel("Episode")
    plt.ylabel("Reward")
    plt.title("Learning Curves")
    plt.legend()
    plt.grid(True)

    # Save plot
    plot_path = os.path.join(args.model_dir, f"{args.source_env}_to_{args.target_env}_learning_curves.png")
    plt.savefig(plot_path)
    plt.close()
    print(f"Learning curves saved to {plot_path}")

    return {
        "source_rewards": source_rewards,
        "scratch_rewards": scratch_rewards,
        "transfer_rewards": transfer_rewards
    }


if __name__ == "__main__":
    import torch
    args = parse_args()
    results = run_transfer_learning(args)
