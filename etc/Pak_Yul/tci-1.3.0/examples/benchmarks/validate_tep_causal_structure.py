#!/usr/bin/env python3
"""
Validate TEP causal structure against known process relationships.

This script compares discovered causal structures against known process relationships
in the Tennessee Eastman Process (TEP) environment.
"""

import os
import sys
import numpy as np
import pandas as pd
import argparse
import json
import matplotlib.pyplot as plt
import networkx as nx
from typing import Dict, List, Tuple, Set

# Add parent directory to path to import tci
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from tci.environments.tep_env import TEPEnv, TEP_VARIABLES
from tci.environments.tep_env_enhanced import TEPEnvEnhanced
from tci.core.causal_discovery import infer_causal_graph


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Validate TEP causal structure")
    parser.add_argument("--dataset-path", type=str,
                        default="/home/<USER>/Ai/S3/tci/TEP_dataset/TEP_FaultFree_Training.RData",
                        help="Path to TEP dataset")
    parser.add_argument("--output-dir", type=str, default="results/tep_causal_validation",
                        help="Output directory for results")
    parser.add_argument("--methods", type=str, default="pc,ges,notears,correlation,sparse",
                        help="Comma-separated list of causal discovery methods to test")
    parser.add_argument("--num-samples", type=int, default=5000,
                        help="Number of samples to use for causal discovery")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")
    return parser.parse_args()


def get_ground_truth_edges() -> List[Tuple[int, int]]:
    """
    Get ground truth causal edges for the TEP environment.
    
    Returns
    -------
    List[Tuple[int, int]]
        List of (source, target) edges
    """
    # Create a mapping from variable names to indices
    var_to_idx = {name: idx for idx, name in TEP_VARIABLES.items()}
    
    # Define known causal relationships
    ground_truth = [
        # Feed effects
        ("A Feed", "Reactor Feed"),
        ("D Feed", "Reactor Feed"),
        ("E Feed", "Reactor Feed"),
        ("A and C Feed", "Reactor Feed"),
        
        # Reactor dynamics
        ("Reactor Feed", "Reactor Pressure"),
        ("Reactor Feed", "Reactor Temp"),
        ("Reactor Feed", "Reactor Level"),
        ("Reactor Cooling Water Flow", "Reactor Temp"),
        
        # Separator dynamics
        ("Reactor Pressure", "Separator Pressure"),
        ("Reactor Temp", "Separator Temp"),
        ("Separator Pot Liquid Flow SP", "Separator Level"),
        ("Condenser Cooling Water Flow", "Separator Temp"),
        
        # Stripper dynamics
        ("Separator Level", "Stripper Level"),
        ("Stripper Liquid Product Flow SP", "Stripper Level"),
        ("Stripper Steam Valve", "Stripper Temperature"),
        
        # Recycle and purge dynamics
        ("Separator Pressure", "Recycle Flow"),
        ("Compressor Recycle Valve", "Recycle Flow"),
        ("Purge Valve", "Purge Rate"),
        
        # KPI relationships
        ("Stripper Liquid Product Flow SP", "Production Rate"),
        ("Reactor Temp", "Product Quality"),
        ("Reactor Pressure", "Product Quality"),
        ("A Feed", "Operating Cost"),
        ("D Feed", "Operating Cost"),
        ("E Feed", "Operating Cost"),
        ("Purge Valve", "Operating Cost"),
        ("Stripper Steam Valve", "Operating Cost"),
        ("Compressor Work", "Operating Cost")
    ]
    
    # Convert to indices
    edges = []
    for src, tgt in ground_truth:
        if src in var_to_idx and tgt in var_to_idx:
            edges.append((var_to_idx[src], var_to_idx[tgt]))
    
    return edges


def evaluate_causal_graph(
    discovered_edges: List[Tuple[int, int]],
    ground_truth_edges: List[Tuple[int, int]]
) -> Dict[str, float]:
    """
    Evaluate a discovered causal graph against ground truth.
    
    Parameters
    ----------
    discovered_edges : List[Tuple[int, int]]
        List of discovered (source, target) edges
    ground_truth_edges : List[Tuple[int, int]]
        List of ground truth (source, target) edges
    
    Returns
    -------
    Dict[str, float]
        Dictionary of evaluation metrics
    """
    # Convert to sets for easier comparison
    discovered_set = set(discovered_edges)
    ground_truth_set = set(ground_truth_edges)
    
    # Calculate true positives, false positives, false negatives
    true_positives = len(discovered_set.intersection(ground_truth_set))
    false_positives = len(discovered_set - ground_truth_set)
    false_negatives = len(ground_truth_set - discovered_set)
    
    # Calculate precision, recall, F1 score
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # Calculate structural Hamming distance (SHD)
    shd = false_positives + false_negatives
    
    return {
        "true_positives": true_positives,
        "false_positives": false_positives,
        "false_negatives": false_negatives,
        "precision": precision,
        "recall": recall,
        "f1_score": f1_score,
        "shd": shd
    }


def discover_causal_graph(
    data: pd.DataFrame,
    method: str,
    alpha: float = 0.1,
    sparsity_factor: float = 0.1,
    num_samples: int = 5000
) -> List[Tuple[int, int]]:
    """
    Discover causal graph from data using specified method.
    
    Parameters
    ----------
    data : pd.DataFrame
        DataFrame containing TEP data
    method : str
        Causal discovery method
    alpha : float, optional
        Significance level for independence tests, by default 0.1
    sparsity_factor : float, optional
        Sparsity factor for sparse causal discovery, by default 0.1
    num_samples : int, optional
        Number of samples to use, by default 5000
    
    Returns
    -------
    List[Tuple[int, int]]
        List of discovered (source, target) edges
    """
    # Limit number of samples
    data = data.iloc[:num_samples]
    
    # Extract states and next states
    states = data.iloc[:-1, :-1].values
    next_states = data.iloc[1:, :-1].values
    
    # Infer causal graph
    _, edges = infer_causal_graph(
        states, next_states, states.shape[1], verbose=False,
        method=method,
        alpha=alpha,
        sparsity_factor=sparsity_factor
    )
    
    return edges


def plot_causal_graph(
    edges: List[Tuple[int, int]],
    title: str,
    output_path: str,
    var_names: Dict[int, str] = None
):
    """
    Plot a causal graph.
    
    Parameters
    ----------
    edges : List[Tuple[int, int]]
        List of (source, target) edges
    title : str
        Plot title
    output_path : str
        Output path for the plot
    var_names : Dict[int, str], optional
        Dictionary mapping variable indices to names, by default None
    """
    # Create directed graph
    G = nx.DiGraph()
    
    # Add edges
    for src, tgt in edges:
        G.add_edge(src, tgt)
    
    # Create plot
    plt.figure(figsize=(12, 10))
    
    # Use spring layout for node positions
    pos = nx.spring_layout(G, seed=42)
    
    # Draw nodes
    nx.draw_networkx_nodes(G, pos, node_size=500, node_color='lightblue')
    
    # Draw edges
    nx.draw_networkx_edges(G, pos, arrowsize=20, width=1.5, edge_color='gray')
    
    # Draw labels
    if var_names is not None:
        labels = {node: var_names.get(node, f"Var{node}") for node in G.nodes()}
    else:
        labels = {node: f"Var{node}" for node in G.nodes()}
    
    nx.draw_networkx_labels(G, pos, labels=labels, font_size=10)
    
    # Set title and save
    plt.title(title)
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def create_html_report(
    results: Dict[str, Dict[str, float]],
    output_path: str
):
    """
    Create an HTML report of causal validation results.
    
    Parameters
    ----------
    results : Dict[str, Dict[str, float]]
        Dictionary mapping method names to evaluation metrics
    output_path : str
        Output path for the HTML report
    """
    # Start building HTML
    html = """<!DOCTYPE html>
    <html>
    <head>
        <title>TEP Causal Structure Validation Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #2c3e50; }
            h2 { color: #3498db; }
            h3 { color: #2980b9; }
            .container { max-width: 1200px; margin: 0 auto; }
            .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .info-box { background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .best { font-weight: bold; color: green; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>TEP Causal Structure Validation Report</h1>
            
            <div class="summary">
                <h2>Summary of Results</h2>
                <p>This report compares the causal structures discovered by different methods against known process relationships in the Tennessee Eastman Process (TEP).</p>
            </div>
            
            <h2>Evaluation Metrics</h2>
            <table>
                <tr>
                    <th>Method</th>
                    <th>Precision</th>
                    <th>Recall</th>
                    <th>F1 Score</th>
                    <th>SHD</th>
                    <th>True Positives</th>
                    <th>False Positives</th>
                    <th>False Negatives</th>
                </tr>
    """
    
    # Find best method for each metric
    best_precision = max(results.items(), key=lambda x: x[1]["precision"])[0]
    best_recall = max(results.items(), key=lambda x: x[1]["recall"])[0]
    best_f1 = max(results.items(), key=lambda x: x[1]["f1_score"])[0]
    best_shd = min(results.items(), key=lambda x: x[1]["shd"])[0]
    
    # Add rows for each method
    for method, metrics in sorted(results.items(), key=lambda x: x[1]["f1_score"], reverse=True):
        html += f"""
                <tr>
                    <td>{method}</td>
                    <td class="{'best' if method == best_precision else ''}">{metrics['precision']:.4f}</td>
                    <td class="{'best' if method == best_recall else ''}">{metrics['recall']:.4f}</td>
                    <td class="{'best' if method == best_f1 else ''}">{metrics['f1_score']:.4f}</td>
                    <td class="{'best' if method == best_shd else ''}">{metrics['shd']}</td>
                    <td>{metrics['true_positives']}</td>
                    <td>{metrics['false_positives']}</td>
                    <td>{metrics['false_negatives']}</td>
                </tr>
        """
    
    # Add information about metrics
    html += """
            </table>
            
            <div class="info-box">
                <h3>Metric Definitions</h3>
                <ul>
                    <li><strong>Precision</strong>: Proportion of discovered edges that are correct (TP / (TP + FP))</li>
                    <li><strong>Recall</strong>: Proportion of true edges that were discovered (TP / (TP + FN))</li>
                    <li><strong>F1 Score</strong>: Harmonic mean of precision and recall (2 * Precision * Recall / (Precision + Recall))</li>
                    <li><strong>SHD</strong>: Structural Hamming Distance - sum of false positives and false negatives</li>
                    <li><strong>True Positives (TP)</strong>: Number of correctly discovered edges</li>
                    <li><strong>False Positives (FP)</strong>: Number of incorrectly discovered edges</li>
                    <li><strong>False Negatives (FN)</strong>: Number of true edges that were not discovered</li>
                </ul>
            </div>
            
            <h2>Causal Graph Visualizations</h2>
            <p>The following images show the causal graphs discovered by each method compared to the ground truth.</p>
    """
    
    # Add images for each method
    for method in results.keys():
        html += f"""
            <h3>{method} Causal Graph</h3>
            <img src="{method}_causal_graph.png" alt="{method} Causal Graph" style="max-width: 100%;">
        """
    
    # Add ground truth graph
    html += """
            <h3>Ground Truth Causal Graph</h3>
            <img src="ground_truth_causal_graph.png" alt="Ground Truth Causal Graph" style="max-width: 100%;">
            
            <div class="info-box">
                <h3>About the Tennessee Eastman Process</h3>
                <p>The Tennessee Eastman Process (TEP) is a realistic chemical process simulation widely used as a benchmark for process control and monitoring. It consists of five major units: a reactor, a condenser, a separator, a stripper, and a compressor.</p>
                <p>The process involves eight components: A, B, C, D, E, F, G, and H. Components A, B, C, D, and E are reactants, while G and H are products. Component F is an inert substance.</p>
                <p>The causal relationships in TEP are complex and involve multiple feedback loops, making it a challenging benchmark for causal discovery algorithms.</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # Write HTML to file
    with open(output_path, "w") as f:
        f.write(html)
    
    print(f"HTML report generated: {output_path}")


def main():
    """Main function."""
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"Validating TEP causal structure with data from: {args.dataset_path}")
    
    # Load data
    env = TEPEnv(data=args.dataset_path, seed=args.seed)
    data = env.data
    
    # Get ground truth edges
    ground_truth_edges = get_ground_truth_edges()
    print(f"Ground truth has {len(ground_truth_edges)} edges")
    
    # Plot ground truth graph
    plot_causal_graph(
        ground_truth_edges,
        "Ground Truth Causal Graph",
        os.path.join(args.output_dir, "ground_truth_causal_graph.png"),
        var_names=TEP_VARIABLES
    )
    
    # Test each method
    methods = args.methods.split(",")
    results = {}
    
    for method in methods:
        print(f"Testing method: {method}")
        
        try:
            # Discover causal graph
            discovered_edges = discover_causal_graph(
                data,
                method=method,
                alpha=0.1,
                sparsity_factor=0.1,
                num_samples=args.num_samples
            )
            
            print(f"Discovered {len(discovered_edges)} edges")
            
            # Plot discovered graph
            plot_causal_graph(
                discovered_edges,
                f"{method.upper()} Causal Graph",
                os.path.join(args.output_dir, f"{method}_causal_graph.png"),
                var_names=TEP_VARIABLES
            )
            
            # Evaluate discovered graph
            metrics = evaluate_causal_graph(discovered_edges, ground_truth_edges)
            results[method] = metrics
            
            print(f"Precision: {metrics['precision']:.4f}, Recall: {metrics['recall']:.4f}, F1: {metrics['f1_score']:.4f}")
            
        except Exception as e:
            print(f"Error testing method {method}: {e}")
            import traceback
            traceback.print_exc()
    
    # Save results as JSON
    with open(os.path.join(args.output_dir, "validation_results.json"), "w") as f:
        json.dump(results, f, indent=2)
    
    # Create HTML report
    create_html_report(
        results,
        os.path.join(args.output_dir, "validation_report.html")
    )
    
    print(f"Validation completed. Results saved to {args.output_dir}")


if __name__ == "__main__":
    main()
