"""
Visualization tools for metrics in TCI.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union, Any


def plot_training_metrics(
    metrics: Dict[str, List[float]],
    figsize: Tuple[int, int] = (15, 10),
    save_path: Optional[str] = None
) -> plt.Figure:
    """
    Plot training metrics.
    
    Parameters
    ----------
    metrics : Dict[str, List[float]]
        Dictionary of training metrics
    figsize : Tuple[int, int], optional
        Figure size, by default (15, 10)
    save_path : Optional[str], optional
        Path to save the plot, by default None
    
    Returns
    -------
    plt.Figure
        Matplotlib figure
    """
    fig = plt.figure(figsize=figsize)
    
    # Plot episode rewards
    plt.subplot(2, 2, 1)
    plt.plot(metrics['episode_rewards'], label='Training')
    if 'eval_rewards' in metrics and metrics['eval_rewards']:
        # Plot evaluation rewards at the correct x-coordinates
        eval_episodes = np.arange(0, len(metrics['episode_rewards']), 
                                 len(metrics['episode_rewards']) // len(metrics['eval_rewards']))
        if len(eval_episodes) > len(metrics['eval_rewards']):
            eval_episodes = eval_episodes[:len(metrics['eval_rewards'])]
        plt.plot(eval_episodes, metrics['eval_rewards'], 'r-', label='Evaluation')
    plt.xlabel('Episode')
    plt.ylabel('Reward')
    plt.title('Episode Rewards')
    plt.legend()
    plt.grid(True)
    
    # Plot episode lengths
    plt.subplot(2, 2, 2)
    plt.plot(metrics['episode_lengths'])
    plt.xlabel('Episode')
    plt.ylabel('Length')
    plt.title('Episode Lengths')
    plt.grid(True)
    
    # Plot losses
    plt.subplot(2, 2, 3)
    plt.plot(metrics['losses'])
    plt.xlabel('Episode')
    plt.ylabel('Loss')
    plt.title('Training Loss')
    plt.grid(True)
    
    # Plot training time
    plt.subplot(2, 2, 4)
    plt.plot(metrics['times'])
    plt.xlabel('Episode')
    plt.ylabel('Time (s)')
    plt.title('Cumulative Training Time')
    plt.grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig


def plot_comparison(
    results: Dict[str, Dict[str, List[float]]],
    metric: str = 'eval_rewards',
    title: str = 'Algorithm Comparison',
    figsize: Tuple[int, int] = (12, 8),
    save_path: Optional[str] = None
) -> plt.Figure:
    """
    Plot a comparison of multiple algorithms.
    
    Parameters
    ----------
    results : Dict[str, Dict[str, List[float]]]
        Dictionary mapping algorithm names to metrics
    metric : str, optional
        Metric to plot, by default 'eval_rewards'
    title : str, optional
        Plot title, by default 'Algorithm Comparison'
    figsize : Tuple[int, int], optional
        Figure size, by default (12, 8)
    save_path : Optional[str], optional
        Path to save the plot, by default None
    
    Returns
    -------
    plt.Figure
        Matplotlib figure
    """
    try:
        import seaborn as sns
    except ImportError:
        raise ImportError("Seaborn is required for comparison plots. "
                         "Install with: pip install seaborn")
    
    # Create figure
    fig, ax = plt.subplots(figsize=figsize)
    
    # Set seaborn style
    sns.set(style="whitegrid")
    
    # Create a color palette
    colors = sns.color_palette("husl", len(results))
    
    # Plot metric for each algorithm
    for i, (name, metrics) in enumerate(results.items()):
        if metric in metrics:
            values = np.mean(metrics[metric], axis=0) if isinstance(metrics[metric][0], list) else metrics[metric]
            
            # Calculate standard error if multiple runs
            if isinstance(metrics[metric][0], list) and len(metrics[metric]) > 1:
                std = np.std(metrics[metric], axis=0)
                se = std / np.sqrt(len(metrics[metric]))
                
                # Plot with confidence interval
                x = np.arange(len(values))
                ax.fill_between(x, values - se, values + se, alpha=0.2, color=colors[i])
            
            ax.plot(values, label=name, color=colors[i], linewidth=2)
    
    ax.set_xlabel('Evaluation')
    ax.set_ylabel(metric.replace('_', ' ').title())
    ax.set_title(title)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig


def plot_ablation_study(
    results: Dict[str, Dict[str, List[float]]],
    baseline: str = 'TCI-Base',
    metric: str = 'eval_rewards',
    use_final_value: bool = True,
    title: str = 'Ablation Study',
    figsize: Tuple[int, int] = (12, 8),
    save_path: Optional[str] = None
) -> plt.Figure:
    """
    Plot an ablation study comparing variants against a baseline.
    
    Parameters
    ----------
    results : Dict[str, Dict[str, List[float]]]
        Dictionary mapping algorithm names to metrics
    baseline : str, optional
        Name of the baseline algorithm, by default 'TCI-Base'
    metric : str, optional
        Metric to plot, by default 'eval_rewards'
    use_final_value : bool, optional
        Whether to use the final value of the metric, by default True
    title : str, optional
        Plot title, by default 'Ablation Study'
    figsize : Tuple[int, int], optional
        Figure size, by default (12, 8)
    save_path : Optional[str], optional
        Path to save the plot, by default None
    
    Returns
    -------
    plt.Figure
        Matplotlib figure
    """
    try:
        import seaborn as sns
    except ImportError:
        raise ImportError("Seaborn is required for ablation study plots. "
                         "Install with: pip install seaborn")
    
    # Create figure
    fig, ax = plt.subplots(figsize=figsize)
    
    # Set seaborn style
    sns.set(style="whitegrid")
    
    # Extract baseline value
    if baseline not in results:
        raise ValueError(f"Baseline '{baseline}' not found in results")
    
    if metric not in results[baseline]:
        raise ValueError(f"Metric '{metric}' not found in baseline results")
    
    if use_final_value:
        baseline_values = [run[-1] for run in results[baseline][metric]]
        baseline_value = np.mean(baseline_values)
    else:
        baseline_values = results[baseline][metric]
        baseline_value = np.mean(baseline_values)
    
    # Calculate improvement over baseline
    improvements = {}
    std_errors = {}
    
    for name, metrics in results.items():
        if name == baseline:
            continue
        
        if metric not in metrics:
            continue
        
        if use_final_value:
            values = [run[-1] for run in metrics[metric]]
        else:
            values = metrics[metric]
        
        improvement = np.mean(values) - baseline_value
        improvements[name] = improvement
        
        # Calculate standard error
        if len(values) > 1:
            std_errors[name] = np.std(values) / np.sqrt(len(values))
        else:
            std_errors[name] = 0
    
    # Sort by improvement
    sorted_names = sorted(improvements.keys(), key=lambda x: improvements[x], reverse=True)
    sorted_improvements = [improvements[name] for name in sorted_names]
    sorted_std_errors = [std_errors[name] for name in sorted_names]
    
    # Create color palette
    colors = sns.color_palette("husl", len(sorted_names))
    
    # Plot bar chart
    bars = ax.bar(sorted_names, sorted_improvements, yerr=sorted_std_errors,
                 capsize=5, color=colors)
    
    ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    ax.set_xlabel('Variant')
    ax.set_ylabel(f'Improvement over {baseline}')
    ax.set_title(title)
    ax.set_xticklabels(sorted_names, rotation=45, ha='right')
    ax.grid(True, axis='y', alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig
