<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCI Extensions - TCI Framework Documentation</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 20px 0;
            overflow-x: hidden;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
            padding: 0.5rem 1rem;
        }
        .sidebar .nav-link.active {
            color: #007bff;
        }
        .sidebar .nav-link:hover {
            color: #0056b3;
        }
        .main-content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid #eaecef;
            padding-bottom: 0.3em;
        }
        h2 {
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
            color: #e83e8c;
        }
        pre code {
            color: #333;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .table {
            margin-bottom: 2rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .footer {
            padding: 20px 0;
            margin-top: 40px;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            text-align: center;
        }
        .toc-link {
            display: block;
            padding: 0.25rem 1.5rem;
            color: #495057;
            text-decoration: none;
        }
        .toc-link:hover {
            color: #007bff;
            text-decoration: none;
        }
        .toc-link.active {
            color: #007bff;
            font-weight: 600;
        }
        .toc-h2 {
            padding-left: 1rem;
        }
        .toc-h3 {
            padding-left: 2rem;
        }
        .toc-h4 {
            padding-left: 3rem;
        }
        .alert-info {
            background-color: #e3f2fd;
            border-color: #b3e5fc;
            color: #0c5460;
        }
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
        }
        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .extension-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">TCI Framework</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="getting_started.html">Getting Started</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="core_concepts.html">Core Concepts</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="extensions.html">Extensions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="api_reference.html">API Reference</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky">
                    <h5 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Contents</span>
                    </h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#introduction">
                                Introduction
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#extension-types">
                                Extension Types
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#gat-extension">
                                GAT Extension
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#sparse-causal-extension">
                                Sparse Causal Extension
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#ensemble-extension">
                                Ensemble Extension
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#variable-history-extension">
                                Variable History Extension
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#maml-extension">
                                MAML Extension
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#discovery-mechanism">
                                Extension Discovery
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#combining-extensions">
                                Combining Extensions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#custom-extensions">
                                Creating Custom Extensions
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="section" id="introduction">
                    <h1>TCI Extensions</h1>
                    <p class="lead">
                        Enhance the capabilities of the TCI framework with specialized extensions.
                    </p>
                    <div class="alert alert-info">
                        <strong>Note:</strong> Extensions can be used individually or combined to address specific challenges in causal reinforcement learning.
                    </div>
                    <p>
                        The TCI framework is designed to be modular and extensible, allowing you to enhance its capabilities with specialized extensions. These extensions address various limitations of the base framework and provide additional functionality for specific use cases.
                    </p>
                    <p>
                        This page provides an overview of the available extensions and how to use them. For detailed information about each extension, follow the links to their dedicated documentation pages.
                    </p>
                </div>

                <div class="section" id="extension-types">
                    <h2>Extension Types</h2>
                    <p>
                        TCI extensions can be categorized into several types based on their functionality:
                    </p>
                    <div class="row">
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="extension-icon">🧠</div>
                                    <h5 class="card-title">Representation Learning</h5>
                                    <p class="card-text">
                                        Extensions that improve how TCI represents and processes state information.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="extension-icon">🔍</div>
                                    <h5 class="card-title">Causal Discovery</h5>
                                    <p class="card-text">
                                        Extensions that enhance TCI's ability to discover causal relationships.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="extension-icon">⏱️</div>
                                    <h5 class="card-title">Temporal Modeling</h5>
                                    <p class="card-text">
                                        Extensions that improve how TCI handles temporal dynamics.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="extension-icon">🤝</div>
                                    <h5 class="card-title">Ensemble Methods</h5>
                                    <p class="card-text">
                                        Extensions that combine multiple models for improved robustness.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="gat-extension">
                    <h2>Graph Attention Networks (GAT) Extension</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <p>
                                The GAT extension enhances TCI's ability to model complex causal relationships by using attention mechanisms to focus on the most relevant variables in the causal graph. This improves both the accuracy of causal discovery and the quality of decision-making.
                            </p>
                            <h4>Key Features:</h4>
                            <ul>
                                <li>Attention-based processing of causal graphs</li>
                                <li>Improved handling of complex causal structures</li>
                                <li>Enhanced interpretability through attention weights</li>
                                <li>Better performance in environments with many variables</li>
                            </ul>
                            <p>
                                <a href="extensions/gat.html" class="btn btn-primary">Learn More</a>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    Quick Example
                                </div>
                                <div class="card-body">
                                    <pre><code class="python">from tci.extensions import GAT

# Create GAT extension
gat = GAT(
    hidden_dim=64,
    output_dim=32,
    num_heads=4
)

# Add to TCI agent
agent = FixedTCIAgent(
    state_dim=state_dim,
    action_dim=action_dim,
    extensions=[gat],
    device=device
)</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="sparse-causal-extension">
                    <h2>Sparse Causal Discovery Extension</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <p>
                                The Sparse Causal Discovery extension enforces sparsity in the learned causal graphs, making them more interpretable and computationally efficient. It uses L1 regularization and thresholding techniques to identify the most important causal relationships.
                            </p>
                            <h4>Key Features:</h4>
                            <ul>
                                <li>Reduced computational complexity</li>
                                <li>Improved interpretability of causal graphs</li>
                                <li>Better generalization to unseen data</li>
                                <li>Configurable sparsity level</li>
                            </ul>
                            <p>
                                <a href="extensions/sparse_causal.html" class="btn btn-primary">Learn More</a>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    Quick Example
                                </div>
                                <div class="card-body">
                                    <pre><code class="python">from tci.extensions import SparseCausal

# Create SparseCausal extension
sparse_causal = SparseCausal(
    sparsity_factor=0.1,
    l1_reg=0.01,
    threshold=0.05
)

# Add to TCI agent
agent = FixedTCIAgent(
    state_dim=state_dim,
    action_dim=action_dim,
    extensions=[sparse_causal],
    device=device
)</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="ensemble-extension">
                    <h2>Ensemble Extension</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <p>
                                The Ensemble extension combines multiple causal models to improve robustness and accuracy. It uses techniques like bagging and boosting to create diverse models and aggregate their predictions.
                            </p>
                            <h4>Key Features:</h4>
                            <ul>
                                <li>Improved robustness to noise</li>
                                <li>Better uncertainty estimation</li>
                                <li>Higher accuracy in causal discovery</li>
                                <li>Configurable ensemble size and aggregation method</li>
                            </ul>
                            <p>
                                <a href="extensions/ensemble.html" class="btn btn-primary">Learn More</a>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    Quick Example
                                </div>
                                <div class="card-body">
                                    <pre><code class="python">from tci.extensions import Ensemble

# Create Ensemble extension
ensemble = Ensemble(
    ensemble_size=5,
    aggregation_method='mean',
    bootstrap=True
)

# Add to TCI agent
agent = FixedTCIAgent(
    state_dim=state_dim,
    action_dim=action_dim,
    extensions=[ensemble],
    device=device
)</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="variable-history-extension">
                    <h2>Variable History Length Extension</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <p>
                                The Variable History Length extension dynamically adjusts the amount of historical data used for causal discovery based on the complexity of the environment. It uses attention mechanisms to focus on the most relevant time periods.
                            </p>
                            <h4>Key Features:</h4>
                            <ul>
                                <li>Better handling of temporal dependencies</li>
                                <li>Improved computational efficiency</li>
                                <li>Adaptive memory usage</li>
                                <li>Configurable maximum history length</li>
                            </ul>
                            <p>
                                <a href="extensions/variable_history.html" class="btn btn-primary">Learn More</a>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    Quick Example
                                </div>
                                <div class="card-body">
                                    <pre><code class="python">from tci.extensions import VariableHistory

# Create VariableHistory extension
variable_history = VariableHistory(
    max_history_length=10,
    min_history_length=2,
    attention_dim=32
)

# Add to TCI agent
agent = FixedTCIAgent(
    state_dim=state_dim,
    action_dim=action_dim,
    extensions=[variable_history],
    device=device
)</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="maml-extension">
                    <h2>Model-Agnostic Meta-Learning (MAML) Extension</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <p>
                                The MAML extension enhances the TCI framework's ability to quickly adapt to new tasks or environments with minimal data. It implements Model-Agnostic Meta-Learning to learn a good initialization for model parameters, enabling rapid adaptation to new tasks with just a few examples.
                            </p>
                            <h4>Key Features:</h4>
                            <ul>
                                <li>Fast adaptation to new environments</li>
                                <li>Improved sample efficiency</li>
                                <li>Better transfer learning capabilities</li>
                                <li>Configurable meta-learning parameters</li>
                            </ul>
                            <p>
                                <a href="extensions/maml.html" class="btn btn-primary">Learn More</a>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    Quick Example
                                </div>
                                <div class="card-body">
                                    <pre><code class="python">from tci.extensions import MAML

# Create MAML extension
maml = MAML(
    meta_lr=0.001,
    inner_lr=0.01,
    num_inner_steps=5,
    task_batch_size=4
)

# Add to TCI agent
agent = FixedTCIAgent(
    state_dim=state_dim,
    action_dim=action_dim,
    extensions=[maml],
    device=device
)</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="discovery-mechanism">
                    <h2>Extension Discovery Mechanism</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <p>
                                The Extension Discovery mechanism provides a way to dynamically discover, load, and use extensions in the TCI framework. This makes it easier to extend the framework with custom extensions without modifying the core code.
                            </p>
                            <h4>Key Features:</h4>
                            <ul>
                                <li>Automatic discovery of available extensions</li>
                                <li>Dynamic loading of extensions by name</li>
                                <li>Introspection of extension parameters</li>
                                <li>Support for custom extension packages</li>
                            </ul>
                            <p>
                                <a href="extensions/discovery.html" class="btn btn-primary">Learn More</a>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    Quick Example
                                </div>
                                <div class="card-body">
                                    <pre><code class="python">from tci.extensions.discovery import load_extension, list_extensions

# List available extensions
extension_names = list_extensions()
print(f"Available extensions: {extension_names}")

# Load extensions dynamically
gat = load_extension('gat', hidden_dim=32)
ensemble = load_extension('ensemble', ensemble_size=5)

# Create TCI agent with loaded extensions
agent = FixedTCIAgent(
    state_dim=state_dim,
    action_dim=action_dim,
    extensions=[gat, ensemble],
    device=device
)</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section" id="combining-extensions">
                    <h2>Combining Extensions</h2>
                    <p>
                        TCI extensions can be combined to address multiple challenges simultaneously. When combining extensions, it's important to consider their order and potential interactions.
                    </p>
                    <div class="alert alert-info">
                        <strong>Note:</strong> The order of extensions matters! Extensions are applied in the order they are provided to the agent.
                    </div>
                    <div class="card mb-4">
                        <div class="card-header">
                            Example: Combining Multiple Extensions
                        </div>
                        <div class="card-body">
                            <pre><code class="python">from tci.core.agent_fix import FixedTCIAgent
from tci.extensions import GAT, SparseCausal, Ensemble, VariableHistory

# Create extensions
gat = GAT(hidden_dim=64, output_dim=32)
sparse_causal = SparseCausal(sparsity_factor=0.1)
ensemble = Ensemble(ensemble_size=3)
variable_history = VariableHistory(max_history_length=10)

# Create TCI agent with multiple extensions
# Note: Order matters - GAT should be first, ensemble last
agent = FixedTCIAgent(
    state_dim=state_dim,
    action_dim=action_dim,
    device=device,
    extensions=[gat, variable_history, sparse_causal, ensemble],
    hidden_dim=64,
    learning_rate=0.001
)</code></pre>
                        </div>
                    </div>
                    <h4>Recommended Extension Combinations:</h4>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Scenario</th>
                                    <th>Recommended Extensions</th>
                                    <th>Benefits</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>High-dimensional state spaces</td>
                                    <td>GAT + SparseCausal</td>
                                    <td>Improved computational efficiency and better handling of complex causal structures</td>
                                </tr>
                                <tr>
                                    <td>Noisy environments</td>
                                    <td>Ensemble + SparseCausal</td>
                                    <td>Increased robustness to noise and more interpretable causal graphs</td>
                                </tr>
                                <tr>
                                    <td>Complex temporal dynamics</td>
                                    <td>VariableHistory + GAT</td>
                                    <td>Better handling of temporal dependencies and improved causal discovery</td>
                                </tr>
                                <tr>
                                    <td>Maximum performance</td>
                                    <td>All extensions</td>
                                    <td>Comprehensive enhancement of all aspects of TCI</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="section" id="custom-extensions">
                    <h2>Creating Custom Extensions</h2>
                    <p>
                        You can create your own custom extensions to address specific challenges or add new functionality to the TCI framework. Custom extensions should implement the following interface:
                    </p>
                    <div class="card mb-4">
                        <div class="card-header">
                            Extension Interface
                        </div>
                        <div class="card-body">
                            <pre><code class="python">class CustomExtension:
    def __init__(self, **kwargs):
        """
        Initialize the extension with custom parameters.
        """
        # Store parameters
        self.param1 = kwargs.get('param1', default_value)
        self.param2 = kwargs.get('param2', default_value)

    def initialize(self, agent):
        """
        Initialize the extension with the agent.
        This method is called when the extension is added to the agent.

        Parameters
        ----------
        agent : FixedTCIAgent
            The TCI agent that this extension is attached to
        """
        # Store agent parameters
        self.state_dim = agent.state_dim
        self.action_dim = agent.action_dim
        self.device = agent.device

        # Initialize extension-specific components
        # ...

    def __call__(self, *args, **kwargs):
        """
        Process input data.
        This method is called during the forward pass of the agent.
        """
        # Process input data
        # ...

        return processed_data</code></pre>
                        </div>
                    </div>
                    <p>
                        For more information on creating custom extensions, see the <a href="extensions/custom.html">Custom Extensions</a> guide.
                    </p>
                </div>

                <footer class="footer">
                    <div class="container">
                        <span class="text-muted">TCI Framework Documentation &copy; 2023</span>
                    </div>
                </footer>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script>
        hljs.highlightAll();

        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Update active link on scroll
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav-link');

            let currentSection = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop && pageYOffset < sectionTop + sectionHeight) {
                    currentSection = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${currentSection}`) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
