#!/usr/bin/env python3
"""
Simple Enhanced TCI-fix

A simplified version of the enhanced TCI-fix that focuses on the key improvements:
1. Bias correction
2. Uncertainty quantification  
3. Better handling of zero/low orders
4. Ensemble approach

This version builds on the existing TCI-fix without complex two-stage modeling.
"""

import numpy as np
import pandas as pd
import pickle
import os
import logging
from datetime import datetime, timedelta

# Import the original TCI-fix
from tci_fix import TCIFixPredictor

class SimplifiedEnhancedTCIFix:
    """
    Simplified enhanced TCI-fix with key improvements for better accuracy.
    
    Key improvements:
    - Bias correction based on recent performance
    - Uncertainty quantification
    - Better zero/low order handling
    - Ensemble approach with multiple methods
    """
    
    def __init__(self, 
                 bias_correction=True,
                 uncertainty_quantification=True,
                 ensemble_weights=None,
                 **kwargs):
        
        # Initialize base TCI-fix predictor
        self.base_predictor = TCIFixPredictor(**kwargs)
        
        # Configuration
        self.bias_correction = bias_correction
        self.uncertainty_quantification = uncertainty_quantification
        self.ensemble_weights = ensemble_weights or [0.6, 0.25, 0.15]  # TCI-fix, Trend, MA
        
        # Model state
        self.is_fitted = False
        self.bias_correction_factor = 1.0
        self.volatility_level = 'medium'
        self.zero_order_threshold = 100  # Threshold for considering an order as "real"
        
        # Performance tracking
        self.recent_errors = []
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def fit(self, data, external_data=None):
        """
        Fit the simplified enhanced model.
        
        Parameters:
        -----------
        data : pandas.DataFrame
            Historical time series data
        external_data : dict
            External data sources
            
        Returns:
        --------
        self
        """
        self.logger.info("Starting Simplified Enhanced TCI-fix training...")
        
        # Step 1: Fit base TCI-fix model
        self.logger.info("Step 1: Training base TCI-fix model...")
        self.base_predictor.fit(data, external_data)
        
        # Step 2: Analyze data characteristics
        self.logger.info("Step 2: Analyzing data characteristics...")
        self._analyze_data_characteristics(data)
        
        # Step 3: Calculate bias correction
        if self.bias_correction:
            self.logger.info("Step 3: Calculating bias correction...")
            self._calculate_bias_correction(data, external_data)
        
        self.is_fitted = True
        self.logger.info("Simplified Enhanced TCI-fix training complete!")
        
        return self
    
    def _analyze_data_characteristics(self, data):
        """Analyze data characteristics to determine enhancement strategies."""
        
        target_col = self.base_predictor.target_column
        
        # Calculate volatility
        cv = data[target_col].std() / (data[target_col].mean() + 1e-6)
        
        if cv > 1.0:
            self.volatility_level = 'high'
        elif cv > 0.5:
            self.volatility_level = 'medium'
        else:
            self.volatility_level = 'low'
        
        # Analyze zero/low orders
        zero_orders = (data[target_col] < self.zero_order_threshold).sum()
        zero_percentage = zero_orders / len(data) * 100
        
        self.logger.info(f"Data characteristics:")
        self.logger.info(f"  Volatility level: {self.volatility_level} (CV: {cv:.3f})")
        self.logger.info(f"  Zero/low orders: {zero_percentage:.1f}%")
        
        # Adjust ensemble weights based on volatility
        if self.volatility_level == 'high':
            # For high volatility, rely more on simple methods
            self.ensemble_weights = [0.5, 0.3, 0.2]  # Less weight on TCI-fix
        elif self.volatility_level == 'low':
            # For low volatility, trust TCI-fix more
            self.ensemble_weights = [0.7, 0.2, 0.1]  # More weight on TCI-fix
    
    def _calculate_bias_correction(self, data, external_data):
        """Calculate bias correction factor based on recent performance."""
        
        try:
            target_col = self.base_predictor.target_column
            
            # Use the last 20% of data for bias calculation
            split_point = int(len(data) * 0.8)
            train_data = data.iloc[:split_point].copy()
            test_data = data.iloc[split_point:].copy()
            
            if len(test_data) < 3:
                self.logger.warning("Insufficient data for bias correction")
                self.bias_correction_factor = 1.0
                return
            
            # Train on subset and predict on test
            temp_predictor = TCIFixPredictor()
            temp_predictor.fit(train_data, external_data)
            
            # Get predictions for test period
            test_predictions = []
            for _, row in test_data.iterrows():
                # Simple prediction using the trained model
                # This is a simplified approach
                pred = temp_predictor.model.predict(
                    temp_predictor.scaler_X.transform(
                        [[row[col] if col in row.index else 0 
                          for col in temp_predictor.feature_columns]]
                    )
                )[0]
                test_predictions.append(pred)
            
            # Calculate bias
            actual_values = test_data[target_col].values
            predicted_values = np.array(test_predictions)
            
            # Remove zero predictions for bias calculation
            non_zero_mask = predicted_values > 0
            if non_zero_mask.sum() > 0:
                actual_non_zero = actual_values[non_zero_mask]
                predicted_non_zero = predicted_values[non_zero_mask]
                
                self.bias_correction_factor = actual_non_zero.mean() / (predicted_non_zero.mean() + 1e-6)
            else:
                self.bias_correction_factor = 1.0
            
            # Limit bias correction to reasonable range
            self.bias_correction_factor = np.clip(self.bias_correction_factor, 0.5, 2.0)
            
            self.logger.info(f"Bias correction factor: {self.bias_correction_factor:.3f}")
            
        except Exception as e:
            self.logger.warning(f"Could not calculate bias correction: {e}")
            self.bias_correction_factor = 1.0
    
    def predict(self, start_date, end_date, data, external_data=None):
        """
        Generate enhanced predictions.
        
        Parameters:
        -----------
        start_date : str or datetime
            Start date for predictions
        end_date : str or datetime  
            End date for predictions
        data : pandas.DataFrame
            Historical data
        external_data : dict
            External data sources
            
        Returns:
        --------
        pandas.DataFrame
            Enhanced predictions with uncertainty bounds
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        self.logger.info("Generating simplified enhanced predictions...")
        
        # Step 1: Get base TCI-fix predictions
        base_predictions = self.base_predictor.predict(start_date, end_date, data, external_data)
        
        if base_predictions is None:
            return None
        
        # Step 2: Apply bias correction
        enhanced_predictions = base_predictions.copy()
        if self.bias_correction:
            enhanced_predictions['predicted_quantity'] *= self.bias_correction_factor
        
        # Step 3: Apply ensemble approach
        enhanced_predictions = self._apply_ensemble_approach(enhanced_predictions, data)
        
        # Step 4: Add uncertainty quantification
        if self.uncertainty_quantification:
            enhanced_predictions = self._add_uncertainty_bounds(enhanced_predictions)
        
        # Step 5: Apply zero/low order logic
        enhanced_predictions = self._apply_zero_order_logic(enhanced_predictions, data)
        
        # Add metadata
        enhanced_predictions['model_type'] = 'simplified_enhanced_tci_fix'
        enhanced_predictions['bias_correction_factor'] = self.bias_correction_factor
        enhanced_predictions['volatility_level'] = self.volatility_level
        
        return enhanced_predictions
    
    def _apply_ensemble_approach(self, predictions, data):
        """Apply ensemble approach combining multiple methods."""
        
        predictions = predictions.copy()
        target_col = self.base_predictor.target_column
        
        # Component 1: TCI-fix prediction (already in predictions)
        tci_fix_pred = predictions['predicted_quantity'].copy()
        
        # Component 2: Trend-based prediction
        recent_data = data[target_col].tail(6)  # Last 6 months
        if len(recent_data) > 1:
            # Simple linear trend
            x = np.arange(len(recent_data))
            trend_coef = np.polyfit(x, recent_data, 1)[0]
            last_value = recent_data.iloc[-1]
            
            trend_predictions = []
            for i in range(len(predictions)):
                trend_pred = last_value + trend_coef * (i + 1)
                trend_predictions.append(max(0, trend_pred))  # Ensure non-negative
            
            trend_pred = np.array(trend_predictions)
        else:
            trend_pred = tci_fix_pred.copy()
        
        # Component 3: Moving average prediction
        ma_pred = np.full(len(predictions), recent_data.mean())
        
        # Weighted ensemble
        tci_weight, trend_weight, ma_weight = self.ensemble_weights
        
        ensemble_prediction = (
            tci_weight * tci_fix_pred +
            trend_weight * trend_pred +
            ma_weight * ma_pred
        )
        
        predictions['predicted_quantity'] = ensemble_prediction
        
        # Store components for analysis
        predictions['tci_fix_component'] = tci_fix_pred
        predictions['trend_component'] = trend_pred
        predictions['ma_component'] = ma_pred
        
        return predictions
    
    def _add_uncertainty_bounds(self, predictions):
        """Add uncertainty bounds based on volatility level."""
        
        predictions = predictions.copy()
        
        # Uncertainty factors based on volatility
        uncertainty_factors = {
            'low': 0.15,      # ±15%
            'medium': 0.25,   # ±25%
            'high': 0.40      # ±40%
        }
        
        uncertainty_factor = uncertainty_factors.get(self.volatility_level, 0.25)
        
        # Calculate bounds
        predictions['lower_bound'] = predictions['predicted_quantity'] * (1 - uncertainty_factor)
        predictions['upper_bound'] = predictions['predicted_quantity'] * (1 + uncertainty_factor)
        predictions['confidence_level'] = 1 - uncertainty_factor
        predictions['uncertainty_factor'] = uncertainty_factor
        
        # Ensure non-negative bounds
        predictions['lower_bound'] = np.maximum(predictions['lower_bound'], 0)
        
        return predictions
    
    def _apply_zero_order_logic(self, predictions, data):
        """Apply logic for handling zero/low order predictions."""
        
        predictions = predictions.copy()
        target_col = self.base_predictor.target_column
        
        # Check recent order pattern
        recent_orders = data[target_col].tail(6)
        recent_non_zero = (recent_orders >= self.zero_order_threshold).sum()
        
        # If most recent orders are zero/low, be more conservative
        if recent_non_zero <= 2:  # Less than half have real orders
            # Reduce predictions and increase uncertainty
            predictions['predicted_quantity'] *= 0.7
            if 'uncertainty_factor' in predictions.columns:
                predictions['uncertainty_factor'] *= 1.2
                predictions['lower_bound'] = predictions['predicted_quantity'] * (1 - predictions['uncertainty_factor'])
                predictions['upper_bound'] = predictions['predicted_quantity'] * (1 + predictions['uncertainty_factor'])
        
        return predictions
    
    def save(self, filepath):
        """Save the enhanced model."""
        model_data = {
            'base_predictor': self.base_predictor,
            'bias_correction_factor': self.bias_correction_factor,
            'volatility_level': self.volatility_level,
            'ensemble_weights': self.ensemble_weights,
            'zero_order_threshold': self.zero_order_threshold,
            'is_fitted': self.is_fitted
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
    
    @classmethod
    def load(cls, filepath):
        """Load the enhanced model."""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        # Create instance
        instance = cls()
        
        # Restore state
        for key, value in model_data.items():
            setattr(instance, key, value)
        
        return instance
