#!/bin/bash
# GPU Acceleration Setup for TCI-fix
# This script installs NVIDIA drivers, CUDA, and GPU libraries

echo "🚀 GPU Acceleration Setup for TCI-fix"
echo "====================================="

echo "📋 System Information:"
lspci | grep -i nvidia
echo ""

echo "⚠️ IMPORTANT: This script requires sudo privileges"
echo "Press Enter to continue or Ctrl+C to cancel"
read

echo "📦 Step 1: Updating system..."
sudo apt update && sudo apt upgrade -y

echo "🔧 Step 2: Installing NVIDIA drivers..."
sudo apt install -y nvidia-driver-535 nvidia-dkms-535

echo "📡 Step 3: Adding CUDA repository..."
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.0-1_all.deb
sudo dpkg -i cuda-keyring_1.0-1_all.deb
sudo apt-get update

echo "⚡ Step 4: Installing CUDA toolkit..."
sudo apt-get -y install cuda-toolkit-12-4

echo "🔧 Step 5: Setting environment variables..."
echo "export CUDA_HOME=/usr/local/cuda" >> ~/.bashrc
echo "export PATH=\/home/<USER>/Project/Historical_Customer_Order_Predictor/.venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:\/bin" >> ~/.bashrc
echo "export LD_LIBRARY_PATH=\/opt/MVS/lib/64:/opt/MVS/lib/32:/opt/MVS/lib/64:/opt/MVS/lib/32::\/lib64" >> ~/.bashrc

echo "📚 Step 6: Installing GPU Python libraries..."
source .venv/bin/activate
pip install cupy-cuda12x numba

echo "✅ Installation complete!"
echo ""
echo "🔄 REBOOT REQUIRED: Please reboot your system to complete installation"
echo "After reboot, verify with: nvidia-smi"
echo ""
echo "🎯 Expected TCI-fix speedup: 10-100x faster training!"
