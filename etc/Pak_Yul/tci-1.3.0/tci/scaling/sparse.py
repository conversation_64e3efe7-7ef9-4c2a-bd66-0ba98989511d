"""
Sparse computation techniques for TCI.

This module provides sparse computation techniques for large causal graphs,
enabling efficient representation and manipulation of high-dimensional causal models.
"""

import numpy as np
import scipy.sparse as sp
import networkx as nx
from typing import Dict, List, Tuple, Optional, Union, Set, Any
import logging
import warnings

logger = logging.getLogger(__name__)


class SparseAdjacencyMatrix:
    """
    Sparse adjacency matrix for causal graphs.
    
    This class provides a sparse representation of adjacency matrices for causal graphs,
    enabling efficient storage and manipulation of large causal graphs.
    
    Parameters
    ----------
    n_nodes : int
        Number of nodes in the graph
    dtype : np.dtype, optional
        Data type of the matrix, by default np.float32
    format : str, optional
        Sparse matrix format, by default 'csr'
    """
    
    def __init__(
        self,
        n_nodes: int,
        dtype: np.dtype = np.float32,
        format: str = 'csr'
    ):
        self.n_nodes = n_nodes
        self.dtype = dtype
        self.format = format
        
        # Initialize empty sparse matrix
        if format == 'csr':
            self.matrix = sp.csr_matrix((n_nodes, n_nodes), dtype=dtype)
        elif format == 'csc':
            self.matrix = sp.csc_matrix((n_nodes, n_nodes), dtype=dtype)
        elif format == 'coo':
            self.matrix = sp.coo_matrix((n_nodes, n_nodes), dtype=dtype)
        elif format == 'lil':
            self.matrix = sp.lil_matrix((n_nodes, n_nodes), dtype=dtype)
        else:
            raise ValueError(f"Unsupported sparse matrix format: {format}")
    
    def add_edge(self, source: int, target: int, weight: float = 1.0):
        """
        Add an edge to the adjacency matrix.
        
        Parameters
        ----------
        source : int
            Source node index
        target : int
            Target node index
        weight : float, optional
            Edge weight, by default 1.0
        """
        if source < 0 or source >= self.n_nodes:
            raise ValueError(f"Source node index {source} out of range [0, {self.n_nodes-1}]")
        if target < 0 or target >= self.n_nodes:
            raise ValueError(f"Target node index {target} out of range [0, {self.n_nodes-1}]")
        
        # Convert to LIL format for efficient single element assignment
        if self.format != 'lil':
            matrix_lil = self.matrix.tolil()
            matrix_lil[source, target] = weight
            self.matrix = matrix_lil.asformat(self.format)
        else:
            self.matrix[source, target] = weight
    
    def remove_edge(self, source: int, target: int):
        """
        Remove an edge from the adjacency matrix.
        
        Parameters
        ----------
        source : int
            Source node index
        target : int
            Target node index
        """
        if source < 0 or source >= self.n_nodes:
            raise ValueError(f"Source node index {source} out of range [0, {self.n_nodes-1}]")
        if target < 0 or target >= self.n_nodes:
            raise ValueError(f"Target node index {target} out of range [0, {self.n_nodes-1}]")
        
        # Convert to LIL format for efficient single element assignment
        if self.format != 'lil':
            matrix_lil = self.matrix.tolil()
            matrix_lil[source, target] = 0
            self.matrix = matrix_lil.asformat(self.format)
        else:
            self.matrix[source, target] = 0
    
    def get_edge_weight(self, source: int, target: int) -> float:
        """
        Get the weight of an edge.
        
        Parameters
        ----------
        source : int
            Source node index
        target : int
            Target node index
            
        Returns
        -------
        float
            Edge weight
        """
        if source < 0 or source >= self.n_nodes:
            raise ValueError(f"Source node index {source} out of range [0, {self.n_nodes-1}]")
        if target < 0 or target >= self.n_nodes:
            raise ValueError(f"Target node index {target} out of range [0, {self.n_nodes-1}]")
        
        return self.matrix[source, target]
    
    def has_edge(self, source: int, target: int) -> bool:
        """
        Check if an edge exists.
        
        Parameters
        ----------
        source : int
            Source node index
        target : int
            Target node index
            
        Returns
        -------
        bool
            True if the edge exists, False otherwise
        """
        if source < 0 or source >= self.n_nodes:
            raise ValueError(f"Source node index {source} out of range [0, {self.n_nodes-1}]")
        if target < 0 or target >= self.n_nodes:
            raise ValueError(f"Target node index {target} out of range [0, {self.n_nodes-1}]")
        
        return self.matrix[source, target] != 0
    
    def get_children(self, node: int) -> List[int]:
        """
        Get the children of a node.
        
        Parameters
        ----------
        node : int
            Node index
            
        Returns
        -------
        List[int]
            List of child node indices
        """
        if node < 0 or node >= self.n_nodes:
            raise ValueError(f"Node index {node} out of range [0, {self.n_nodes-1}]")
        
        # Get the row corresponding to the node
        row = self.matrix.getrow(node)
        
        # Get the indices of non-zero elements
        children = row.nonzero()[1].tolist()
        
        return children
    
    def get_parents(self, node: int) -> List[int]:
        """
        Get the parents of a node.
        
        Parameters
        ----------
        node : int
            Node index
            
        Returns
        -------
        List[int]
            List of parent node indices
        """
        if node < 0 or node >= self.n_nodes:
            raise ValueError(f"Node index {node} out of range [0, {self.n_nodes-1}]")
        
        # Get the column corresponding to the node
        col = self.matrix.getcol(node)
        
        # Get the indices of non-zero elements
        parents = col.nonzero()[0].tolist()
        
        return parents
    
    def get_neighbors(self, node: int) -> List[int]:
        """
        Get the neighbors of a node.
        
        Parameters
        ----------
        node : int
            Node index
            
        Returns
        -------
        List[int]
            List of neighbor node indices
        """
        if node < 0 or node >= self.n_nodes:
            raise ValueError(f"Node index {node} out of range [0, {self.n_nodes-1}]")
        
        # Get the row and column corresponding to the node
        row = self.matrix.getrow(node)
        col = self.matrix.getcol(node)
        
        # Get the indices of non-zero elements
        children = row.nonzero()[1].tolist()
        parents = col.nonzero()[0].tolist()
        
        # Combine and remove duplicates
        neighbors = list(set(children + parents))
        
        return neighbors
    
    def get_adjacency_list(self) -> Dict[int, List[int]]:
        """
        Get the adjacency list representation of the graph.
        
        Returns
        -------
        Dict[int, List[int]]
            Dictionary mapping node indices to lists of adjacent node indices
        """
        adjacency_list = {}
        
        for i in range(self.n_nodes):
            adjacency_list[i] = self.get_children(i)
        
        return adjacency_list
    
    def get_edge_list(self) -> List[Tuple[int, int, float]]:
        """
        Get the edge list representation of the graph.
        
        Returns
        -------
        List[Tuple[int, int, float]]
            List of (source, target, weight) tuples
        """
        # Convert to COO format for efficient iteration over non-zero elements
        matrix_coo = self.matrix.tocoo()
        
        # Create edge list
        edge_list = []
        for i, j, v in zip(matrix_coo.row, matrix_coo.col, matrix_coo.data):
            edge_list.append((i, j, v))
        
        return edge_list
    
    def to_dense(self) -> np.ndarray:
        """
        Convert to dense matrix.
        
        Returns
        -------
        np.ndarray
            Dense adjacency matrix
        """
        return self.matrix.toarray()
    
    def to_networkx(self) -> nx.DiGraph:
        """
        Convert to NetworkX graph.
        
        Returns
        -------
        nx.DiGraph
            NetworkX directed graph
        """
        # Create empty directed graph
        G = nx.DiGraph()
        
        # Add nodes
        G.add_nodes_from(range(self.n_nodes))
        
        # Add edges
        for source, target, weight in self.get_edge_list():
            G.add_edge(source, target, weight=weight)
        
        return G
    
    @classmethod
    def from_networkx(cls, G: nx.DiGraph) -> 'SparseAdjacencyMatrix':
        """
        Create from NetworkX graph.
        
        Parameters
        ----------
        G : nx.DiGraph
            NetworkX directed graph
            
        Returns
        -------
        SparseAdjacencyMatrix
            Sparse adjacency matrix
        """
        # Get number of nodes
        n_nodes = G.number_of_nodes()
        
        # Create empty sparse adjacency matrix
        adj_matrix = cls(n_nodes)
        
        # Add edges
        for source, target, data in G.edges(data=True):
            weight = data.get('weight', 1.0)
            adj_matrix.add_edge(source, target, weight)
        
        return adj_matrix
    
    @classmethod
    def from_dense(cls, matrix: np.ndarray) -> 'SparseAdjacencyMatrix':
        """
        Create from dense matrix.
        
        Parameters
        ----------
        matrix : np.ndarray
            Dense adjacency matrix
            
        Returns
        -------
        SparseAdjacencyMatrix
            Sparse adjacency matrix
        """
        # Get number of nodes
        n_nodes = matrix.shape[0]
        
        # Create empty sparse adjacency matrix
        adj_matrix = cls(n_nodes, dtype=matrix.dtype)
        
        # Set matrix
        adj_matrix.matrix = sp.csr_matrix(matrix)
        
        return adj_matrix
    
    def __repr__(self) -> str:
        """
        Get string representation.
        
        Returns
        -------
        str
            String representation
        """
        return f"SparseAdjacencyMatrix(n_nodes={self.n_nodes}, format='{self.format}', nnz={self.matrix.nnz})"
class SparseWeightMatrix:
    """
    Sparse weight matrix for causal graphs.
    
    This class provides a sparse representation of weight matrices for causal graphs,
    enabling efficient storage and manipulation of large causal models.
    
    Parameters
    ----------
    n_nodes : int
        Number of nodes in the graph
    n_features : int
        Number of features per edge
    dtype : np.dtype, optional
        Data type of the matrix, by default np.float32
    format : str, optional
        Sparse matrix format, by default 'csr'
    """
    
    def __init__(
        self,
        n_nodes: int,
        n_features: int,
        dtype: np.dtype = np.float32,
        format: str = 'csr'
    ):
        self.n_nodes = n_nodes
        self.n_features = n_features
        self.dtype = dtype
        self.format = format
        
        # Initialize empty sparse matrices for each feature
        self.matrices = []
        for _ in range(n_features):
            if format == 'csr':
                matrix = sp.csr_matrix((n_nodes, n_nodes), dtype=dtype)
            elif format == 'csc':
                matrix = sp.csc_matrix((n_nodes, n_nodes), dtype=dtype)
            elif format == 'coo':
                matrix = sp.coo_matrix((n_nodes, n_nodes), dtype=dtype)
            elif format == 'lil':
                matrix = sp.lil_matrix((n_nodes, n_nodes), dtype=dtype)
            else:
                raise ValueError(f"Unsupported sparse matrix format: {format}")
            
            self.matrices.append(matrix)
    
    def set_weights(self, source: int, target: int, weights: np.ndarray):
        """
        Set the weights of an edge.
        
        Parameters
        ----------
        source : int
            Source node index
        target : int
            Target node index
        weights : np.ndarray
            Edge weights
        """
        if source < 0 or source >= self.n_nodes:
            raise ValueError(f"Source node index {source} out of range [0, {self.n_nodes-1}]")
        if target < 0 or target >= self.n_nodes:
            raise ValueError(f"Target node index {target} out of range [0, {self.n_nodes-1}]")
        if len(weights) != self.n_features:
            raise ValueError(f"Expected {self.n_features} weights, got {len(weights)}")
        
        # Set weights for each feature
        for i, weight in enumerate(weights):
            # Convert to LIL format for efficient single element assignment
            if self.format != 'lil':
                matrix_lil = self.matrices[i].tolil()
                matrix_lil[source, target] = weight
                self.matrices[i] = matrix_lil.asformat(self.format)
            else:
                self.matrices[i][source, target] = weight
    
    def get_weights(self, source: int, target: int) -> np.ndarray:
        """
        Get the weights of an edge.
        
        Parameters
        ----------
        source : int
            Source node index
        target : int
            Target node index
            
        Returns
        -------
        np.ndarray
            Edge weights
        """
        if source < 0 or source >= self.n_nodes:
            raise ValueError(f"Source node index {source} out of range [0, {self.n_nodes-1}]")
        if target < 0 or target >= self.n_nodes:
            raise ValueError(f"Target node index {target} out of range [0, {self.n_nodes-1}]")
        
        # Get weights for each feature
        weights = np.zeros(self.n_features, dtype=self.dtype)
        for i in range(self.n_features):
            weights[i] = self.matrices[i][source, target]
        
        return weights
    
    def has_edge(self, source: int, target: int) -> bool:
        """
        Check if an edge exists.
        
        Parameters
        ----------
        source : int
            Source node index
        target : int
            Target node index
            
        Returns
        -------
        bool
            True if the edge exists, False otherwise
        """
        if source < 0 or source >= self.n_nodes:
            raise ValueError(f"Source node index {source} out of range [0, {self.n_nodes-1}]")
        if target < 0 or target >= self.n_nodes:
            raise ValueError(f"Target node index {target} out of range [0, {self.n_nodes-1}]")
        
        # Check if any feature has a non-zero weight
        for i in range(self.n_features):
            if self.matrices[i][source, target] != 0:
                return True
        
        return False
    
    def get_adjacency_matrix(self) -> SparseAdjacencyMatrix:
        """
        Get the adjacency matrix.
        
        Returns
        -------
        SparseAdjacencyMatrix
            Adjacency matrix
        """
        # Create empty sparse adjacency matrix
        adj_matrix = SparseAdjacencyMatrix(self.n_nodes, dtype=self.dtype, format=self.format)
        
        # Set edges
        for source in range(self.n_nodes):
            for target in range(self.n_nodes):
                if self.has_edge(source, target):
                    adj_matrix.add_edge(source, target)
        
        return adj_matrix
    
    def to_dense(self) -> np.ndarray:
        """
        Convert to dense tensor.
        
        Returns
        -------
        np.ndarray
            Dense weight tensor of shape (n_nodes, n_nodes, n_features)
        """
        # Create empty dense tensor
        tensor = np.zeros((self.n_nodes, self.n_nodes, self.n_features), dtype=self.dtype)
        
        # Set weights
        for i in range(self.n_features):
            tensor[:, :, i] = self.matrices[i].toarray()
        
        return tensor
    
    @classmethod
    def from_dense(cls, tensor: np.ndarray) -> 'SparseWeightMatrix':
        """
        Create from dense tensor.
        
        Parameters
        ----------
        tensor : np.ndarray
            Dense weight tensor of shape (n_nodes, n_nodes, n_features)
            
        Returns
        -------
        SparseWeightMatrix
            Sparse weight matrix
        """
        # Get dimensions
        n_nodes, _, n_features = tensor.shape
        
        # Create empty sparse weight matrix
        weight_matrix = cls(n_nodes, n_features, dtype=tensor.dtype)
        
        # Set weights
        for source in range(n_nodes):
            for target in range(n_nodes):
                weights = tensor[source, target, :]
                if np.any(weights != 0):
                    weight_matrix.set_weights(source, target, weights)
        
        return weight_matrix
    
    def __repr__(self) -> str:
        """
        Get string representation.
        
        Returns
        -------
        str
            String representation
        """
        nnz = sum(matrix.nnz for matrix in self.matrices)
        return f"SparseWeightMatrix(n_nodes={self.n_nodes}, n_features={self.n_features}, format='{self.format}', nnz={nnz})"
class SparseCausalGraph:
    """
    Sparse causal graph.
    
    This class provides a sparse representation of causal graphs,
    enabling efficient storage and manipulation of large causal models.
    
    Parameters
    ----------
    n_nodes : int
        Number of nodes in the graph
    node_names : List[str], optional
        Names of the nodes, by default None
    directed : bool, optional
        Whether the graph is directed, by default True
    weighted : bool, optional
        Whether the graph is weighted, by default False
    n_features : int, optional
        Number of features per edge, by default 1
    dtype : np.dtype, optional
        Data type of the matrix, by default np.float32
    format : str, optional
        Sparse matrix format, by default 'csr'
    """
    
    def __init__(
        self,
        n_nodes: int,
        node_names: Optional[List[str]] = None,
        directed: bool = True,
        weighted: bool = False,
        n_features: int = 1,
        dtype: np.dtype = np.float32,
        format: str = 'csr'
    ):
        self.n_nodes = n_nodes
        self.directed = directed
        self.weighted = weighted
        self.n_features = n_features
        self.dtype = dtype
        self.format = format
        
        # Set node names
        if node_names is None:
            self.node_names = [f"X{i}" for i in range(n_nodes)]
        else:
            if len(node_names) != n_nodes:
                raise ValueError(f"Expected {n_nodes} node names, got {len(node_names)}")
            self.node_names = node_names
        
        # Create node name to index mapping
        self.node_to_idx = {name: i for i, name in enumerate(self.node_names)}
        
        # Initialize adjacency matrix
        self.adjacency_matrix = SparseAdjacencyMatrix(n_nodes, dtype=dtype, format=format)
        
        # Initialize weight matrix if weighted
        if weighted:
            self.weight_matrix = SparseWeightMatrix(n_nodes, n_features, dtype=dtype, format=format)
        else:
            self.weight_matrix = None
    
    def add_edge(
        self,
        source: Union[int, str],
        target: Union[int, str],
        weight: Optional[Union[float, np.ndarray]] = None
    ):
        """
        Add an edge to the graph.
        
        Parameters
        ----------
        source : Union[int, str]
            Source node index or name
        target : Union[int, str]
            Target node index or name
        weight : Optional[Union[float, np.ndarray]], optional
            Edge weight, by default None
        """
        # Convert node names to indices
        source_idx = self._get_node_idx(source)
        target_idx = self._get_node_idx(target)
        
        # Add edge to adjacency matrix
        self.adjacency_matrix.add_edge(source_idx, target_idx)
        
        # Add edge to weight matrix if weighted
        if self.weighted:
            if weight is None:
                weight = np.ones(self.n_features, dtype=self.dtype)
            elif isinstance(weight, (int, float)):
                weight = np.full(self.n_features, weight, dtype=self.dtype)
            
            self.weight_matrix.set_weights(source_idx, target_idx, weight)
        
        # Add reverse edge if undirected
        if not self.directed:
            self.adjacency_matrix.add_edge(target_idx, source_idx)
            
            if self.weighted:
                self.weight_matrix.set_weights(target_idx, source_idx, weight)
    
    def remove_edge(self, source: Union[int, str], target: Union[int, str]):
        """
        Remove an edge from the graph.
        
        Parameters
        ----------
        source : Union[int, str]
            Source node index or name
        target : Union[int, str]
            Target node index or name
        """
        # Convert node names to indices
        source_idx = self._get_node_idx(source)
        target_idx = self._get_node_idx(target)
        
        # Remove edge from adjacency matrix
        self.adjacency_matrix.remove_edge(source_idx, target_idx)
        
        # Remove edge from weight matrix if weighted
        if self.weighted:
            self.weight_matrix.set_weights(source_idx, target_idx, np.zeros(self.n_features, dtype=self.dtype))
        
        # Remove reverse edge if undirected
        if not self.directed:
            self.adjacency_matrix.remove_edge(target_idx, source_idx)
            
            if self.weighted:
                self.weight_matrix.set_weights(target_idx, source_idx, np.zeros(self.n_features, dtype=self.dtype))
    
    def has_edge(self, source: Union[int, str], target: Union[int, str]) -> bool:
        """
        Check if an edge exists.
        
        Parameters
        ----------
        source : Union[int, str]
            Source node index or name
        target : Union[int, str]
            Target node index or name
            
        Returns
        -------
        bool
            True if the edge exists, False otherwise
        """
        # Convert node names to indices
        source_idx = self._get_node_idx(source)
        target_idx = self._get_node_idx(target)
        
        # Check if edge exists in adjacency matrix
        return self.adjacency_matrix.has_edge(source_idx, target_idx)
    
    def get_edge_weight(self, source: Union[int, str], target: Union[int, str]) -> Union[float, np.ndarray]:
        """
        Get the weight of an edge.
        
        Parameters
        ----------
        source : Union[int, str]
            Source node index or name
        target : Union[int, str]
            Target node index or name
            
        Returns
        -------
        Union[float, np.ndarray]
            Edge weight
        """
        if not self.weighted:
            raise ValueError("Graph is not weighted")
        
        # Convert node names to indices
        source_idx = self._get_node_idx(source)
        target_idx = self._get_node_idx(target)
        
        # Get edge weight from weight matrix
        return self.weight_matrix.get_weights(source_idx, target_idx)
    
    def get_children(self, node: Union[int, str]) -> List[Union[int, str]]:
        """
        Get the children of a node.
        
        Parameters
        ----------
        node : Union[int, str]
            Node index or name
            
        Returns
        -------
        List[Union[int, str]]
            List of child node indices or names
        """
        # Convert node name to index
        node_idx = self._get_node_idx(node)
        
        # Get children from adjacency matrix
        children_idx = self.adjacency_matrix.get_children(node_idx)
        
        # Convert indices to names if node is a string
        if isinstance(node, str):
            return [self.node_names[idx] for idx in children_idx]
        else:
            return children_idx
    
    def get_parents(self, node: Union[int, str]) -> List[Union[int, str]]:
        """
        Get the parents of a node.
        
        Parameters
        ----------
        node : Union[int, str]
            Node index or name
            
        Returns
        -------
        List[Union[int, str]]
            List of parent node indices or names
        """
        # Convert node name to index
        node_idx = self._get_node_idx(node)
        
        # Get parents from adjacency matrix
        parents_idx = self.adjacency_matrix.get_parents(node_idx)
        
        # Convert indices to names if node is a string
        if isinstance(node, str):
            return [self.node_names[idx] for idx in parents_idx]
        else:
            return parents_idx
    
    def get_neighbors(self, node: Union[int, str]) -> List[Union[int, str]]:
        """
        Get the neighbors of a node.
        
        Parameters
        ----------
        node : Union[int, str]
            Node index or name
            
        Returns
        -------
        List[Union[int, str]]
            List of neighbor node indices or names
        """
        # Convert node name to index
        node_idx = self._get_node_idx(node)
        
        # Get neighbors from adjacency matrix
        neighbors_idx = self.adjacency_matrix.get_neighbors(node_idx)
        
        # Convert indices to names if node is a string
        if isinstance(node, str):
            return [self.node_names[idx] for idx in neighbors_idx]
        else:
            return neighbors_idx
    
    def get_adjacency_list(self) -> Dict[Union[int, str], List[Union[int, str]]]:
        """
        Get the adjacency list representation of the graph.
        
        Returns
        -------
        Dict[Union[int, str], List[Union[int, str]]]
            Dictionary mapping node indices or names to lists of adjacent node indices or names
        """
        # Get adjacency list from adjacency matrix
        adjacency_list_idx = self.adjacency_matrix.get_adjacency_list()
        
        # Convert indices to names
        adjacency_list = {}
        for node_idx, children_idx in adjacency_list_idx.items():
            node = self.node_names[node_idx]
            children = [self.node_names[idx] for idx in children_idx]
            adjacency_list[node] = children
        
        return adjacency_list
    
    def get_edge_list(self) -> List[Tuple[Union[int, str], Union[int, str], Union[float, np.ndarray]]]:
        """
        Get the edge list representation of the graph.
        
        Returns
        -------
        List[Tuple[Union[int, str], Union[int, str], Union[float, np.ndarray]]]
            List of (source, target, weight) tuples
        """
        # Get edge list from adjacency matrix
        edge_list_idx = self.adjacency_matrix.get_edge_list()
        
        # Convert indices to names and get weights
        edge_list = []
        for source_idx, target_idx, _ in edge_list_idx:
            source = self.node_names[source_idx]
            target = self.node_names[target_idx]
            
            if self.weighted:
                weight = self.weight_matrix.get_weights(source_idx, target_idx)
            else:
                weight = 1.0
            
            edge_list.append((source, target, weight))
        
        return edge_list
    
    def to_networkx(self) -> nx.DiGraph:
        """
        Convert to NetworkX graph.
        
        Returns
        -------
        nx.DiGraph
            NetworkX directed graph
        """
        # Create empty directed graph
        if self.directed:
            G = nx.DiGraph()
        else:
            G = nx.Graph()
        
        # Add nodes with names
        for i, name in enumerate(self.node_names):
            G.add_node(i, name=name)
        
        # Add edges with weights
        for source, target, weight in self.get_edge_list():
            source_idx = self._get_node_idx(source)
            target_idx = self._get_node_idx(target)
            
            if self.weighted:
                G.add_edge(source_idx, target_idx, weight=weight)
            else:
                G.add_edge(source_idx, target_idx)
        
        return G
    
    @classmethod
    def from_networkx(
        cls,
        G: nx.Graph,
        node_names: Optional[List[str]] = None,
        weighted: bool = False,
        n_features: int = 1
    ) -> 'SparseCausalGraph':
        """
        Create from NetworkX graph.
        
        Parameters
        ----------
        G : nx.Graph
            NetworkX graph
        node_names : Optional[List[str]], optional
            Names of the nodes, by default None
        weighted : bool, optional
            Whether the graph is weighted, by default False
        n_features : int, optional
            Number of features per edge, by default 1
            
        Returns
        -------
        SparseCausalGraph
            Sparse causal graph
        """
        # Get number of nodes
        n_nodes = G.number_of_nodes()
        
        # Get node names
        if node_names is None:
            node_names = [G.nodes[i].get('name', f"X{i}") for i in range(n_nodes)]
        
        # Create empty sparse causal graph
        directed = isinstance(G, nx.DiGraph)
        graph = cls(n_nodes, node_names, directed, weighted, n_features)
        
        # Add edges
        for source, target, data in G.edges(data=True):
            if weighted:
                weight = data.get('weight', 1.0)
                graph.add_edge(source, target, weight)
            else:
                graph.add_edge(source, target)
        
        return graph
    
    def _get_node_idx(self, node: Union[int, str]) -> int:
        """
        Get the index of a node.
        
        Parameters
        ----------
        node : Union[int, str]
            Node index or name
            
        Returns
        -------
        int
            Node index
        """
        if isinstance(node, int):
            if node < 0 or node >= self.n_nodes:
                raise ValueError(f"Node index {node} out of range [0, {self.n_nodes-1}]")
            return node
        elif isinstance(node, str):
            if node not in self.node_to_idx:
                raise ValueError(f"Node name '{node}' not found")
            return self.node_to_idx[node]
        else:
            raise TypeError(f"Node must be int or str, got {type(node)}")
    
    def __repr__(self) -> str:
        """
        Get string representation.
        
        Returns
        -------
        str
            String representation
        """
        return f"SparseCausalGraph(n_nodes={self.n_nodes}, directed={self.directed}, weighted={self.weighted}, n_features={self.n_features})"
