#!/usr/bin/env python3
"""
Read and compare the Excel comparison files to understand prediction accuracy
before and after adding new API data.
"""

import pandas as pd
import numpy as np
import os
import sys

def read_excel_comparison():
    """Read both comparison Excel files and analyze the differences."""
    
    print("📊 Reading Comparison Data")
    print("=" * 50)
    
    # File paths
    old_file = "results/comparison/Comparison old.xlsx"
    new_file = "results/comparison/Comparison.xlsx"
    
    # Check if files exist
    if not os.path.exists(old_file):
        print(f"❌ File not found: {old_file}")
        return
    
    if not os.path.exists(new_file):
        print(f"❌ File not found: {new_file}")
        return
    
    try:
        # Read Excel files
        print("📖 Reading comparison files...")
        
        # Try to read all sheets
        old_data = pd.read_excel(old_file, sheet_name=None)
        new_data = pd.read_excel(new_file, sheet_name=None)
        
        print(f"✅ Old file sheets: {list(old_data.keys())}")
        print(f"✅ New file sheets: {list(new_data.keys())}")
        
        # Analyze each sheet
        for sheet_name in old_data.keys():
            if sheet_name in new_data:
                print(f"\n📋 Analyzing sheet: {sheet_name}")
                print("-" * 30)
                
                old_sheet = old_data[sheet_name]
                new_sheet = new_data[sheet_name]
                
                print(f"Old data shape: {old_sheet.shape}")
                print(f"New data shape: {new_sheet.shape}")

                # Clean up the data - fix column names
                if old_sheet.iloc[0].astype(str).str.contains('Date|Quantity|Predicted').any():
                    # First row contains headers
                    old_sheet.columns = old_sheet.iloc[0]
                    old_sheet = old_sheet.drop(0).reset_index(drop=True)

                if new_sheet.iloc[0].astype(str).str.contains('Date|Quantity|Predicted').any():
                    # First row contains headers
                    new_sheet.columns = new_sheet.iloc[0]
                    new_sheet = new_sheet.drop(0).reset_index(drop=True)

                # Remove NaN rows
                old_sheet = old_sheet.dropna()
                new_sheet = new_sheet.dropna()

                print(f"After cleaning - Old: {old_sheet.shape}, New: {new_sheet.shape}")

                # Display first few rows
                print("\n🔍 Old data (cleaned, first 5 rows):")
                print(old_sheet.head())

                print("\n🔍 New data (cleaned, first 5 rows):")
                print(new_sheet.head())
                
                # Analyze the specific format we found
                analyze_advik_comparison(old_sheet, new_sheet, sheet_name)
        
    except Exception as e:
        print(f"❌ Error reading Excel files: {e}")
        print("Trying to read as CSV...")
        
        # Try reading as CSV if Excel fails
        try:
            old_data = pd.read_csv(old_file.replace('.xlsx', '.csv'))
            new_data = pd.read_csv(new_file.replace('.xlsx', '.csv'))
            print("✅ Successfully read as CSV")
        except Exception as e2:
            print(f"❌ Error reading as CSV: {e2}")

def analyze_advik_comparison(old_df, new_df, sheet_name):
    """Analyze the ADVIK comparison data specifically."""

    print(f"\n🎯 ADVIK Comparison Analysis")
    print("-" * 40)

    # Check if data is identical
    if old_df.equals(new_df):
        print("⚠️ OLD and NEW data are IDENTICAL!")
        print("This means the new API data had NO IMPACT on predictions.")
        print("The predictions are exactly the same before and after adding API data.")
    else:
        print("✅ Data differs between old and new versions")

    # Analyze the prediction accuracy
    try:
        # Convert columns to proper types
        if 'Date' in old_df.columns:
            old_df['Date'] = pd.to_datetime(old_df['Date'], errors='coerce')
            new_df['Date'] = pd.to_datetime(new_df['Date'], errors='coerce')

        if 'Quantity' in old_df.columns and 'Predicted' in old_df.columns:
            old_df['Quantity'] = pd.to_numeric(old_df['Quantity'], errors='coerce')
            old_df['Predicted'] = pd.to_numeric(old_df['Predicted'], errors='coerce')
            new_df['Quantity'] = pd.to_numeric(new_df['Quantity'], errors='coerce')
            new_df['Predicted'] = pd.to_numeric(new_df['Predicted'], errors='coerce')

            # Calculate accuracy metrics
            old_actual = old_df['Quantity'].dropna()
            old_pred = old_df['Predicted'].dropna()

            if len(old_actual) > 0 and len(old_pred) > 0:
                # Align data
                min_len = min(len(old_actual), len(old_pred))
                old_actual = old_actual.iloc[:min_len]
                old_pred = old_pred.iloc[:min_len]

                # Calculate metrics
                mae = np.mean(np.abs(old_actual - old_pred))
                mape = np.mean(np.abs((old_actual - old_pred) / (old_actual + 1e-6))) * 100
                rmse = np.sqrt(np.mean((old_actual - old_pred) ** 2))

                print(f"\n📊 Prediction Accuracy Metrics:")
                print(f"   MAE (Mean Absolute Error): {mae:,.0f}")
                print(f"   MAPE (Mean Absolute % Error): {mape:.2f}%")
                print(f"   RMSE (Root Mean Square Error): {rmse:,.0f}")

                # Analyze individual predictions
                print(f"\n📈 Individual Prediction Analysis:")
                for i in range(min(len(old_actual), 5)):
                    actual = old_actual.iloc[i]
                    pred = old_pred.iloc[i]
                    error = abs(actual - pred)
                    error_pct = (error / actual) * 100 if actual > 0 else 0

                    print(f"   Period {i+1}: Actual={actual:,.0f}, Predicted={pred:,.0f}, Error={error:,.0f} ({error_pct:.1f}%)")

                # Overall assessment
                print(f"\n🎯 Overall Assessment:")
                if mape < 10:
                    print("✅ EXCELLENT accuracy (MAPE < 10%)")
                elif mape < 20:
                    print("✅ GOOD accuracy (MAPE < 20%)")
                elif mape < 30:
                    print("⚠️ FAIR accuracy (MAPE < 30%)")
                else:
                    print("❌ POOR accuracy (MAPE > 30%)")

                # Check if predictions are close to actual
                close_predictions = (np.abs((old_actual - old_pred) / old_actual) < 0.15).sum()
                total_predictions = len(old_actual)
                close_percentage = (close_predictions / total_predictions) * 100

                print(f"📊 {close_predictions}/{total_predictions} predictions within 15% of actual ({close_percentage:.1f}%)")

    except Exception as e:
        print(f"❌ Error analyzing data: {e}")

def analyze_prediction_accuracy(old_df, new_df, sheet_name):
    """Analyze prediction accuracy between old and new data."""
    
    print(f"\n🎯 Prediction Accuracy Analysis for {sheet_name}")
    print("-" * 40)
    
    # Find actual and prediction columns
    actual_cols = [col for col in old_df.columns if 'actual' in col.lower()]
    pred_cols = [col for col in old_df.columns if 'prediction' in col.lower() or 'forecast' in col.lower()]
    
    if not actual_cols or not pred_cols:
        print("⚠️ Could not find actual/prediction columns")
        return
    
    actual_col = actual_cols[0]
    pred_col = pred_cols[0]
    
    print(f"📊 Using columns: {actual_col} vs {pred_col}")
    
    # Calculate metrics for old data
    old_actual = old_df[actual_col].dropna()
    old_pred = old_df[pred_col].dropna()
    
    if len(old_actual) > 0 and len(old_pred) > 0:
        # Align data
        min_len = min(len(old_actual), len(old_pred))
        old_actual = old_actual.iloc[:min_len]
        old_pred = old_pred.iloc[:min_len]
        
        old_mae = np.mean(np.abs(old_actual - old_pred))
        old_mape = np.mean(np.abs((old_actual - old_pred) / (old_actual + 1e-6))) * 100
        old_rmse = np.sqrt(np.mean((old_actual - old_pred) ** 2))
        
        print(f"📈 OLD (before API) Metrics:")
        print(f"   MAE:  {old_mae:.2f}")
        print(f"   MAPE: {old_mape:.2f}%")
        print(f"   RMSE: {old_rmse:.2f}")
    
    # Calculate metrics for new data
    if actual_col in new_df.columns and pred_col in new_df.columns:
        new_actual = new_df[actual_col].dropna()
        new_pred = new_df[pred_col].dropna()
        
        if len(new_actual) > 0 and len(new_pred) > 0:
            # Align data
            min_len = min(len(new_actual), len(new_pred))
            new_actual = new_actual.iloc[:min_len]
            new_pred = new_pred.iloc[:min_len]
            
            new_mae = np.mean(np.abs(new_actual - new_pred))
            new_mape = np.mean(np.abs((new_actual - new_pred) / (new_actual + 1e-6))) * 100
            new_rmse = np.sqrt(np.mean((new_actual - new_pred) ** 2))
            
            print(f"📈 NEW (with API) Metrics:")
            print(f"   MAE:  {new_mae:.2f}")
            print(f"   MAPE: {new_mape:.2f}%")
            print(f"   RMSE: {new_rmse:.2f}")
            
            # Compare
            if 'old_mae' in locals():
                print(f"\n🔄 Comparison:")
                mae_change = ((new_mae - old_mae) / old_mae) * 100
                mape_change = ((new_mape - old_mape) / old_mape) * 100
                rmse_change = ((new_rmse - old_rmse) / old_rmse) * 100
                
                print(f"   MAE change:  {mae_change:+.2f}%")
                print(f"   MAPE change: {mape_change:+.2f}%")
                print(f"   RMSE change: {rmse_change:+.2f}%")
                
                if mae_change < 0:
                    print("✅ API data IMPROVED prediction accuracy!")
                else:
                    print("❌ API data WORSENED prediction accuracy!")

def analyze_time_series_data(old_df, new_df, sheet_name):
    """Analyze time series data to understand prediction patterns."""
    
    print(f"\n📅 Time Series Analysis for {sheet_name}")
    print("-" * 40)
    
    # Look for date columns
    date_cols = [col for col in old_df.columns if 'date' in col.lower()]
    if not date_cols:
        print("⚠️ No date column found")
        return
    
    date_col = date_cols[0]
    
    # Look for quantity/value columns
    value_cols = [col for col in old_df.columns if any(keyword in col.lower() 
                  for keyword in ['quantity', 'value', 'amount', 'forecast', 'prediction'])]
    
    if value_cols:
        print(f"📊 Found value columns: {value_cols}")
        
        # Compare data ranges
        print(f"\n📅 Date ranges:")
        if date_col in old_df.columns:
            old_dates = pd.to_datetime(old_df[date_col], errors='coerce').dropna()
            if len(old_dates) > 0:
                print(f"   Old: {old_dates.min()} to {old_dates.max()} ({len(old_dates)} points)")
        
        if date_col in new_df.columns:
            new_dates = pd.to_datetime(new_df[date_col], errors='coerce').dropna()
            if len(new_dates) > 0:
                print(f"   New: {new_dates.min()} to {new_dates.max()} ({len(new_dates)} points)")
        
        # Compare value statistics
        for col in value_cols[:3]:  # Analyze first 3 value columns
            if col in old_df.columns and col in new_df.columns:
                old_values = old_df[col].dropna()
                new_values = new_df[col].dropna()
                
                print(f"\n📊 {col} Statistics:")
                print(f"   Old - Mean: {old_values.mean():.2f}, Std: {old_values.std():.2f}")
                print(f"   New - Mean: {new_values.mean():.2f}, Std: {new_values.std():.2f}")
                
                if len(old_values) > 0 and len(new_values) > 0:
                    mean_change = ((new_values.mean() - old_values.mean()) / old_values.mean()) * 100
                    print(f"   Mean change: {mean_change:+.2f}%")

if __name__ == "__main__":
    print("📊 Comparison Data Analysis")
    print("=" * 50)
    print("Analyzing prediction accuracy before and after adding new API data.\n")
    
    try:
        read_excel_comparison()
    except KeyboardInterrupt:
        print("\n⏹️ Analysis interrupted by user")
    except Exception as e:
        print(f"\n❌ Analysis failed with error: {e}")
        import traceback
        traceback.print_exc()
