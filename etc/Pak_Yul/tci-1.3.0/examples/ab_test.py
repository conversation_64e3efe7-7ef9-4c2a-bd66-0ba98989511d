"""
A/B Testing for TCI.

This script runs A/B tests comparing different TCI variants.
"""

import os
import sys
import argparse
import numpy as np
import json
import torch
import time
from typing import Dict, Any, List

# Add parent directory to path to import tci
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import tci


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run A/B tests for TCI")
    parser.add_argument("--env", type=str, default="CartPole-v1",
                        help="Environment to use")
    parser.add_argument("--control", type=str, default="TCI-Base",
                        help="Control algorithm")
    parser.add_argument("--treatment", type=str, default="TCI-DynaQ",
                        help="Treatment algorithm")
    parser.add_argument("--iterations", type=int, default=5,
                        help="Number of iterations")
    parser.add_argument("--episodes", type=int, default=100,
                        help="Number of episodes per iteration")
    parser.add_argument("--gpu", action="store_true",
                        help="Use GPU if available")
    parser.add_argument("--output-dir", type=str, default="results/ab_test",
                        help="Directory to save results")
    return parser.parse_args()


def create_tci_agent(name: str, state_dim: int, action_dim: int, device: str):
    """Create a TCI agent with the given name."""
    if name == "TCI-Base":
        return tci.TCIAgent(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device
        )
    elif name == "TCI-DynaQ":
        return tci.TCIAgent(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device,
            extensions=[tci.extensions.DynaQ(planning_steps=10)]
        )
    elif name == "TCI-PER":
        return tci.TCIAgent(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device,
            extensions=[tci.extensions.PER()]
        )
    elif name == "TCI-GAT":
        return tci.TCIAgent(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device,
            extensions=[tci.extensions.GAT()]
        )
    else:
        raise ValueError(f"Unknown agent name: {name}")


def create_environment(env_name: str):
    """Create an environment."""
    import gym
    env = gym.make(env_name)
    
    # Get state and action dimensions
    if isinstance(env.observation_space, gym.spaces.Discrete):
        state_dim = env.observation_space.n
    else:
        state_dim = env.observation_space.shape[0]
    
    if isinstance(env.action_space, gym.spaces.Discrete):
        action_dim = env.action_space.n
    else:
        action_dim = env.action_space.shape[0]
    
    return env, state_dim, action_dim


def run_experiment(agent, env_name: str, episodes: int, seed: int = None):
    """Run an experiment with the given agent."""
    import gym
    
    # Set random seed
    if seed is not None:
        np.random.seed(seed)
        torch.manual_seed(seed)
    
    # Create environment
    env = gym.make(env_name)
    if seed is not None:
        env.seed(seed)
    
    # Run episodes
    rewards = []
    
    for episode in range(episodes):
        state = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            # Select action
            action = agent.act(state, episode)
            
            # Take action
            next_state, reward, done, _ = env.step(action)
            
            # Store transition
            agent.store(state, action, reward, next_state)
            
            # Train agent
            agent.train()
            
            # Update state and reward
            state = next_state
            episode_reward += reward
        
        # Record reward
        rewards.append(episode_reward)
        
        # Print progress
        if (episode + 1) % 10 == 0:
            print(f"Episode {episode + 1}/{episodes}, Reward: {episode_reward:.2f}")
    
    # Calculate metrics
    final_reward = rewards[-1]
    mean_reward = np.mean(rewards)
    max_reward = np.max(rewards)
    
    return {
        "final_reward": final_reward,
        "mean_reward": mean_reward,
        "max_reward": max_reward,
        "rewards": rewards
    }


def run_ab_test(args):
    """Run A/B test."""
    # Set device
    device = "cuda" if args.gpu and torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    # Create environment to get dimensions
    env, state_dim, action_dim = create_environment(args.env)
    print(f"Environment: {args.env}")
    print(f"State dimension: {state_dim}")
    print(f"Action dimension: {action_dim}")
    
    # Create output directory
    output_dir = os.path.join(args.output_dir, args.env)
    os.makedirs(output_dir, exist_ok=True)
    
    # Run A/B test
    control_results = {
        "final_reward": [],
        "mean_reward": [],
        "max_reward": []
    }
    treatment_results = {
        "final_reward": [],
        "mean_reward": [],
        "max_reward": []
    }
    
    # Generate random seeds
    seeds = np.random.randint(0, 10000, args.iterations)
    
    # Run control benchmarks
    print(f"\nRunning control benchmarks ({args.control})...")
    for i, seed in enumerate(seeds):
        print(f"Iteration {i+1}/{args.iterations}")
        
        # Create control agent
        control_agent = create_tci_agent(args.control, state_dim, action_dim, device)
        
        # Run experiment
        results = run_experiment(control_agent, args.env, args.episodes, seed)
        
        # Store results
        for metric in ["final_reward", "mean_reward", "max_reward"]:
            control_results[metric].append(results[metric])
        
        # Save rewards
        with open(os.path.join(output_dir, f"control_rewards_{i}.json"), "w") as f:
            json.dump(results["rewards"], f)
    
    # Run treatment benchmarks
    print(f"\nRunning treatment benchmarks ({args.treatment})...")
    for i, seed in enumerate(seeds):
        print(f"Iteration {i+1}/{args.iterations}")
        
        # Create treatment agent
        treatment_agent = create_tci_agent(args.treatment, state_dim, action_dim, device)
        
        # Run experiment
        results = run_experiment(treatment_agent, args.env, args.episodes, seed)
        
        # Store results
        for metric in ["final_reward", "mean_reward", "max_reward"]:
            treatment_results[metric].append(results[metric])
        
        # Save rewards
        with open(os.path.join(output_dir, f"treatment_rewards_{i}.json"), "w") as f:
            json.dump(results["rewards"], f)
    
    # Calculate statistics
    print("\nA/B Test Results:")
    for metric in ["final_reward", "mean_reward", "max_reward"]:
        control_values = control_results[metric]
        treatment_values = treatment_results[metric]
        
        control_mean = np.mean(control_values)
        treatment_mean = np.mean(treatment_values)
        control_std = np.std(control_values)
        treatment_std = np.std(treatment_values)
        
        # Calculate t-test
        from scipy.stats import ttest_ind
        t_stat, p_value = ttest_ind(control_values, treatment_values, equal_var=False)
        
        # Calculate effect size (Cohen's d)
        n1 = len(control_values)
        n2 = len(treatment_values)
        pooled_std = np.sqrt(((n1 - 1) * control_std**2 + (n2 - 1) * treatment_std**2) / (n1 + n2 - 2))
        effect_size = (treatment_mean - control_mean) / pooled_std
        
        # Interpret effect size
        if abs(effect_size) < 0.2:
            effect_size_interp = "negligible"
        elif abs(effect_size) < 0.5:
            effect_size_interp = "small"
        elif abs(effect_size) < 0.8:
            effect_size_interp = "medium"
        else:
            effect_size_interp = "large"
        
        # Print results
        print(f"\nMetric: {metric}")
        print(f"Control ({args.control}): {control_mean:.2f} ± {control_std:.2f}")
        print(f"Treatment ({args.treatment}): {treatment_mean:.2f} ± {treatment_std:.2f}")
        print(f"Difference: {treatment_mean - control_mean:.2f}")
        print(f"Percent Change: {(treatment_mean - control_mean) / control_mean * 100:.2f}%")
        print(f"p-value: {p_value:.4f}")
        print(f"Significant: {'Yes' if p_value < 0.05 else 'No'}")
        print(f"Effect Size (Cohen's d): {effect_size:.2f} ({effect_size_interp})")
        
        # Save results
        results = {
            "metric": metric,
            "control_name": args.control,
            "treatment_name": args.treatment,
            "control_mean": float(control_mean),
            "treatment_mean": float(treatment_mean),
            "control_std": float(control_std),
            "treatment_std": float(treatment_std),
            "difference": float(treatment_mean - control_mean),
            "percent_change": float((treatment_mean - control_mean) / control_mean * 100),
            "p_value": float(p_value),
            "significant": bool(p_value < 0.05),
            "effect_size": float(effect_size),
            "effect_size_interpretation": effect_size_interp,
            "control_values": [float(x) for x in control_values],
            "treatment_values": [float(x) for x in treatment_values]
        }
        
        # Save results to file
        with open(os.path.join(output_dir, f"{metric}_results.json"), "w") as f:
            json.dump(results, f, indent=2)


def main():
    """Run A/B test."""
    args = parse_args()
    run_ab_test(args)


if __name__ == "__main__":
    main()
