<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorials - TCI Framework</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 20px;
            padding-bottom: 50px;
        }
        .navbar {
            margin-bottom: 30px;
        }
        h1, h2, h3 {
            color: #0d6efd;
            margin-top: 30px;
        }
        .card {
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .card-title {
            color: #0d6efd;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container-fluid">
                <a class="navbar-brand" href="../index.html">TCI Framework</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="../core_concepts.html">Core Concepts</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../getting_started.html">Getting Started</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../extensions.html">Extensions</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="index.html">Tutorials</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../api_reference.html">API Reference</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <h1>TCI Framework Tutorials</h1>
        
        <p class="lead">
            Welcome to the TCI Framework tutorials! These step-by-step guides will help you understand and use the various features of the TCI framework.
        </p>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Getting Started with TCI</h5>
                        <p class="card-text">Learn the basics of the TCI framework, including how to create agents, environments, and run simple experiments.</p>
                        <a href="../getting_started.html" class="btn btn-primary">Read Tutorial</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Causal Discovery in TCI</h5>
                        <p class="card-text">Explore the causal discovery capabilities of the TCI framework, including different algorithms and their applications.</p>
                        <a href="causal_discovery.html" class="btn btn-primary">Read Tutorial</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Transfer Learning</h5>
                        <p class="card-text">Learn how to transfer knowledge between environments using the TCI framework's transfer learning capabilities.</p>
                        <a href="transfer_learning.html" class="btn btn-primary">Read Tutorial</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Model Persistence</h5>
                        <p class="card-text">Discover how to save and load TCI models for later use, deployment, or transfer learning.</p>
                        <a href="model_persistence.html" class="btn btn-primary">Read Tutorial</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Working with Extensions</h5>
                        <p class="card-text">Explore the various extensions available in the TCI framework and learn how to use them effectively.</p>
                        <a href="#" class="btn btn-secondary disabled">Coming Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Advanced Environments</h5>
                        <p class="card-text">Learn how to work with complex environments like the Tennessee Eastman Process (TEP) in the TCI framework.</p>
                        <a href="#" class="btn btn-secondary disabled">Coming Soon</a>
                    </div>
                </div>
            </div>
        </div>

        <h2>Video Tutorials</h2>
        
        <p>
            Video tutorials are coming soon! Check back later for video guides on using the TCI framework.
        </p>

        <h2>Example Projects</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Chemical Process Control</h5>
                        <p class="card-text">A complete example of using TCI for chemical process control with the TEP environment.</p>
                        <a href="#" class="btn btn-secondary disabled">Coming Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Multi-agent Causal Discovery</h5>
                        <p class="card-text">Explore how multiple TCI agents can collaborate to discover causal relationships in complex environments.</p>
                        <a href="#" class="btn btn-secondary disabled">Coming Soon</a>
                    </div>
                </div>
            </div>
        </div>

        <h2>Additional Resources</h2>
        
        <ul>
            <li><a href="../api_reference.html">API Reference</a> - Detailed documentation of all TCI classes and functions</li>
            <li><a href="../core_concepts.html">Core Concepts</a> - Fundamental concepts behind the TCI framework</li>
            <li><a href="../extensions.html">Extensions</a> - Information about the various extensions available in TCI</li>
        </ul>

        <div class="footer">
            TCI Framework & it's Documentation © 2025 By Yuliadi Rumanto
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
