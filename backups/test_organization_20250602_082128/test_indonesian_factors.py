"""
Test script for Indonesian external factors.

This script specifically tests if <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> to Dollar factors
are correctly applied to Indonesian customers.
"""

import pandas as pd
import numpy as np
from datetime import datetime
from utils.external_factor_processor import ExternalFactorProcessor

def main():
    """Test Indonesian external factors."""
    print("Testing Indonesian external factors...")
    
    # Create sample data with dates during Ramadhan
    dates = pd.date_range(start='2024-03-01', end='2024-04-15', freq='D')
    df = pd.DataFrame({
        'date': dates,
        'quantity': np.random.randint(100, 1000, size=len(dates))
    })
    
    # Create an instance of ExternalFactorProcessor
    processor = ExternalFactorProcessor()
    
    # Test with Indonesian customer
    indonesian_customer = "ADVIK (IND)"
    
    # Process data for Indonesian customer
    print(f"\nProcessing data for Indonesian customer: {indonesian_customer}")
    df_indonesia = processor.add_external_factors(df, customer_name=indonesian_customer)
    
    # Check if Ramadhan factor is applied
    ramadhan_days = df_indonesia[df_indonesia['is_ram<PERSON>han'] == True]
    print(f"\nRamadhan days detected: {len(ramadhan_days)} out of {len(df_indonesia)} days")
    if len(ramadhan_days) > 0:
        print(f"First Ramadhan day: {ramadhan_days['date'].min().strftime('%Y-%m-%d')}")
        print(f"Last Ramadhan day: {ramadhan_days['date'].max().strftime('%Y-%m-%d')}")
    else:
        print("No Ramadhan days detected!")
    
    # Check if Rupiah to Dollar factor is applied
    rp_to_dollar_values = df_indonesia['rp_to_dollar'].dropna()
    print(f"\nRupiah to Dollar values: {len(rp_to_dollar_values)} out of {len(df_indonesia)} days")
    if len(rp_to_dollar_values) > 0:
        print(f"Min Rupiah to Dollar rate: {rp_to_dollar_values.min()}")
        print(f"Max Rupiah to Dollar rate: {rp_to_dollar_values.max()}")
        print(f"Average Rupiah to Dollar rate: {rp_to_dollar_values.mean()}")
    else:
        print("No Rupiah to Dollar values detected!")
    
    # Print sample of the data
    print("\nSample of processed data:")
    print(df_indonesia[['date', 'is_ramadhan', 'rp_to_dollar']].head(10))
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
