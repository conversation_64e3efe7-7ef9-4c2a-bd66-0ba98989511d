"""
Test script to verify the industry data integration with TCI-fix model.
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime

# Import the industry data fetcher
from industry_data_fetcher import IndustryDataFetcher

# Import the external data loader
from tci_fix_integration import load_external_data

def test_industry_data():
    """Test the industry data fetcher and integration."""
    print("Testing industry data fetcher...")

    # Create industry data fetcher
    fetcher = IndustryDataFetcher()

    # Get all industry data
    industry_data = fetcher.get_all_industry_data(
        country_codes=["JP", "ID"],
        start_year=2020,
        end_year=2025
    )

    # Verify the data
    print(f"Fetched {len(industry_data)} industry data sources:")
    for name, df in industry_data.items():
        if isinstance(df, pd.DataFrame) and 'date' in df.columns and len(df) > 0:
            # Convert date column to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(df['date']):
                df['date'] = pd.to_datetime(df['date'])

            # Now we can safely get min and max dates
            min_date = df['date'].min()
            max_date = df['date'].max()

            print(f"  - {name}: {len(df)} records from {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
            # Print the first few rows
            print(df.head(2))
        else:
            print(f"  - {name}: {type(df)} (not a valid DataFrame with dates)")

    # Test the load_external_data function
    print("\nTesting load_external_data function...")
    external_data = load_external_data(start_year=2020, end_year=2025)

    # Verify the data
    print(f"Loaded {len(external_data)} external data sources")

    return industry_data, external_data

if __name__ == "__main__":
    test_industry_data()
