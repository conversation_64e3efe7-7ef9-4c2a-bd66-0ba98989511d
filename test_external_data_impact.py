#!/usr/bin/env python3
"""
Test External Data Impact

This script tests whether the new API data (industrial, automotive, manufacturing)
is actually impacting the TCI-fix model predictions.

It compares predictions with and without external data to measure the actual impact.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import required modules
try:
    from tci_fix import TCIFixPredictor
    from folder_data_manager import FolderDataManager
    from tci_fix_integration import load_external_data
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Error importing modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

def load_sample_data():
    """Load a sample customer-product combination for testing."""
    print("\n📊 Loading sample data...")
    
    # Initialize data manager
    data_manager = FolderDataManager("dataset")
    success, message = data_manager.load_data_from_folder()
    
    if not success:
        print(f"❌ Failed to load data: {message}")
        return None, None, None
    
    # Get available customers and products
    customers = data_manager.get_customers()
    if not customers:
        print("❌ No customers found")
        return None, None, None
    
    # Select first customer with sufficient data
    selected_customer = None
    selected_product = None
    sample_data = None
    
    for customer in customers[:5]:  # Check first 5 customers
        products = data_manager.get_products(customer)
        if not products:
            continue

        for product in products[:3]:  # Check first 3 products per customer
            key = f"{customer}_{product}"
            if key not in data_manager.data:
                continue
            data = data_manager.data[key].copy()
            if data is not None and len(data) >= 24:  # Need at least 2 years of data
                selected_customer = customer
                selected_product = product
                sample_data = data.copy()
                break
        
        if selected_customer:
            break
    
    if selected_customer is None:
        print("❌ No suitable customer-product combination found (need at least 24 months of data)")
        return None, None, None
    
    print(f"✅ Selected: {selected_customer} - {selected_product}")
    print(f"   Data points: {len(sample_data)}")
    print(f"   Date range: {sample_data['date'].min()} to {sample_data['date'].max()}")
    
    return selected_customer, selected_product, sample_data

def test_external_data_impact():
    """Test the impact of external data on predictions."""
    print("🔬 Testing External Data Impact")
    print("=" * 50)
    
    # Load sample data
    customer, product, data = load_sample_data()
    if data is None:
        return
    
    # Load external data
    print("\n🌐 Loading external data...")
    try:
        external_data = load_external_data(start_year=2020, end_year=2025)
        if external_data:
            print(f"✅ Loaded {len(external_data)} external data sources:")
            for source_name in external_data.keys():
                print(f"   - {source_name}: {len(external_data[source_name])} records")
        else:
            print("⚠️ No external data loaded")
            external_data = {}
    except Exception as e:
        print(f"⚠️ Error loading external data: {e}")
        external_data = {}
    
    # Prepare data for testing
    print("\n🔧 Preparing data for testing...")
    
    # Split data into training and testing
    split_date = data['date'].quantile(0.8)  # Use 80% for training
    train_data = data[data['date'] <= split_date].copy()
    test_data = data[data['date'] > split_date].copy()
    
    print(f"   Training data: {len(train_data)} points ({train_data['date'].min()} to {train_data['date'].max()})")
    print(f"   Testing data: {len(test_data)} points ({test_data['date'].min()} to {test_data['date'].max()})")
    
    if len(test_data) < 3:
        print("❌ Not enough test data (need at least 3 months)")
        return
    
    # Test 1: Model WITH external data
    print("\n🤖 Training model WITH external data...")
    try:
        model_with_external = TCIFixPredictor(
            max_lag=6,
            feature_engineering_level='advanced',
            n_estimators=100,
            min_samples_leaf=4,
            max_depth=20,
            trend_weight=2.0,
            seasonal_weight=1.2,
            balance_historical_future=True
        )
        
        model_with_external.fit(train_data, external_data)
        
        # Generate predictions
        predictions_with = model_with_external.predict(
            start_date=test_data['date'].min(),
            end_date=test_data['date'].max(),
            data=train_data,
            external_data=external_data
        )
        
        print("✅ Model with external data trained successfully")
        
    except Exception as e:
        print(f"❌ Error training model with external data: {e}")
        return
    
    # Test 2: Model WITHOUT external data
    print("\n🤖 Training model WITHOUT external data...")
    try:
        model_without_external = TCIFixPredictor(
            max_lag=6,
            feature_engineering_level='advanced',
            n_estimators=100,
            min_samples_leaf=4,
            max_depth=20,
            trend_weight=2.0,
            seasonal_weight=1.2,
            balance_historical_future=True
        )
        
        model_without_external.fit(train_data, None)  # No external data
        
        # Generate predictions
        predictions_without = model_without_external.predict(
            start_date=test_data['date'].min(),
            end_date=test_data['date'].max(),
            data=train_data,
            external_data=None
        )
        
        print("✅ Model without external data trained successfully")
        
    except Exception as e:
        print(f"❌ Error training model without external data: {e}")
        return
    
    # Compare predictions
    print("\n📊 Analyzing prediction differences...")
    
    # Ensure both predictions have the same dates for comparison
    common_dates = pd.merge(predictions_with[['date']], predictions_without[['date']], on='date')['date']
    
    pred_with_filtered = predictions_with[predictions_with['date'].isin(common_dates)].sort_values('date')
    pred_without_filtered = predictions_without[predictions_without['date'].isin(common_dates)].sort_values('date')
    
    if len(pred_with_filtered) == 0 or len(pred_without_filtered) == 0:
        print("❌ No overlapping prediction dates found")
        return
    
    # Calculate differences
    pred_diff = abs(pred_with_filtered['prediction'].values - pred_without_filtered['prediction'].values)
    avg_prediction = (pred_with_filtered['prediction'].values + pred_without_filtered['prediction'].values) / 2
    
    # Calculate metrics
    absolute_difference = pred_diff.mean()
    relative_difference = (pred_diff / (avg_prediction + 1e-6)).mean() * 100  # Percentage
    max_difference = pred_diff.max()
    
    print(f"\n📈 RESULTS:")
    print(f"   Number of prediction periods compared: {len(pred_diff)}")
    print(f"   Average absolute difference: {absolute_difference:.2f}")
    print(f"   Average relative difference: {relative_difference:.2f}%")
    print(f"   Maximum difference: {max_difference:.2f}")
    
    # Interpretation
    print(f"\n🎯 INTERPRETATION:")
    if relative_difference < 1:
        print("   ❌ VERY LOW IMPACT: External data has minimal effect (<1% difference)")
        print("   💡 Suggestion: External data is not significantly influencing predictions")
    elif relative_difference < 5:
        print("   ⚠️ LOW IMPACT: External data has small effect (1-5% difference)")
        print("   💡 Suggestion: External data provides minor adjustments")
    elif relative_difference < 15:
        print("   ✅ MODERATE IMPACT: External data has noticeable effect (5-15% difference)")
        print("   💡 Suggestion: External data is providing valuable information")
    else:
        print("   🚀 HIGH IMPACT: External data has significant effect (>15% difference)")
        print("   💡 Suggestion: External data is strongly influencing predictions")
    
    # Feature importance analysis
    print(f"\n🔍 FEATURE IMPORTANCE ANALYSIS:")
    try:
        if hasattr(model_with_external, 'model') and hasattr(model_with_external.model, 'feature_importances_'):
            feature_names = model_with_external.feature_columns if hasattr(model_with_external, 'feature_columns') else []
            importances = model_with_external.model.feature_importances_
            
            if len(feature_names) == len(importances):
                importance_df = pd.DataFrame({
                    'feature': feature_names,
                    'importance': importances
                }).sort_values('importance', ascending=False)
                
                # Identify external features
                external_keywords = ['industrial', 'automotive', 'manufacturing', 'jp_', 'id_']
                external_features = importance_df[
                    importance_df['feature'].str.contains('|'.join(external_keywords), case=False, na=False)
                ]
                
                if len(external_features) > 0:
                    total_external_importance = external_features['importance'].sum()
                    print(f"   External features total importance: {total_external_importance:.4f} ({total_external_importance*100:.2f}%)")
                    print(f"   Top 3 external features:")
                    for i, row in external_features.head(3).iterrows():
                        print(f"     - {row['feature']}: {row['importance']:.4f}")
                else:
                    print("   ❌ No external features found in top features")
            else:
                print("   ⚠️ Feature names and importances length mismatch")
        else:
            print("   ⚠️ Feature importance not available")
    except Exception as e:
        print(f"   ❌ Error analyzing feature importance: {e}")
    
    # Create visualization
    try:
        print(f"\n📊 Creating visualization...")
        
        plt.figure(figsize=(12, 8))
        
        # Plot 1: Prediction comparison
        plt.subplot(2, 1, 1)
        plt.plot(pred_with_filtered['date'], pred_with_filtered['prediction'], 
                label='With External Data', marker='o', linewidth=2)
        plt.plot(pred_without_filtered['date'], pred_without_filtered['prediction'], 
                label='Without External Data', marker='s', linewidth=2)
        
        # Add actual values if available
        test_dates = test_data['date']
        actual_values = test_data['quantity']
        plt.plot(test_dates, actual_values, label='Actual', marker='^', linewidth=2, alpha=0.7)
        
        plt.title(f'Prediction Comparison: {customer} - {product}')
        plt.xlabel('Date')
        plt.ylabel('Quantity')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        
        # Plot 2: Difference
        plt.subplot(2, 1, 2)
        plt.plot(pred_with_filtered['date'], pred_diff, marker='o', color='red', linewidth=2)
        plt.title('Absolute Difference Between Predictions')
        plt.xlabel('Date')
        plt.ylabel('Absolute Difference')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # Save plot
        plot_filename = f'external_data_impact_test_{customer}_{product}.png'.replace(' ', '_').replace('/', '_')
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"   📊 Visualization saved as: {plot_filename}")
        
        plt.show()
        
    except Exception as e:
        print(f"   ❌ Error creating visualization: {e}")
    
    print(f"\n✅ External data impact test completed!")
    
    return {
        'customer': customer,
        'product': product,
        'absolute_difference': absolute_difference,
        'relative_difference': relative_difference,
        'max_difference': max_difference,
        'num_periods': len(pred_diff)
    }

if __name__ == "__main__":
    print("🔬 External Data Impact Test")
    print("=" * 50)
    print("This script will test whether external API data is impacting your predictions.")
    print("It compares TCI-fix model predictions with and without external data.\n")
    
    try:
        results = test_external_data_impact()
        if results:
            print(f"\n📋 SUMMARY:")
            print(f"   Customer: {results['customer']}")
            print(f"   Product: {results['product']}")
            print(f"   Impact: {results['relative_difference']:.2f}% average difference")
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
