#!/usr/bin/env python3
"""
Analyze Comparison Results

This script analyzes the real comparison.xlsx file to understand prediction performance
and identify patterns for improvement.
"""

import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_comparison_file():
    """Analyze the comparison Excel file."""
    print("📊 Real Comparison.xlsx Analysis")
    print("=" * 60)
    
    try:
        # Read the comparison file
        file_path = "results/comparison/Comparison.xlsx"
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return
        
        # Try reading all sheets
        try:
            df_dict = pd.read_excel(file_path, sheet_name=None)
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            return
        
        all_results = []
        
        for sheet_name, data in df_dict.items():
            print(f"\n📋 Analyzing Sheet: {sheet_name}")
            print("-" * 40)
            
            # Clean up the data
            data_clean = clean_comparison_data(data)
            
            if data_clean is not None and len(data_clean) > 0:
                # Analyze this sheet
                results = analyze_sheet_performance(data_clean, sheet_name)
                if results:
                    all_results.append(results)
            else:
                print("⚠️ No valid data found in this sheet")
        
        # Overall analysis
        if all_results:
            print(f"\n🎯 OVERALL ANALYSIS ACROSS ALL PRODUCTS")
            print("=" * 60)
            analyze_overall_performance(all_results)
            
            print(f"\n💡 IMPROVEMENT RECOMMENDATIONS")
            print("=" * 60)
            provide_improvement_recommendations(all_results)
        
    except Exception as e:
        print(f"❌ Error analyzing comparison file: {e}")
        import traceback
        traceback.print_exc()

def clean_comparison_data(data):
    """Clean and standardize comparison data."""
    try:
        # Make a copy
        df = data.copy()
        
        # Check if first row contains headers
        first_row = df.iloc[0].astype(str)
        if any(keyword in first_row.str.upper().str.join(' ') for keyword in ['DATE', 'QUANTITY', 'PREDICTED']):
            df.columns = df.iloc[0]
            df = df.drop(0).reset_index(drop=True)
        
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Look for date, quantity, and predicted columns
        date_col = None
        quantity_col = None
        predicted_col = None
        
        for col in df.columns:
            col_str = str(col).upper()
            if 'DATE' in col_str:
                date_col = col
            elif 'QUANTITY' in col_str:
                quantity_col = col
            elif 'PREDICTED' in col_str or 'FORECAST' in col_str:
                predicted_col = col
        
        if not all([date_col, quantity_col, predicted_col]):
            # Try alternative approach - assume columns are in order
            if len(df.columns) >= 3:
                date_col = df.columns[0]
                quantity_col = df.columns[1] 
                predicted_col = df.columns[2]
            else:
                return None
        
        # Clean the data
        df = df[[date_col, quantity_col, predicted_col]].copy()
        df.columns = ['Date', 'Quantity', 'Predicted']
        
        # Convert to numeric
        df['Quantity'] = pd.to_numeric(df['Quantity'], errors='coerce')
        df['Predicted'] = pd.to_numeric(df['Predicted'], errors='coerce')
        
        # Remove rows with NaN values
        df = df.dropna()
        
        # Convert dates
        try:
            df['Date'] = pd.to_datetime(df['Date'])
        except:
            pass  # Keep as is if date conversion fails
        
        return df if len(df) > 0 else None
        
    except Exception as e:
        print(f"Error cleaning data: {e}")
        return None

def analyze_sheet_performance(data, sheet_name):
    """Analyze prediction performance for a single sheet."""
    try:
        actual = data['Quantity']
        predicted = data['Predicted']
        
        if len(actual) == 0 or len(predicted) == 0:
            return None
        
        # Calculate metrics
        mae = np.mean(np.abs(actual - predicted))
        mape = np.mean(np.abs((actual - predicted) / (actual + 1e-6))) * 100
        rmse = np.sqrt(np.mean((actual - predicted) ** 2))
        
        # Calculate error percentages
        error_pcts = np.abs((actual - predicted) / (actual + 1e-6)) * 100
        
        # Remove infinite values for analysis
        error_pcts_clean = error_pcts[np.isfinite(error_pcts)]
        
        if len(error_pcts_clean) == 0:
            return None
        
        # Categorize predictions
        excellent = (error_pcts_clean < 10).sum()
        good = ((error_pcts_clean >= 10) & (error_pcts_clean < 20)).sum()
        fair = ((error_pcts_clean >= 20) & (error_pcts_clean < 30)).sum()
        poor = (error_pcts_clean >= 30).sum()
        
        total_predictions = len(error_pcts_clean)
        
        print(f"📊 {sheet_name} Performance:")
        print(f"   Predictions analyzed: {total_predictions}")
        print(f"   MAE: {mae:,.0f}")
        print(f"   MAPE: {np.median(error_pcts_clean):.1f}% (median)")
        print(f"   RMSE: {rmse:,.0f}")
        
        print(f"\n🎯 Quality Distribution:")
        print(f"   Excellent (<10%): {excellent}/{total_predictions} ({excellent/total_predictions*100:.1f}%)")
        print(f"   Good (10-20%): {good}/{total_predictions} ({good/total_predictions*100:.1f}%)")
        print(f"   Fair (20-30%): {fair}/{total_predictions} ({fair/total_predictions*100:.1f}%)")
        print(f"   Poor (>30%): {poor}/{total_predictions} ({poor/total_predictions*100:.1f}%)")
        
        # Find best and worst predictions
        if len(error_pcts_clean) > 0:
            best_error = error_pcts_clean.min()
            worst_error = error_pcts_clean.max()

            # Find indices safely
            best_mask = error_pcts == best_error
            worst_mask = error_pcts == worst_error

            if best_mask.any():
                best_idx = best_mask.idxmax()
                print(f"\n✅ Best: Actual={actual.iloc[best_idx]:,.0f}, Predicted={predicted.iloc[best_idx]:,.0f} ({best_error:.1f}% error)")

            if worst_mask.any():
                worst_idx = worst_mask.idxmax()
                print(f"❌ Worst: Actual={actual.iloc[worst_idx]:,.0f}, Predicted={predicted.iloc[worst_idx]:,.0f} ({worst_error:.1f}% error)")
        
        # Analyze patterns
        print(f"\n📈 Pattern Analysis:")
        
        # Trend analysis
        if len(actual) > 1:
            actual_trend = np.polyfit(range(len(actual)), actual, 1)[0]
            predicted_trend = np.polyfit(range(len(predicted)), predicted, 1)[0]
            print(f"   Actual trend: {actual_trend:+.0f} units/period")
            print(f"   Predicted trend: {predicted_trend:+.0f} units/period")
            
            trend_accuracy = 1 - abs(actual_trend - predicted_trend) / (abs(actual_trend) + 1e-6)
            print(f"   Trend accuracy: {trend_accuracy:.1%}")
        
        # Volatility analysis
        actual_cv = actual.std() / (actual.mean() + 1e-6)
        predicted_cv = predicted.std() / (predicted.mean() + 1e-6)
        print(f"   Actual volatility (CV): {actual_cv:.2f}")
        print(f"   Predicted volatility (CV): {predicted_cv:.2f}")
        
        # Return results for overall analysis
        return {
            'sheet_name': sheet_name,
            'total_predictions': total_predictions,
            'mae': mae,
            'mape_median': np.median(error_pcts_clean),
            'rmse': rmse,
            'excellent_pct': excellent/total_predictions*100,
            'good_pct': good/total_predictions*100,
            'fair_pct': fair/total_predictions*100,
            'poor_pct': poor/total_predictions*100,
            'best_error': error_pcts_clean.min(),
            'worst_error': error_pcts_clean.max(),
            'actual_mean': actual.mean(),
            'predicted_mean': predicted.mean(),
            'actual_cv': actual_cv,
            'predicted_cv': predicted_cv,
            'data': data
        }
        
    except Exception as e:
        print(f"Error analyzing sheet {sheet_name}: {e}")
        return None

def analyze_overall_performance(results):
    """Analyze overall performance across all products."""
    
    # Calculate weighted averages
    total_predictions = sum(r['total_predictions'] for r in results)
    
    weighted_mape = sum(r['mape_median'] * r['total_predictions'] for r in results) / total_predictions
    weighted_excellent = sum(r['excellent_pct'] * r['total_predictions'] for r in results) / total_predictions / 100
    weighted_poor = sum(r['poor_pct'] * r['total_predictions'] for r in results) / total_predictions / 100
    
    print(f"📊 Overall Performance Summary:")
    print(f"   Total products analyzed: {len(results)}")
    print(f"   Total predictions: {total_predictions}")
    print(f"   Weighted average MAPE: {weighted_mape:.1f}%")
    print(f"   Excellent predictions: {weighted_excellent:.1%}")
    print(f"   Poor predictions: {weighted_poor:.1%}")
    
    # Best and worst performing products
    best_product = min(results, key=lambda x: x['mape_median'])
    worst_product = max(results, key=lambda x: x['mape_median'])
    
    print(f"\n🏆 Best Performing Product:")
    print(f"   {best_product['sheet_name']}: {best_product['mape_median']:.1f}% MAPE")
    
    print(f"\n⚠️ Worst Performing Product:")
    print(f"   {worst_product['sheet_name']}: {worst_product['mape_median']:.1f}% MAPE")
    
    # Identify patterns
    print(f"\n🔍 Performance Patterns:")
    
    # High vs low volume products
    high_volume = [r for r in results if r['actual_mean'] > 50000]
    low_volume = [r for r in results if r['actual_mean'] <= 50000]
    
    if high_volume and low_volume:
        high_vol_mape = np.mean([r['mape_median'] for r in high_volume])
        low_vol_mape = np.mean([r['mape_median'] for r in low_volume])
        
        print(f"   High volume products (>{50000:,}): {high_vol_mape:.1f}% avg MAPE")
        print(f"   Low volume products (≤{50000:,}): {low_vol_mape:.1f}% avg MAPE")
    
    # Volatile vs stable products
    high_volatility = [r for r in results if r['actual_cv'] > 0.5]
    low_volatility = [r for r in results if r['actual_cv'] <= 0.5]
    
    if high_volatility and low_volatility:
        high_vol_mape = np.mean([r['mape_median'] for r in high_volatility])
        low_vol_mape = np.mean([r['mape_median'] for r in low_volatility])
        
        print(f"   High volatility products (CV>0.5): {high_vol_mape:.1f}% avg MAPE")
        print(f"   Low volatility products (CV≤0.5): {low_vol_mape:.1f}% avg MAPE")

def provide_improvement_recommendations(results):
    """Provide specific recommendations for improvement."""
    
    # Identify problem areas
    poor_performers = [r for r in results if r['mape_median'] > 30]
    high_volatility = [r for r in results if r['actual_cv'] > 0.5]
    
    print(f"🎯 Specific Improvement Areas:")
    
    if poor_performers:
        print(f"\n1. 📉 Poor Performing Products ({len(poor_performers)} products):")
        for product in poor_performers[:3]:  # Show top 3
            print(f"   - {product['sheet_name']}: {product['mape_median']:.1f}% MAPE")
        
        print(f"   💡 Recommendations:")
        print(f"   - Increase training data for these products")
        print(f"   - Add product-specific external factors")
        print(f"   - Consider ensemble methods")
        print(f"   - Review data quality and outliers")
    
    if high_volatility:
        print(f"\n2. 📊 High Volatility Products ({len(high_volatility)} products):")
        for product in high_volatility[:3]:  # Show top 3
            print(f"   - {product['sheet_name']}: CV={product['actual_cv']:.2f}")
        
        print(f"   💡 Recommendations:")
        print(f"   - Use uncertainty quantification")
        print(f"   - Implement adaptive learning")
        print(f"   - Add volatility-specific features")
        print(f"   - Consider regime-switching models")
    
    # General recommendations
    print(f"\n3. 🔧 General Improvements:")
    
    avg_excellent = np.mean([r['excellent_pct'] for r in results])
    if avg_excellent < 50:
        print(f"   - Only {avg_excellent:.1f}% excellent predictions - target >50%")
        print(f"   - Enhance feature engineering")
        print(f"   - Optimize hyperparameters per product")
    
    avg_poor = np.mean([r['poor_pct'] for r in results])
    if avg_poor > 20:
        print(f"   - {avg_poor:.1f}% poor predictions - target <20%")
        print(f"   - Implement outlier detection")
        print(f"   - Add data validation checks")
    
    print(f"\n4. 🚀 Advanced Techniques to Try:")
    print(f"   - Multi-task learning across similar products")
    print(f"   - Transfer learning from high-performing products")
    print(f"   - Hierarchical forecasting (customer → product)")
    print(f"   - Dynamic model selection based on recent performance")
    print(f"   - Ensemble of TCI-fix + other models")

if __name__ == "__main__":
    analyze_comparison_file()
