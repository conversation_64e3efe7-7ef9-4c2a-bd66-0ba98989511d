"""
Run a working comparison between TCI baseline and an extension.

This script runs a comparison between the TCI baseline agent (without DynaQ) and an agent with an extension.
"""

import os
import sys
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

import tci
from tci.environments import ChemEnv
from tci.extensions import GAT

def train_and_evaluate(agent, env, episodes=100, max_steps=200, eval_interval=10):
    """Train and evaluate an agent."""
    metrics = tci.train(
        agent=agent,
        env=env,
        episodes=episodes,
        max_steps=max_steps,
        batch_size=32,
        gamma=0.99,
        update_interval=1,
        eval_interval=eval_interval,
        verbose=True
    )
    return metrics

def main():
    """Run a comparison between TCI baseline and an extension."""
    # Set device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # Create environment
    env = ChemEnv(seed=42)
    print(f"Created environment with state_dim={env.state_dim}, action_dim={env.action_dim}")

    # Create TCI baseline agent (without DynaQ)
    baseline_agent = tci.TCIAgent(
        state_dim=env.state_dim,
        action_dim=env.action_dim,
        device=device,
        include_dynaq=False,  # Disable DynaQ to avoid the error
        include_experience_replay=True
    )

    # Create TCI agent with GAT extension
    gat_agent = tci.TCIAgent(
        state_dim=env.state_dim,
        action_dim=env.action_dim,
        device=device,
        include_dynaq=False,  # Disable DynaQ to avoid the error
        include_experience_replay=True,
        extensions=[GAT(hidden_dim=32, output_dim=64)]
    )

    # Train and evaluate baseline agent
    print("Training baseline agent...")
    baseline_metrics = train_and_evaluate(baseline_agent, env)

    # Train and evaluate GAT agent
    print("Training GAT agent...")
    gat_metrics = train_and_evaluate(gat_agent, env)

    # Plot results
    plt.figure(figsize=(10, 6))
    plt.plot(baseline_metrics["eval_rewards"], label="Baseline")
    plt.plot(gat_metrics["eval_rewards"], label="GAT")
    plt.title("TCI Agent Performance Comparison")
    plt.xlabel("Evaluation Step")
    plt.ylabel("Reward")
    plt.legend()
    plt.grid(True)
    plt.savefig("comparison_results.png")

    # Calculate statistics
    baseline_final = baseline_metrics["eval_rewards"][-1]
    gat_final = gat_metrics["eval_rewards"][-1]
    improvement = (gat_final - baseline_final) / baseline_final * 100

    print("Training complete!")
    print(f"Final baseline reward: {baseline_final:.2f}")
    print(f"Final GAT reward: {gat_final:.2f}")
    print(f"Improvement: {improvement:.2f}%")
    print(f"Results saved to comparison_results.png")

if __name__ == "__main__":
    main()
