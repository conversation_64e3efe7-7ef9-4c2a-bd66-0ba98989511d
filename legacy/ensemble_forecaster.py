"""
Ensemble Forecaster for Time Series Prediction

This module implements a pattern-based ensemble forecasting approach that combines
multiple forecasting methods to produce more accurate predictions.
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
from sklearn.metrics import mean_absolute_error, mean_squared_error

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsembleForecaster:
    """
    A pattern-based ensemble forecaster that combines multiple forecasting methods.
    """
    
    def __init__(self):
        """Initialize the ensemble forecaster."""
        self.name = "Pattern-Based Ensemble Forecaster"
        self.models = {}
        self.weights = {}
        self.historical_data = None
        self.forecast_data = None
    
    def fit(self, historical_data):
        """
        Fit the ensemble forecaster to historical data.
        
        Args:
            historical_data (pd.DataFrame): Historical data with 'ds' (date) and 'y' (target) columns
        """
        self.historical_data = historical_data.copy()
        
        # Extract patterns from historical data
        self.patterns = self._extract_patterns(historical_data)
        
        # Generate forecasts from different methods
        self._generate_component_forecasts()
        
        # Calculate weights based on historical performance
        self._calculate_weights()
        
        return self
    
    def predict(self, periods=36):
        """
        Generate forecasts for future periods.
        
        Args:
            periods (int): Number of periods to forecast
            
        Returns:
            pd.DataFrame: Forecast dataframe with ds, yhat, yhat_lower, yhat_upper columns
        """
        if self.historical_data is None:
            raise ValueError("Model has not been fit to historical data")
        
        # Generate future dates
        last_date = self.historical_data['ds'].max()
        future_dates = pd.date_range(start=last_date + pd.Timedelta(days=30), periods=periods, freq='MS')
        
        # Create forecast dataframe
        forecast = pd.DataFrame({'ds': future_dates})
        
        # Generate component forecasts
        component_forecasts = {}
        for name, model in self.models.items():
            component_forecasts[name] = model(self.historical_data, future_dates)
        
        # Combine forecasts using weights
        forecast['yhat'] = 0
        for name, component in component_forecasts.items():
            forecast['yhat'] += component['yhat'] * self.weights.get(name, 1/len(self.models))
        
        # Apply post-processing adjustments
        forecast = self._apply_post_processing(forecast)
        
        # Add prediction intervals
        forecast['yhat_lower'] = forecast['yhat'] * 0.8
        forecast['yhat_upper'] = forecast['yhat'] * 1.2
        
        self.forecast_data = forecast
        return forecast
    
    def _extract_patterns(self, data):
        """
        Extract patterns from historical data.
        
        Args:
            data (pd.DataFrame): Historical data
            
        Returns:
            dict: Dictionary of patterns
        """
        patterns = {}
        
        # Calculate basic statistics
        patterns['mean'] = data['y'].mean()
        patterns['median'] = data['y'].median()
        patterns['std'] = data['y'].std()
        patterns['min'] = data['y'].min()
        patterns['max'] = data['y'].max()
        patterns['coefficient_of_variation'] = patterns['std'] / patterns['mean'] if patterns['mean'] > 0 else 0
        
        # Extract monthly patterns
        data['month'] = data['ds'].dt.month
        monthly_patterns = {}
        for month in range(1, 13):
            month_data = data[data['month'] == month]
            if len(month_data) > 0:
                monthly_patterns[month] = {
                    'mean': month_data['y'].mean(),
                    'median': month_data['y'].median(),
                    'std': month_data['y'].std(),
                    'min': month_data['y'].min(),
                    'max': month_data['y'].max(),
                    'count': len(month_data),
                    'seasonal_factor': month_data['y'].mean() / patterns['mean'] if patterns['mean'] > 0 else 1.0
                }
        patterns['monthly'] = monthly_patterns
        
        # Extract year-over-year growth
        data['year'] = data['ds'].dt.year
        yearly_means = data.groupby('year')['y'].mean()
        if len(yearly_means) > 1:
            yearly_growth = []
            for i in range(1, len(yearly_means)):
                if yearly_means.iloc[i-1] > 0:
                    growth = (yearly_means.iloc[i] - yearly_means.iloc[i-1]) / yearly_means.iloc[i-1]
                    yearly_growth.append(growth)
            patterns['yearly_growth'] = np.mean(yearly_growth) if yearly_growth else 0
        else:
            patterns['yearly_growth'] = 0
        
        # Extract autocorrelation
        if len(data) > 12:
            autocorr = []
            for i in range(1, 13):
                if i < len(data):
                    corr = data['y'].autocorr(lag=i)
                    autocorr.append(corr)
            patterns['autocorrelation'] = autocorr
        else:
            patterns['autocorrelation'] = [0] * min(12, len(data))
        
        # Detect seasonality
        patterns['monthly_seasonality'] = self._detect_seasonality(data, 'month')
        patterns['quarterly_seasonality'] = self._detect_seasonality(data, 'quarter')
        
        return patterns
    
    def _detect_seasonality(self, data, period_type):
        """
        Detect seasonality in the data.
        
        Args:
            data (pd.DataFrame): Historical data
            period_type (str): Type of period ('month' or 'quarter')
            
        Returns:
            bool: True if seasonality is detected, False otherwise
        """
        if period_type == 'month':
            data['period'] = data['ds'].dt.month
        elif period_type == 'quarter':
            data['period'] = data['ds'].dt.quarter
        else:
            return False
        
        if len(data) < 24:  # Need at least 2 years of data
            return False
        
        # Calculate mean by period
        period_means = data.groupby('period')['y'].mean()
        
        # Calculate coefficient of variation of period means
        cv = period_means.std() / period_means.mean() if period_means.mean() > 0 else 0
        
        # If CV is high enough, consider it seasonal
        return cv > 0.1
    
    def _generate_component_forecasts(self):
        """Generate component forecasts using different methods."""
        # Define component forecast methods
        self.models = {
            'pattern_copy': self._pattern_copy_forecast,
            'seasonal_naive': self._seasonal_naive_forecast,
            'trend_adjusted': self._trend_adjusted_forecast,
            'average_pattern': self._average_pattern_forecast
        }
    
    def _calculate_weights(self):
        """Calculate weights for component forecasts based on historical performance."""
        # For now, use equal weights
        num_models = len(self.models)
        self.weights = {name: 1.0 / num_models for name in self.models.keys()}
        
        # TODO: Implement more sophisticated weight calculation based on historical performance
    
    def _pattern_copy_forecast(self, historical_data, future_dates):
        """
        Generate forecasts by copying patterns from historical data.
        
        Args:
            historical_data (pd.DataFrame): Historical data
            future_dates (pd.DatetimeIndex): Future dates to forecast
            
        Returns:
            pd.DataFrame: Forecast dataframe
        """
        forecast = pd.DataFrame({'ds': future_dates})
        
        # Find the most recent complete year
        max_year = historical_data['ds'].dt.year.max()
        min_year = historical_data['ds'].dt.year.min()
        
        # Check if we have complete years
        years_with_data = historical_data.groupby(historical_data['ds'].dt.year)['y'].count()
        complete_years = years_with_data[years_with_data >= 12].index.tolist()
        
        if complete_years:
            # Use the most recent complete year
            reference_year = max(complete_years)
        else:
            # Use the most recent year
            reference_year = max_year
        
        # Get the reference year data
        reference_data = historical_data[historical_data['ds'].dt.year == reference_year].copy()
        
        # If reference data is not enough, use all available data
        if len(reference_data) < 6:
            logger.warning(f"Not enough data in reference year {reference_year}, using all available data")
            reference_data = historical_data.copy()
        
        # Create a mapping of month to value
        month_to_value = {}
        for _, row in reference_data.iterrows():
            month = row['ds'].month
            month_to_value[month] = row['y']
        
        # Fill in missing months with the median
        median_value = reference_data['y'].median()
        for month in range(1, 13):
            if month not in month_to_value:
                month_to_value[month] = median_value
        
        # Generate forecasts based on month patterns
        forecast['yhat'] = forecast['ds'].dt.month.map(month_to_value)
        
        # Apply random variations to make it more realistic
        np.random.seed(42)  # For reproducibility
        variations = np.random.normal(1.0, 0.1, len(forecast))
        forecast['yhat'] = forecast['yhat'] * variations
        
        # Apply trend if detected
        if self.patterns.get('yearly_growth', 0) != 0:
            years_ahead = forecast['ds'].dt.year - max_year
            forecast['yhat'] = forecast['yhat'] * (1 + self.patterns['yearly_growth']) ** years_ahead
        
        # Ensure non-negative values
        forecast['yhat'] = forecast['yhat'].clip(lower=0)
        
        return forecast
    
    def _seasonal_naive_forecast(self, historical_data, future_dates):
        """
        Generate forecasts using the seasonal naive method.
        
        Args:
            historical_data (pd.DataFrame): Historical data
            future_dates (pd.DatetimeIndex): Future dates to forecast
            
        Returns:
            pd.DataFrame: Forecast dataframe
        """
        forecast = pd.DataFrame({'ds': future_dates})
        
        # Calculate seasonal factors
        historical_data['month'] = historical_data['ds'].dt.month
        seasonal_factors = historical_data.groupby('month')['y'].mean()
        seasonal_factors = seasonal_factors / seasonal_factors.mean()
        
        # Apply seasonal factors
        forecast['month'] = forecast['ds'].dt.month
        forecast['seasonal_factor'] = forecast['month'].map(seasonal_factors)
        
        # Calculate base value (average of last 12 months)
        last_year_data = historical_data.sort_values('ds').tail(12)
        base_value = last_year_data['y'].mean()
        
        # Generate forecasts
        forecast['yhat'] = base_value * forecast['seasonal_factor']
        
        # Apply trend if detected
        if self.patterns.get('yearly_growth', 0) != 0:
            max_year = historical_data['ds'].dt.year.max()
            years_ahead = forecast['ds'].dt.year - max_year
            forecast['yhat'] = forecast['yhat'] * (1 + self.patterns['yearly_growth']) ** years_ahead
        
        # Drop intermediate columns
        forecast = forecast.drop(['month', 'seasonal_factor'], axis=1)
        
        # Ensure non-negative values
        forecast['yhat'] = forecast['yhat'].clip(lower=0)
        
        return forecast
    
    def _trend_adjusted_forecast(self, historical_data, future_dates):
        """
        Generate forecasts using trend-adjusted method.
        
        Args:
            historical_data (pd.DataFrame): Historical data
            future_dates (pd.DatetimeIndex): Future dates to forecast
            
        Returns:
            pd.DataFrame: Forecast dataframe
        """
        forecast = pd.DataFrame({'ds': future_dates})
        
        # Calculate trend
        historical_data = historical_data.sort_values('ds')
        historical_data['t'] = range(len(historical_data))
        
        # Simple linear regression
        slope, intercept, _, _, _ = stats.linregress(historical_data['t'], historical_data['y'])
        
        # Calculate base value (last observed value)
        base_value = historical_data['y'].iloc[-1]
        
        # Generate forecasts
        forecast['t'] = range(len(historical_data), len(historical_data) + len(forecast))
        forecast['trend'] = intercept + slope * forecast['t']
        
        # Apply seasonal factors
        historical_data['month'] = historical_data['ds'].dt.month
        seasonal_factors = historical_data.groupby('month')['y'].mean() / historical_data['y'].mean()
        
        forecast['month'] = forecast['ds'].dt.month
        forecast['seasonal_factor'] = forecast['month'].map(seasonal_factors)
        forecast['seasonal_factor'] = forecast['seasonal_factor'].fillna(1.0)
        
        # Combine trend and seasonality
        forecast['yhat'] = forecast['trend'] * forecast['seasonal_factor']
        
        # Drop intermediate columns
        forecast = forecast.drop(['t', 'trend', 'month', 'seasonal_factor'], axis=1)
        
        # Ensure non-negative values
        forecast['yhat'] = forecast['yhat'].clip(lower=0)
        
        return forecast
    
    def _average_pattern_forecast(self, historical_data, future_dates):
        """
        Generate forecasts using average patterns.
        
        Args:
            historical_data (pd.DataFrame): Historical data
            future_dates (pd.DatetimeIndex): Future dates to forecast
            
        Returns:
            pd.DataFrame: Forecast dataframe
        """
        forecast = pd.DataFrame({'ds': future_dates})
        
        # Calculate monthly patterns
        historical_data['month'] = historical_data['ds'].dt.month
        monthly_patterns = {}
        
        for month in range(1, 13):
            month_data = historical_data[historical_data['month'] == month]
            if len(month_data) > 0:
                # Use weighted average with more weight on recent data
                weights = np.linspace(0.5, 1.0, len(month_data))
                weights = weights / weights.sum()
                monthly_patterns[month] = np.average(month_data['y'], weights=weights)
            else:
                monthly_patterns[month] = historical_data['y'].mean()
        
        # Apply monthly patterns
        forecast['month'] = forecast['ds'].dt.month
        forecast['yhat'] = forecast['month'].map(monthly_patterns)
        
        # Apply trend if detected
        if self.patterns.get('yearly_growth', 0) != 0:
            max_year = historical_data['ds'].dt.year.max()
            years_ahead = forecast['ds'].dt.year - max_year
            forecast['yhat'] = forecast['yhat'] * (1 + self.patterns['yearly_growth']) ** years_ahead
        
        # Drop intermediate columns
        forecast = forecast.drop(['month'], axis=1)
        
        # Ensure non-negative values
        forecast['yhat'] = forecast['yhat'].clip(lower=0)
        
        return forecast
    
    def _apply_post_processing(self, forecast):
        """
        Apply post-processing adjustments to the forecast.
        
        Args:
            forecast (pd.DataFrame): Forecast dataframe
            
        Returns:
            pd.DataFrame: Adjusted forecast dataframe
        """
        # Apply product-specific adjustments
        forecast = self._apply_product_specific_adjustments(forecast)
        
        # Apply domain constraints
        forecast = self._apply_domain_constraints(forecast)
        
        return forecast
    
    def _apply_product_specific_adjustments(self, forecast):
        """
        Apply product-specific adjustments to the forecast.
        
        Args:
            forecast (pd.DataFrame): Forecast dataframe
            
        Returns:
            pd.DataFrame: Adjusted forecast dataframe
        """
        # Get monthly patterns
        monthly_patterns = self.patterns.get('monthly', {})
        
        # Apply adjustments based on monthly patterns
        forecast['month'] = forecast['ds'].dt.month
        
        # Adjust December values (typically very low)
        dec_factor = monthly_patterns.get(12, {}).get('seasonal_factor', 0.5)
        forecast.loc[forecast['month'] == 12, 'yhat'] = forecast.loc[forecast['month'] == 12, 'yhat'] * dec_factor
        
        # Adjust September-November values (typically very high)
        for month in [9, 10, 11]:
            month_factor = monthly_patterns.get(month, {}).get('seasonal_factor', 1.5)
            forecast.loc[forecast['month'] == month, 'yhat'] = forecast.loc[forecast['month'] == month, 'yhat'] * month_factor
        
        # Drop intermediate columns
        forecast = forecast.drop(['month'], axis=1)
        
        return forecast
    
    def _apply_domain_constraints(self, forecast):
        """
        Apply domain constraints to the forecast.
        
        Args:
            forecast (pd.DataFrame): Forecast dataframe
            
        Returns:
            pd.DataFrame: Adjusted forecast dataframe
        """
        # Ensure values are within historical range
        min_value = max(0, self.patterns.get('min', 0) * 0.5)
        max_value = self.patterns.get('max', forecast['yhat'].max()) * 1.5
        
        forecast['yhat'] = forecast['yhat'].clip(lower=min_value, upper=max_value)
        
        # Round values to nearest 100
        forecast['yhat'] = np.round(forecast['yhat'] / 100) * 100
        
        return forecast
    
    def plot_forecast(self, include_history=True, figsize=(12, 6)):
        """
        Plot the forecast.
        
        Args:
            include_history (bool): Whether to include historical data in the plot
            figsize (tuple): Figure size
            
        Returns:
            matplotlib.figure.Figure: Figure object
        """
        if self.forecast_data is None:
            raise ValueError("No forecast data available. Call predict() first.")
        
        fig, ax = plt.subplots(figsize=figsize)
        
        # Plot historical data
        if include_history and self.historical_data is not None:
            ax.plot(self.historical_data['ds'], self.historical_data['y'], 'b-', label='Historical')
        
        # Plot forecast
        ax.plot(self.forecast_data['ds'], self.forecast_data['yhat'], 'r-', label='Forecast')
        
        # Plot confidence intervals
        ax.fill_between(self.forecast_data['ds'], 
                        self.forecast_data['yhat_lower'], 
                        self.forecast_data['yhat_upper'], 
                        color='r', alpha=0.2)
        
        # Add labels and title
        ax.set_xlabel('Date')
        ax.set_ylabel('Value')
        ax.set_title('Forecast')
        ax.legend()
        ax.grid(True)
        
        return fig
    
    def evaluate(self, test_data):
        """
        Evaluate the forecast against test data.
        
        Args:
            test_data (pd.DataFrame): Test data with 'ds' and 'y' columns
            
        Returns:
            dict: Dictionary of evaluation metrics
        """
        if self.forecast_data is None:
            raise ValueError("No forecast data available. Call predict() first.")
        
        # Merge forecast with test data
        merged = pd.merge(test_data, self.forecast_data, on='ds', how='inner')
        
        if len(merged) == 0:
            raise ValueError("No overlapping dates between forecast and test data")
        
        # Calculate metrics
        metrics = {
            'mae': mean_absolute_error(merged['y'], merged['yhat']),
            'rmse': np.sqrt(mean_squared_error(merged['y'], merged['yhat'])),
            'mape': np.mean(np.abs((merged['y'] - merged['yhat']) / merged['y'])) * 100 if (merged['y'] > 0).all() else np.nan
        }
        
        return metrics
