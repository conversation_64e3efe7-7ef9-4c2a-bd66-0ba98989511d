#!/usr/bin/env python3
"""
A/B Testing for TCI with Tennessee Eastman Process (TEP) Dataset

This script performs A/B testing of TCI agents on the TEP dataset,
comparing different configurations and providing statistical analysis.
"""

import os
import sys
import numpy as np
import torch
import scipy.stats as stats
import json
import time
import argparse
# Type hints for function signatures

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tci.environments import TEPEnv
from tci.core.agent_fix import FixedTCIAgent
from tci.core.causal_discovery import infer_causal_graph

def evaluate_agent(agent, env, n_episodes=3, max_steps=100, reduced_state_dim=None):
    """
    Evaluate an agent on the TEP environment.

    Parameters
    ----------
    agent : FixedTCIAgent
        Agent to evaluate
    env : TEPEnv
        Environment to evaluate in
    n_episodes : int, optional
        Number of episodes to evaluate, by default 3
    max_steps : int, optional
        Maximum steps per episode, by default 100
    reduced_state_dim : int, optional
        Reduced state dimension for the agent, by default None

    Returns
    -------
    float
        Mean reward
    """
    total_reward = 0

    for _ in range(n_episodes):
        state = env.reset()
        episode_reward = 0
        done = False
        step_count = 0

        while not done and step_count < max_steps:
            # Reduce state dimension if needed
            if reduced_state_dim is not None and len(state) > reduced_state_dim:
                # Use the first reduced_state_dim elements (most important variables)
                reduced_state = state[:reduced_state_dim]
            else:
                reduced_state = state

            # Select action (without exploration)
            with torch.no_grad():
                action = agent.act(reduced_state)

            # Take action
            next_state, reward, done = env.step(action)

            # Update state and reward
            state = next_state
            episode_reward += reward
            step_count += 1

            if done:
                break

        total_reward += episode_reward

    return total_reward / n_episodes

def train_and_evaluate(agent, env, episodes=20, max_steps=100, eval_interval=5, reward_scale=0.01, reduced_state_dim=None):
    """
    Train and evaluate an agent on the TEP environment.

    Parameters
    ----------
    agent : FixedTCIAgent
        Agent to train and evaluate
    env : TEPEnv
        Environment to train and evaluate in
    episodes : int, optional
        Number of episodes to train, by default 20
    max_steps : int, optional
        Maximum steps per episode, by default 100
    eval_interval : int, optional
        Interval for evaluation, by default 5
    reward_scale : float, optional
        Factor to scale rewards for numerical stability, by default 0.01
    reduced_state_dim : int, optional
        Reduced state dimension for the agent, by default None

    Returns
    -------
    dict
        Dictionary with metrics
    """
    eval_rewards = []
    train_rewards = []
    losses = []
    nan_losses = 0

    start_time = time.time()

    for episode in range(episodes):
        state = env.reset()
        episode_reward = 0

        for _ in range(max_steps):
            # Reduce state dimension if needed
            if reduced_state_dim is not None and len(state) > reduced_state_dim:
                # Use the first reduced_state_dim elements (most important variables)
                reduced_state = state[:reduced_state_dim]
            else:
                reduced_state = state

            # Select action using reduced state
            action = agent.act(reduced_state)

            # Take action
            next_state, reward, done = env.step(action)

            # Reduce next state dimension if needed
            if reduced_state_dim is not None and len(next_state) > reduced_state_dim:
                reduced_next_state = next_state[:reduced_state_dim]
            else:
                reduced_next_state = next_state

            # Store experience with reduced states
            scaled_reward = reward * reward_scale
            agent.store(reduced_state, action, scaled_reward, reduced_next_state)

            # Update state and reward
            state = next_state
            episode_reward += reward

            if done:
                break

        # Train agent
        if len(agent.buffer) >= 32:
            # Apply gradient clipping to prevent exploding gradients
            torch.nn.utils.clip_grad_norm_(agent.bnn.parameters(), max_norm=1.0)

            loss_dict = agent.train(batch_size=32)

            # Check for NaN loss
            if np.isnan(loss_dict["bnn_loss"]):
                nan_losses += 1
                print(f"Warning: NaN loss detected in episode {episode}")

            losses.append(loss_dict["bnn_loss"])

        # Store training reward
        train_rewards.append(episode_reward)

        # Evaluate agent
        if (episode + 1) % eval_interval == 0:
            eval_reward = evaluate_agent(agent, env, n_episodes=3, max_steps=max_steps, reduced_state_dim=reduced_state_dim)
            eval_rewards.append(eval_reward)

            # Print progress
            print(f"Episode {episode + 1}: Train reward = {episode_reward:.2f}, Eval reward = {eval_reward:.2f}")

            # Print loss if available
            if losses:
                print(f"Loss: {losses[-1]:.4f}")

    # Calculate training time
    training_time = time.time() - start_time

    # Return metrics
    return {
        "eval_rewards": eval_rewards,
        "train_rewards": train_rewards,
        "losses": losses,
        "nan_losses": nan_losses,
        "nan_percentage": (nan_losses / len(losses)) * 100 if losses else 0,
        "training_time": training_time
    }

def save_model(agent, path):
    """
    Save a TCI agent model.

    Parameters
    ----------
    agent : FixedTCIAgent
        Agent to save
    path : str
        Path to save the model
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(path), exist_ok=True)

    # Save model state
    torch.save({
        'bnn_state_dict': agent.bnn.state_dict(),
        'vae_state_dict': agent.vae.state_dict(),
        'dynamics_model_state_dict': agent.dynamics_model.state_dict() if agent.dynamics_model else None,
        'state_dim': agent.state_dim,
        'action_dim': agent.action_dim,
        'hidden_dim': agent.hidden_dim,
        'latent_dim': agent.latent_dim,
        'history_length': agent.history_length,
        'include_dynaq': agent.include_dynaq,
        'include_experience_replay': agent.include_experience_replay,
        'Gt': agent.Gt,
        'causal_edges': agent.causal_edges
    }, path)

    print(f"Model saved to {path}")

def load_model(path, device="cpu"):
    """
    Load a TCI agent model.

    Parameters
    ----------
    path : str
        Path to load the model from
    device : str, optional
        Device to load the model on, by default "cpu"

    Returns
    -------
    FixedTCIAgent
        Loaded agent
    """
    # Load model state
    checkpoint = torch.load(path, map_location=device)

    # Create agent
    agent = FixedTCIAgent(
        state_dim=checkpoint['state_dim'],
        action_dim=checkpoint['action_dim'],
        device=device,
        hidden_dim=checkpoint['hidden_dim'],
        latent_dim=checkpoint['latent_dim'],
        history_length=checkpoint['history_length'],
        include_dynaq=checkpoint['include_dynaq'],
        include_experience_replay=checkpoint['include_experience_replay']
    )

    # Load state dictionaries
    agent.bnn.load_state_dict(checkpoint['bnn_state_dict'])
    agent.vae.load_state_dict(checkpoint['vae_state_dict'])

    if agent.dynamics_model and checkpoint['dynamics_model_state_dict']:
        agent.dynamics_model.load_state_dict(checkpoint['dynamics_model_state_dict'])

    # Load causal graph
    agent.Gt = checkpoint['Gt']
    agent.causal_edges = checkpoint['causal_edges']

    print(f"Model loaded from {path}")
    return agent

def run_single_seed(seed, args, train_data_path, test_data_path=None):
    """
    Run a single seed experiment with the TEP dataset.

    Parameters
    ----------
    seed : int
        Random seed
    args : argparse.Namespace
        Command-line arguments
    train_data_path : str
        Path to training data
    test_data_path : str, optional
        Path to testing data, by default None

    Returns
    -------
    dict
        Dictionary with results
    """
    print(f"\nRunning seed {seed}...")

    # Set random seed
    np.random.seed(seed)
    torch.manual_seed(seed)

    # Set device
    device = "cuda" if torch.cuda.is_available() and args.gpu else "cpu"
    print(f"Using device: {device}")

    # Create training environments with appropriate causal update intervals
    # Baseline environment
    baseline_train_env = TEPEnv(
        data=train_data_path,
        seed=seed,
        max_steps=args.max_steps,
        use_causal_reward=True,
        causal_update_interval=args.causal_update_interval
    )
    print(f"Created baseline training environment with state_dim={baseline_train_env.state_dim}, action_dim={baseline_train_env.action_dim}")
    print(f"Baseline causal graph updates every {args.causal_update_interval} steps (0 = disabled)")

    # Variant environment
    variant_train_env = TEPEnv(
        data=train_data_path,
        seed=seed,
        max_steps=args.max_steps,
        use_causal_reward=True,
        causal_update_interval=args.variant_causal_update_interval
    )
    print(f"Created variant training environment with state_dim={variant_train_env.state_dim}, action_dim={variant_train_env.action_dim}")
    print(f"Variant causal graph updates every {args.variant_causal_update_interval} steps (0 = disabled)")

    # Create testing environments if test data provided
    baseline_test_env = None
    variant_test_env = None
    if test_data_path:
        baseline_test_env = TEPEnv(
            data=test_data_path,
            seed=seed,
            max_steps=args.max_steps,
            use_causal_reward=True,
            causal_update_interval=args.causal_update_interval
        )
        print(f"Created baseline testing environment with state_dim={baseline_test_env.state_dim}, action_dim={baseline_test_env.action_dim}")

        variant_test_env = TEPEnv(
            data=test_data_path,
            seed=seed,
            max_steps=args.max_steps,
            use_causal_reward=True,
            causal_update_interval=args.variant_causal_update_interval
        )
        print(f"Created variant testing environment with state_dim={variant_test_env.state_dim}, action_dim={variant_test_env.action_dim}")

    # For TEP, we need to create a reduced state dimension version
    # The original state dimension (54) is too large for the BNN
    # We'll use a smaller state dimension (20) for the agent
    reduced_state_dim = 20  # Use a smaller state dimension for the agent
    print(f"Using reduced state dimension of {reduced_state_dim} (original: {baseline_train_env.state_dim})")

    # Create baseline agent
    baseline_agent = FixedTCIAgent(
        state_dim=reduced_state_dim,  # Use reduced state dimension
        action_dim=baseline_train_env.action_dim,
        device=device,
        include_dynaq=False,  # Disable DynaQ for TEP (too complex)
        include_experience_replay=True,
        buffer_size=1000,
        hidden_dim=args.hidden_dim,
        history_length=args.history_length,
        learning_rate=args.learning_rate,
        causal_discovery_params={
            "method": args.causal_method,
            "alpha": args.causal_alpha,
            "sparsity_factor": args.sparsity_factor
        }
    )
    print(f"Created baseline agent with hidden_dim={args.hidden_dim}, learning_rate={args.learning_rate}, history_length={args.history_length}")
    print(f"Baseline causal discovery method: {args.causal_method}, alpha={args.causal_alpha}")

    # Create variant agent with potentially different configuration
    variant_agent = FixedTCIAgent(
        state_dim=reduced_state_dim,  # Use reduced state dimension
        action_dim=variant_train_env.action_dim,
        device=device,
        include_dynaq=False,  # Disable DynaQ for TEP (too complex)
        include_experience_replay=True,
        buffer_size=1000,
        hidden_dim=args.variant_hidden_dim,
        history_length=args.variant_history_length,
        learning_rate=args.variant_learning_rate,
        causal_discovery_params={
            "method": args.variant_causal_method,
            "alpha": args.variant_causal_alpha,
            "sparsity_factor": args.variant_sparsity_factor
        }
    )
    print(f"Created variant agent with hidden_dim={args.variant_hidden_dim}, learning_rate={args.variant_learning_rate}, history_length={args.variant_history_length}")
    print(f"Variant causal discovery method: {args.variant_causal_method}, alpha={args.variant_causal_alpha}")

    # Train and evaluate baseline agent
    print(f"Training baseline agent for {args.episodes} episodes...")
    baseline_metrics = train_and_evaluate(
        agent=baseline_agent,
        env=baseline_train_env,
        episodes=args.episodes,
        max_steps=args.max_steps,
        eval_interval=args.eval_interval,
        reward_scale=args.reward_scale,
        reduced_state_dim=reduced_state_dim  # Pass reduced state dimension
    )

    # Save baseline model
    if args.save_models:
        save_model(
            baseline_agent,
            os.path.join(args.output_dir, f"baseline_seed_{seed}.pt")
        )

    # Train and evaluate variant agent
    print(f"Training variant agent for {args.episodes} episodes...")
    variant_metrics = train_and_evaluate(
        agent=variant_agent,
        env=variant_train_env,
        episodes=args.episodes,
        max_steps=args.max_steps,
        eval_interval=args.eval_interval,
        reward_scale=args.reward_scale,
        reduced_state_dim=reduced_state_dim  # Pass reduced state dimension
    )

    # Save variant model
    if args.save_models:
        save_model(
            variant_agent,
            os.path.join(args.output_dir, f"variant_seed_{seed}.pt")
        )

    # Test on test data if provided
    if baseline_test_env and variant_test_env:
        print("Evaluating on test data...")

        # Evaluate baseline agent on test data
        baseline_test_reward = evaluate_agent(
            baseline_agent,
            baseline_test_env,
            n_episodes=5,
            max_steps=args.max_steps,
            reduced_state_dim=reduced_state_dim  # Pass reduced state dimension
        )
        print(f"Baseline test reward: {baseline_test_reward:.2f}")

        # Evaluate variant agent on test data
        variant_test_reward = evaluate_agent(
            variant_agent,
            variant_test_env,
            n_episodes=5,
            max_steps=args.max_steps,
            reduced_state_dim=reduced_state_dim  # Pass reduced state dimension
        )
        print(f"Variant test reward: {variant_test_reward:.2f}")

        # Add test rewards to metrics
        baseline_metrics["test_reward"] = baseline_test_reward
        variant_metrics["test_reward"] = variant_test_reward

    return {
        "baseline": baseline_metrics,
        "variant": variant_metrics
    }

def run_ab_test(control_values, treatment_values, alpha=0.05, bootstrap_samples=1000):
    """
    Run a comprehensive A/B test with multiple statistical tests and bootstrap confidence intervals.

    Parameters
    ----------
    control_values : list
        Control group values
    treatment_values : list
        Treatment group values
    alpha : float, optional
        Significance level, by default 0.05

    Returns
    -------
    dict
        Dictionary with test results
    """
    # Handle empty or single-value arrays
    if len(control_values) <= 1 or len(treatment_values) <= 1:
        print("Warning: Not enough data points for statistical analysis. Need at least 2 seeds.")
        return {
            "control_mean": float(np.mean(control_values)) if len(control_values) > 0 else float('nan'),
            "control_std": float(np.std(control_values)) if len(control_values) > 1 else 0.0,
            "treatment_mean": float(np.mean(treatment_values)) if len(treatment_values) > 0 else float('nan'),
            "treatment_std": float(np.std(treatment_values)) if len(treatment_values) > 1 else 0.0,
            "percent_change": float(((np.mean(treatment_values) - np.mean(control_values)) / np.mean(control_values) * 100))
                              if len(control_values) > 0 and len(treatment_values) > 0 and np.mean(control_values) != 0 else float('nan'),
            "p_value": float('nan'),
            "significant": False,
            "effect_size": 0.0,
            "effect_size_interpretation": "insufficient data",
            "ci_lower": float('nan'),
            "ci_upper": float('nan'),
            "power": 0.0,
            "normality": False,
            "test_used": "insufficient data"
        }

    # Calculate basic statistics
    control_mean = np.mean(control_values)
    control_std = np.std(control_values, ddof=1)  # Use unbiased estimator
    treatment_mean = np.mean(treatment_values)
    treatment_std = np.std(treatment_values, ddof=1)  # Use unbiased estimator

    # Calculate percent change
    percent_change = ((treatment_mean - control_mean) / control_mean) * 100 if control_mean != 0 else float('nan')

    # Check for normality using Shapiro-Wilk test if enough data points
    if len(control_values) >= 3 and len(treatment_values) >= 3:
        _, control_normality = stats.shapiro(control_values)
        _, treatment_normality = stats.shapiro(treatment_values)
        is_normal = control_normality > 0.05 and treatment_normality > 0.05
    else:
        is_normal = True  # Assume normality for small samples

    # Perform appropriate statistical test
    if is_normal:
        # Use t-test for normally distributed data
        _, p_value = stats.ttest_ind(
            control_values,
            treatment_values,
            equal_var=False  # Welch's t-test
        )
        test_used = "Welch's t-test"
    else:
        # Use Mann-Whitney U test for non-normal data
        _, p_value = stats.mannwhitneyu(
            control_values,
            treatment_values,
            alternative="two-sided"
        )
        test_used = "Mann-Whitney U test"

    # Calculate effect size (Cohen's d)
    n1 = len(control_values)
    n2 = len(treatment_values)
    pooled_std = np.sqrt(((n1 - 1) * control_std**2 + (n2 - 1) * treatment_std**2) / (n1 + n2 - 2)) if n1 > 1 and n2 > 1 else 1.0
    effect_size = (treatment_mean - control_mean) / pooled_std if pooled_std > 0 else 0

    # Interpret effect size
    if abs(effect_size) < 0.2:
        effect_size_interpretation = "negligible"
    elif abs(effect_size) < 0.5:
        effect_size_interpretation = "small"
    elif abs(effect_size) < 0.8:
        effect_size_interpretation = "medium"
    else:
        effect_size_interpretation = "large"

    # Calculate 95% confidence interval using bootstrap
    try:
        # Bootstrap confidence interval for the difference in means
        n_bootstrap = bootstrap_samples
        bootstrap_diffs = []

        for _ in range(n_bootstrap):
            # Resample with replacement
            control_sample = np.random.choice(control_values, size=len(control_values), replace=True)
            treatment_sample = np.random.choice(treatment_values, size=len(treatment_values), replace=True)

            # Calculate difference in means
            diff = np.mean(treatment_sample) - np.mean(control_sample)
            bootstrap_diffs.append(diff)

        # Calculate 95% confidence interval
        ci_lower = np.percentile(bootstrap_diffs, 2.5)
        ci_upper = np.percentile(bootstrap_diffs, 97.5)
    except:
        ci_lower = float('nan')
        ci_upper = float('nan')

    # Calculate statistical power
    try:
        from statsmodels.stats.power import TTestIndPower
        power_analysis = TTestIndPower()
        power = power_analysis.solve_power(
            effect_size=abs(effect_size),
            nobs1=n1,
            ratio=n2/n1,
            alpha=alpha,
            alternative='two-sided'
        )
    except:
        power = float('nan')

    # Return comprehensive results
    return {
        "control_mean": float(control_mean),
        "control_std": float(control_std),
        "treatment_mean": float(treatment_mean),
        "treatment_std": float(treatment_std),
        "percent_change": float(percent_change),
        "p_value": float(p_value),
        "significant": bool(p_value < alpha),
        "effect_size": float(effect_size),
        "effect_size_interpretation": effect_size_interpretation,
        "ci_lower": float(ci_lower),
        "ci_upper": float(ci_upper),
        "power": float(power),
        "normality": bool(is_normal),
        "test_used": test_used
    }

def create_html_report(results, args, title="TCI A/B Test Report with TEP Dataset"):
    """
    Create an enhanced HTML report with comprehensive statistical analysis.

    Parameters
    ----------
    results : dict
        Dictionary with test results
    title : str, optional
        Report title, by default "TCI A/B Test Report with TEP Dataset"

    Returns
    -------
    str
        HTML report
    """
    # Start building HTML
    html = f"""<!DOCTYPE html>
    <html>
    <head>
        <title>{title}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #2c3e50; }}
            h2 {{ color: #3498db; }}
            h3 {{ color: #2980b9; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .result {{ font-weight: bold; font-size: 18px; margin: 10px 0; }}
            .significant {{ color: green; }}
            .not-significant {{ color: orange; }}
            .negative {{ color: red; }}
            .warning {{ color: #e67e22; }}
            table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .info-box {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .stat-box {{ background-color: #f0f9e8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .power-low {{ color: #e74c3c; }}
            .power-medium {{ color: #f39c12; }}
            .power-high {{ color: #27ae60; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>{title}</h1>

            <div class="summary">
                <h2>Summary</h2>
    """

    for metric, result in results.items():
        percent_change = result["percent_change"]
        p_value = result["p_value"]
        significant = result["significant"]
        effect_size = result["effect_size"]
        effect_size_interpretation = result["effect_size_interpretation"]
        power = result.get("power", float('nan'))
        ci_lower = result.get("ci_lower", float('nan'))
        ci_upper = result.get("ci_upper", float('nan'))
        test_used = result.get("test_used", "Unknown test")

        # Determine power class
        power_class = "power-low"
        if not np.isnan(power):
            if power >= 0.8:
                power_class = "power-high"
            elif power >= 0.5:
                power_class = "power-medium"

        html += f"""
                <h3>{metric}</h3>
                <p class="result">Improvement: <span class="{'significant' if significant and percent_change > 0 else 'not-significant' if not significant else 'negative'}">{percent_change:.2f}%</span></p>
                <p class="result">Statistical Significance: <span class="{'significant' if significant else 'not-significant'}">p-value = {p_value:.4f} ({'significant' if significant else 'not significant'})</span></p>
                <p class="result">Effect Size: <span class="{'significant' if abs(effect_size) >= 0.8 else 'not-significant' if abs(effect_size) >= 0.2 else 'negative'}">{effect_size:.2f} ({effect_size_interpretation})</span></p>
                <p class="result">95% Confidence Interval: [{ci_lower:.2f}, {ci_upper:.2f}]</p>
                <p class="result">Statistical Power: <span class="{power_class}">{power:.2f}</span> {'(adequate)' if power >= 0.8 else '(inadequate)'}</p>
                <p class="result">Test Used: {test_used}</p>
        """

    html += """
            </div>

            <h2>Detailed Results</h2>
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Baseline</th>
                    <th>Variant</th>
                    <th>Change</th>
                    <th>95% CI</th>
                    <th>p-value</th>
                    <th>Test Used</th>
                    <th>Power</th>
                    <th>Effect Size</th>
                </tr>
    """

    for metric, result in results.items():
        ci_lower = result.get("ci_lower", float('nan'))
        ci_upper = result.get("ci_upper", float('nan'))
        power = result.get("power", float('nan'))
        test_used = result.get("test_used", "Unknown test")

        html += f"""
                <tr>
                    <td>{metric}</td>
                    <td>{result['control_mean']:.2f} ± {result['control_std']:.2f}</td>
                    <td>{result['treatment_mean']:.2f} ± {result['treatment_std']:.2f}</td>
                    <td>{result['percent_change']:.2f}%</td>
                    <td>[{ci_lower:.2f}, {ci_upper:.2f}]</td>
                    <td>{result['p_value']:.4f} {'(significant)' if result['significant'] else ''}</td>
                    <td>{test_used}</td>
                    <td>{power:.2f} {'(adequate)' if power >= 0.8 else '(inadequate)'}</td>
                    <td>{result['effect_size']:.2f} ({result['effect_size_interpretation']})</td>
                </tr>
        """

    html += """
            </table>

            <div class="stat-box">
                <h3>Statistical Analysis Details</h3>
                <p>This report includes comprehensive statistical analysis suitable for doctoral research:</p>
                <ul>
                    <li><strong>Normality Testing:</strong> Shapiro-Wilk test to determine appropriate statistical tests</li>
                    <li><strong>Statistical Tests:</strong> Welch's t-test (for normal data) or Mann-Whitney U test (for non-normal data)</li>
                    <li><strong>Effect Size:</strong> Cohen's d to quantify the magnitude of differences</li>
                    <li><strong>Bootstrap Confidence Intervals:</strong> 95% confidence intervals for the difference in means</li>
                    <li><strong>Statistical Power:</strong> Probability of detecting an effect if one exists</li>
                </ul>
                <p class="warning">Note: Statistical power should be at least 0.8 (80%) for reliable results. Low power indicates a need for more seeds.</p>
            </div>

            <div class="info-box">
                <h3>About TCI with TEP Dataset</h3>
                <p>The Tennessee Eastman Process (TEP) is a realistic chemical process simulation widely used as a benchmark for process control and monitoring.</p>
                <p>The TCI framework bridges reinforcement learning and causal inference to handle dynamic systems like TEP that require temporal-causal reasoning.</p>
                <p>Key components of TCI:</p>
                <ul>
                    <li><strong>Dynamic Causal Graphs (G_t)</strong>: Temporal representation of causal relationships</li>
                    <li><strong>Long-horizon Prediction P(x_{{t+k}})</strong>: Forecasting future states based on causal understanding</li>
                    <li><strong>History Embedding (h_t)</strong>: Representation of temporal context</li>
                    <li><strong>Experience Replay</strong>: Efficient use of past experiences</li>
                    <li><strong>DynaQ</strong>: Model-based planning for improved sample efficiency</li>
                </ul>
            </div>

            <div class="info-box">
                <h3>Statistical Methods</h3>
                <p><strong>Effect Size Interpretation (Cohen's d):</strong></p>
                <ul>
                    <li>|d| < 0.2: Negligible effect</li>
                    <li>0.2 ≤ |d| < 0.5: Small effect</li>
                    <li>0.5 ≤ |d| < 0.8: Medium effect</li>
                    <li>|d| ≥ 0.8: Large effect</li>
                </ul>

                <p><strong>Statistical Power Interpretation:</strong></p>
                <ul>
                    <li>Power < 0.5: Low power (high risk of Type II error)</li>
                    <li>0.5 ≤ Power < 0.8: Medium power (moderate risk of Type II error)</li>
                    <li>Power ≥ 0.8: High power (low risk of Type II error)</li>
                </ul>

                <p><strong>Confidence Interval Interpretation:</strong></p>
                <ul>
                    <li>If the interval includes 0, the difference is not statistically significant</li>
                    <li>The width of the interval indicates the precision of the estimate</li>
                </ul>
            </div>

            <div class="info-box">
                <h3>Configuration Details</h3>
                <h4>Baseline Configuration:</h4>
                <ul>
                    <li><strong>Hidden Dimension:</strong> HIDDEN_DIM_BASELINE</li>
                    <li><strong>Learning Rate:</strong> LEARNING_RATE_BASELINE</li>
                    <li><strong>History Length:</strong> HISTORY_LENGTH_BASELINE</li>
                    <li><strong>Causal Update Interval:</strong> CAUSAL_UPDATE_INTERVAL_BASELINE</li>
                    <li><strong>Causal Discovery Method:</strong> CAUSAL_METHOD_BASELINE</li>
                    <li><strong>Causal Alpha:</strong> CAUSAL_ALPHA_BASELINE</li>
                    SPARSITY_FACTOR_BASELINE_HTML
                </ul>

                <h4>Variant Configuration:</h4>
                <ul>
                    <li><strong>Hidden Dimension:</strong> HIDDEN_DIM_VARIANT</li>
                    <li><strong>Learning Rate:</strong> LEARNING_RATE_VARIANT</li>
                    <li><strong>History Length:</strong> HISTORY_LENGTH_VARIANT</li>
                    <li><strong>Causal Update Interval:</strong> CAUSAL_UPDATE_INTERVAL_VARIANT</li>
                    <li><strong>Causal Discovery Method:</strong> CAUSAL_METHOD_VARIANT</li>
                    <li><strong>Causal Alpha:</strong> CAUSAL_ALPHA_VARIANT</li>
                    SPARSITY_FACTOR_VARIANT_HTML
                </ul>

                <h4>Key Differences:</h4>
                <ul>
                    {config_differences}
                </ul>
            </div>
        </div>
    </body>
    </html>
    """

    # Generate configuration differences list
    differences = []
    if args.hidden_dim != args.variant_hidden_dim:
        differences.append(f"<li><strong>Hidden Dimension:</strong> {args.hidden_dim} vs {args.variant_hidden_dim}</li>")
    if args.learning_rate != args.variant_learning_rate:
        differences.append(f"<li><strong>Learning Rate:</strong> {args.learning_rate} vs {args.variant_learning_rate}</li>")
    if args.history_length != args.variant_history_length:
        differences.append(f"<li><strong>History Length:</strong> {args.history_length} vs {args.variant_history_length}</li>")
    if args.causal_update_interval != args.variant_causal_update_interval:
        differences.append(f"<li><strong>Causal Update Interval:</strong> {args.causal_update_interval} vs {args.variant_causal_update_interval}</li>")
    if args.causal_method != args.variant_causal_method:
        differences.append(f"<li><strong>Causal Discovery Method:</strong> {args.causal_method} vs {args.variant_causal_method}</li>")
    if args.causal_alpha != args.variant_causal_alpha:
        differences.append(f"<li><strong>Causal Alpha:</strong> {args.causal_alpha} vs {args.variant_causal_alpha}</li>")
    if args.causal_method == 'sparse' and args.variant_causal_method == 'sparse' and args.sparsity_factor != args.variant_sparsity_factor:
        differences.append(f"<li><strong>Sparsity Factor:</strong> {args.sparsity_factor} vs {args.variant_sparsity_factor}</li>")

    config_differences = "\n                    ".join(differences) if differences else "<li>None (identical configurations)</li>"

    # Replace the configuration placeholders
    html = html.replace("HIDDEN_DIM_BASELINE", str(args.hidden_dim))
    html = html.replace("LEARNING_RATE_BASELINE", str(args.learning_rate))
    html = html.replace("HISTORY_LENGTH_BASELINE", str(args.history_length))
    html = html.replace("CAUSAL_UPDATE_INTERVAL_BASELINE", str(args.causal_update_interval))
    html = html.replace("CAUSAL_METHOD_BASELINE", str(args.causal_method))
    html = html.replace("CAUSAL_ALPHA_BASELINE", str(args.causal_alpha))

    # Handle sparsity factor for baseline
    if args.causal_method == 'sparse':
        html = html.replace("SPARSITY_FACTOR_BASELINE_HTML", f"<li><strong>Sparsity Factor:</strong> {args.sparsity_factor}</li>")
    else:
        html = html.replace("SPARSITY_FACTOR_BASELINE_HTML", "")

    html = html.replace("HIDDEN_DIM_VARIANT", str(args.variant_hidden_dim))
    html = html.replace("LEARNING_RATE_VARIANT", str(args.variant_learning_rate))
    html = html.replace("HISTORY_LENGTH_VARIANT", str(args.variant_history_length))
    html = html.replace("CAUSAL_UPDATE_INTERVAL_VARIANT", str(args.variant_causal_update_interval))
    html = html.replace("CAUSAL_METHOD_VARIANT", str(args.variant_causal_method))
    html = html.replace("CAUSAL_ALPHA_VARIANT", str(args.variant_causal_alpha))

    # Handle sparsity factor for variant
    if args.variant_causal_method == 'sparse':
        html = html.replace("SPARSITY_FACTOR_VARIANT_HTML", f"<li><strong>Sparsity Factor:</strong> {args.variant_sparsity_factor}</li>")
    else:
        html = html.replace("SPARSITY_FACTOR_VARIANT_HTML", "")

    # Replace the configuration differences placeholder
    html = html.replace("{config_differences}", config_differences)

    return html

def main():
    """
    Run the A/B test with the TEP dataset with enhanced statistical rigor.
    """
    parser = argparse.ArgumentParser(description="Run TCI A/B testing with TEP dataset")
    parser.add_argument("--episodes", type=int, default=50, help="Number of episodes to train")
    parser.add_argument("--max-steps", type=int, default=100, help="Maximum steps per episode")
    parser.add_argument("--eval-interval", type=int, default=5, help="Interval for evaluation")
    parser.add_argument("--output-dir", type=str, default="tci_tep_ab_test_results", help="Output directory")
    parser.add_argument("--master-seed", type=int, default=42, help="Master random seed")
    parser.add_argument("--num-seeds", type=int, default=10,
                        help="Number of random seeds to run (recommended: 10-20 for doctoral research)")
    parser.add_argument("--reward-scale", type=float, default=0.01, help="Reward scaling factor")

    # Baseline agent parameters
    parser.add_argument("--hidden-dim", type=int, default=32, help="Hidden dimension for baseline agent")
    parser.add_argument("--learning-rate", type=float, default=0.001, help="Learning rate for baseline agent")
    parser.add_argument("--history-length", type=int, default=1, help="History length for baseline agent")
    parser.add_argument("--causal-update-interval", type=int, default=50,
                        help="Interval for updating causal graph for baseline (0 to disable)")
    parser.add_argument("--causal-method", type=str, default="pc", choices=["pc", "ges", "notears", "correlation", "sparse"],
                        help="Causal discovery method for baseline agent")
    parser.add_argument("--causal-alpha", type=float, default=0.1,
                        help="Significance level for independence tests in causal discovery")
    parser.add_argument("--sparsity-factor", type=float, default=0.1,
                        help="Sparsity factor for sparse causal discovery")

    # Variant agent parameters
    parser.add_argument("--variant-hidden-dim", type=int, default=64, help="Hidden dimension for variant agent")
    parser.add_argument("--variant-learning-rate", type=float, default=0.001, help="Learning rate for variant agent")
    parser.add_argument("--variant-history-length", type=int, default=1, help="History length for variant agent")
    parser.add_argument("--variant-causal-update-interval", type=int, default=None,
                        help="Interval for updating causal graph for variant (0 to disable, None to use same as baseline)")
    parser.add_argument("--variant-causal-method", type=str, default=None, choices=["pc", "ges", "notears", "correlation", "sparse", None],
                        help="Causal discovery method for variant agent (None to use same as baseline)")
    parser.add_argument("--variant-causal-alpha", type=float, default=None,
                        help="Significance level for independence tests in causal discovery for variant (None to use same as baseline)")
    parser.add_argument("--variant-sparsity-factor", type=float, default=None,
                        help="Sparsity factor for sparse causal discovery for variant (None to use same as baseline)")

    # Other parameters
    parser.add_argument("--dataset-path", type=str, required=True, help="Path to TEP dataset directory")
    parser.add_argument("--save-models", action="store_true", help="Save trained models")
    parser.add_argument("--gpu", action="store_true", help="Use GPU if available")
    parser.add_argument("--alpha", type=float, default=0.05, help="Significance level for statistical tests")
    parser.add_argument("--bootstrap-samples", type=int, default=1000,
                        help="Number of bootstrap samples for confidence intervals")
    args = parser.parse_args()

    # Set variant parameters to baseline if not specified
    if args.variant_causal_update_interval is None:
        args.variant_causal_update_interval = args.causal_update_interval
    if args.variant_causal_method is None:
        args.variant_causal_method = args.causal_method
    if args.variant_causal_alpha is None:
        args.variant_causal_alpha = args.causal_alpha
    if args.variant_sparsity_factor is None:
        args.variant_sparsity_factor = args.sparsity_factor

    # Check if number of seeds is sufficient for statistical analysis
    if args.num_seeds < 5:
        print("\033[93mWARNING: Using fewer than 5 seeds may result in unreliable statistical analysis.\033[0m")
        print("\033[93mFor doctoral research, at least 10-20 seeds are recommended.\033[0m")
        print("\033[93mContinuing with requested number of seeds, but statistical power may be low.\033[0m")
    elif args.num_seeds < 10:
        print("\033[93mWARNING: For doctoral research, at least 10-20 seeds are recommended.\033[0m")
        print("\033[93mContinuing with requested number of seeds, but consider increasing for publication.\033[0m")

    print("Running TCI A/B Testing Framework with TEP Dataset")
    print("\nConfiguration:")
    print(f"  Number of seeds: {args.num_seeds}")
    print(f"  Episodes per seed: {args.episodes}")
    print(f"  Dataset path: {args.dataset_path}")

    print("\nBaseline Configuration:")
    print(f"  Hidden dimension: {args.hidden_dim}")
    print(f"  Learning rate: {args.learning_rate}")
    print(f"  History length: {args.history_length}")
    print(f"  Causal update interval: {args.causal_update_interval}")
    print(f"  Causal discovery method: {args.causal_method}")
    print(f"  Causal alpha: {args.causal_alpha}")
    if args.causal_method == 'sparse':
        print(f"  Sparsity factor: {args.sparsity_factor}")

    print("\nVariant Configuration:")
    print(f"  Hidden dimension: {args.variant_hidden_dim}")
    print(f"  Learning rate: {args.variant_learning_rate}")
    print(f"  History length: {args.variant_history_length}")
    print(f"  Causal update interval: {args.variant_causal_update_interval}")
    print(f"  Causal discovery method: {args.variant_causal_method}")
    print(f"  Causal alpha: {args.variant_causal_alpha}")
    if args.variant_causal_method == 'sparse':
        print(f"  Sparsity factor: {args.variant_sparsity_factor}")

    # Identify differences
    differences = []
    if args.hidden_dim != args.variant_hidden_dim:
        differences.append(f"Hidden dimension: {args.hidden_dim} vs {args.variant_hidden_dim}")
    if args.learning_rate != args.variant_learning_rate:
        differences.append(f"Learning rate: {args.learning_rate} vs {args.variant_learning_rate}")
    if args.history_length != args.variant_history_length:
        differences.append(f"History length: {args.history_length} vs {args.variant_history_length}")
    if args.causal_update_interval != args.variant_causal_update_interval:
        differences.append(f"Causal update interval: {args.causal_update_interval} vs {args.variant_causal_update_interval}")
    if args.causal_method != args.variant_causal_method:
        differences.append(f"Causal discovery method: {args.causal_method} vs {args.variant_causal_method}")
    if args.causal_alpha != args.variant_causal_alpha:
        differences.append(f"Causal alpha: {args.causal_alpha} vs {args.variant_causal_alpha}")
    if args.causal_method == 'sparse' and args.variant_causal_method == 'sparse' and args.sparsity_factor != args.variant_sparsity_factor:
        differences.append(f"Sparsity factor: {args.sparsity_factor} vs {args.variant_sparsity_factor}")

    print("\nKey Differences:")
    if differences:
        for diff in differences:
            print(f"  {diff}")
    else:
        print("  None (identical configurations)")

    # Check if dataset exists
    train_data_path = os.path.join(args.dataset_path, "TEP_FaultFree_Training.RData")
    test_data_path = os.path.join(args.dataset_path, "TEP_FaultFree_Testing.RData")

    if not os.path.exists(train_data_path):
        print(f"Error: Training data not found at {train_data_path}")
        return

    if not os.path.exists(test_data_path):
        print(f"Warning: Testing data not found at {test_data_path}")
        test_data_path = None

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Generate random seeds
    np.random.seed(args.master_seed)
    seeds = np.random.randint(0, 10000, args.num_seeds)
    print(f"Generated seeds: {seeds}")

    # Run experiments for each seed
    all_results = []
    for i, seed in enumerate(seeds):
        print(f"\nRunning seed {i+1}/{args.num_seeds} (seed={seed})...")
        result = run_single_seed(seed, args, train_data_path, test_data_path)
        all_results.append(result)

    # Extract metrics
    baseline_train_rewards = [np.mean(r["baseline"]["train_rewards"]) for r in all_results]
    variant_train_rewards = [np.mean(r["variant"]["train_rewards"]) for r in all_results]

    baseline_eval_rewards = [np.mean(r["baseline"]["eval_rewards"]) for r in all_results]
    variant_eval_rewards = [np.mean(r["variant"]["eval_rewards"]) for r in all_results]

    # Extract test rewards if available
    if test_data_path:
        baseline_test_rewards = [r["baseline"]["test_reward"] for r in all_results]
        variant_test_rewards = [r["variant"]["test_reward"] for r in all_results]

    # Run A/B tests with enhanced statistical analysis
    results_dict = {
        "Training Reward": run_ab_test(
            baseline_train_rewards,
            variant_train_rewards,
            alpha=args.alpha,
            bootstrap_samples=args.bootstrap_samples
        ),
        "Evaluation Reward": run_ab_test(
            baseline_eval_rewards,
            variant_eval_rewards,
            alpha=args.alpha,
            bootstrap_samples=args.bootstrap_samples
        )
    }

    if test_data_path:
        results_dict["Test Reward"] = run_ab_test(
            baseline_test_rewards,
            variant_test_rewards,
            alpha=args.alpha,
            bootstrap_samples=args.bootstrap_samples
        )

    # Save data
    np.savez_compressed(
        os.path.join(args.output_dir, "tep_ab_test_data.npz"),
        baseline_train_rewards=baseline_train_rewards,
        variant_train_rewards=variant_train_rewards,
        baseline_eval_rewards=baseline_eval_rewards,
        variant_eval_rewards=variant_eval_rewards,
        baseline_test_rewards=baseline_test_rewards if test_data_path else None,
        variant_test_rewards=variant_test_rewards if test_data_path else None
    )

    # Create and save HTML report
    report_html = create_html_report(
        results=results_dict,
        args=args,
        title="TCI A/B Test Report with TEP Dataset"
    )

    report_path = os.path.join(args.output_dir, "tep_ab_test_report.html")
    with open(report_path, "w") as f:
        f.write(report_html)
    print(f"Saved A/B test report to {report_path}")

    # Save results as JSON
    with open(os.path.join(args.output_dir, "tep_ab_test_results.json"), "w") as f:
        json.dump(results_dict, f, indent=2)
    print(f"Saved A/B test results JSON.")

    # Print final results
    print("\nA/B Testing Complete!")

    for metric, result in results_dict.items():
        print(f"\n{metric}:")
        print(f"Baseline: {result['control_mean']:.2f} ± {result['control_std']:.2f}")
        print(f"Variant: {result['treatment_mean']:.2f} ± {result['treatment_std']:.2f}")
        print(f"Improvement: {result['percent_change']:.2f}%")
        print(f"p-value: {result['p_value']:.4f} ({'significant' if result['significant'] else 'not significant'})")
        print(f"Effect size (Cohen's d): {result['effect_size']:.2f} ({result['effect_size_interpretation']})")

    print(f"\nResults saved in {args.output_dir}/")

if __name__ == "__main__":
    main()
