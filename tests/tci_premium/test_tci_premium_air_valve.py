"""
Test script for the TCI Premium model on AIR CUT VALVE ASSY product.
"""

import os
import sys
import logging
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import our custom models
import sys
import os

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tci_premium import TCIPremiumPredictor
from folder_data_manager import FolderDataManager

def test_tci_premium_air_valve():
    """
    Test the TCI Premium model on AIR CUT VALVE ASSY product.
    """
    logger.info("Initializing data manager")
    data_manager = FolderDataManager(dataset_folder="dataset")
    
    # Load data from folder
    success, message = data_manager.load_data_from_folder()
    if not success:
        logger.error(f"Failed to load data from folder: {message}")
        return
    
    logger.info(f"Loaded data for {len(data_manager.customers)} customers")
    
    logger.info("Initializing TCI Premium predictor")
    tci_premium = TCIPremiumPredictor()
    
    # Use a specific customer and product
    customer = "YIMM (INT)"
    product = "AIR CUT VALVE ASSY ( EA0256A0-R )"
    logger.info(f"Using customer: {customer}")
    logger.info(f"Using product: {product}")
    
    # Train the model
    logger.info(f"Training model for {customer} - {product}")
    try:
        # Load data
        key = f"{customer}_{product}"
        if key in data_manager.data:
            data = data_manager.data[key]
        else:
            logger.error(f"Product data not found with key: {key}")
            # Try to find the product in the data
            possible_keys = [k for k in data_manager.data.keys() if product in k]
            if possible_keys:
                logger.info(f"Found possible matching products: {possible_keys}")
                key = possible_keys[0]
                data = data_manager.data[key]
                logger.info(f"Using product with key: {key}")
            else:
                logger.error(f"No matching products found")
                return
        
        # Check if data was loaded correctly
        if data is None or data.empty:
            logger.error(f"Failed to load data for {customer} - {product}")
            return
            
        logger.info(f"Loaded data with {len(data)} rows")
        
        # Convert to the format expected by TCI Premium
        data['ds'] = pd.to_datetime(data['date'])
        data['y'] = data['quantity']
        
        # Train the model
        success, message = tci_premium.train_model(
            data=data,
            customer=customer,
            product=product,
            optimize_hyperparams=True
        )
        
        if success:
            logger.info(f"Successfully trained model: {message}")
            
            # Generate forecast
            logger.info("Generating forecast")
            forecast = tci_premium.predict_future(customer, product, periods=36)  # 3 years (36 months)
            
            if forecast is not None and not forecast.empty:
                logger.info(f"Successfully generated forecast with {len(forecast)} periods")
                logger.info(f"Forecast date range: {forecast['ds'].min()} to {forecast['ds'].max()}")
                logger.info(f"Forecast sample:\n{forecast.head()}")
                
                # Save the forecast to a CSV file
                forecast_file = f"Forecast_YIMM_AIR_VALVE(TCI_Premium).csv"
                forecast.to_csv(forecast_file, index=False)
                logger.info(f"Saved forecast to {forecast_file}")
                
                # Create a visualization
                plt.figure(figsize=(12, 6))
                
                # Plot historical data
                plt.plot(data['ds'], data['y'], 'b-', label='Historical Data')
                
                # Plot forecast
                plt.plot(forecast['ds'], forecast['yhat'], 'r-', label='TCI Premium Forecast')
                
                # Add confidence intervals
                plt.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'], 
                                color='r', alpha=0.2, label='Confidence Interval')
                
                # Add labels and title
                plt.xlabel('Date')
                plt.ylabel('Quantity')
                plt.title(f'TCI Premium Forecast for {customer} - {product}')
                plt.legend()
                plt.grid(True)
                
                # Save the visualization
                plt.savefig(f"Forecast_YIMM_AIR_VALVE(TCI_Premium).png", dpi=300, bbox_inches='tight')
                logger.info(f"Saved visualization to Forecast_YIMM_AIR_VALVE(TCI_Premium).png")
                
                # Compare with actual data from full dataset
                compare_with_actual_data(forecast, customer, product)
            else:
                logger.error("Failed to generate forecast")
        else:
            logger.error(f"Failed to train model: {message}")
    except Exception as e:
        logger.error(f"Error testing TCI Premium: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def compare_with_actual_data(forecast, customer, product):
    """
    Compare the forecast with actual data from the full dataset.
    
    Args:
        forecast (pd.DataFrame): Forecast dataframe
        customer (str): Customer name
        product (str): Product name
    """
    try:
        # Load actual data from full dataset
        full_data_path = f"dataset(full)/{customer}/{product}.csv"
        if os.path.exists(full_data_path):
            actual_data = pd.read_csv(full_data_path)
            actual_data['ds'] = pd.to_datetime(actual_data['date'])
            actual_data['y'] = actual_data['quantity']
            
            # Filter actual data to match forecast period
            min_forecast_date = forecast['ds'].min()
            max_forecast_date = forecast['ds'].max()
            actual_data_in_forecast_period = actual_data[
                (actual_data['ds'] >= min_forecast_date) & 
                (actual_data['ds'] <= max_forecast_date)
            ]
            
            if not actual_data_in_forecast_period.empty:
                logger.info(f"Found {len(actual_data_in_forecast_period)} actual data points in forecast period")
                
                # Merge forecast with actual data
                comparison = pd.merge(
                    forecast[['ds', 'yhat', 'yhat_lower', 'yhat_upper']], 
                    actual_data_in_forecast_period[['ds', 'y']], 
                    on='ds', 
                    how='outer'
                )
                
                # Save comparison to CSV
                comparison_file = f"Comparison_YIMM_AIR_VALVE.csv"
                comparison.to_csv(comparison_file, index=False)
                logger.info(f"Saved comparison to {comparison_file}")
                
                # Create comparison visualization
                plt.figure(figsize=(15, 8))
                
                # Plot forecast
                plt.plot(forecast['ds'], forecast['yhat'], 'r-', label='TCI Premium Forecast')
                
                # Plot actual data
                plt.plot(actual_data_in_forecast_period['ds'], actual_data_in_forecast_period['y'], 'g-', 
                        marker='o', markersize=8, label='Actual Data')
                
                # Add confidence intervals
                plt.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'], 
                                color='r', alpha=0.2)
                
                # Add labels and title
                plt.xlabel('Date')
                plt.ylabel('Quantity')
                plt.title(f'Forecast vs Actual Data for {customer} - {product}')
                plt.legend()
                plt.grid(True)
                
                # Save the visualization
                plt.savefig(f"Comparison_YIMM_AIR_VALVE.png", dpi=300, bbox_inches='tight')
                logger.info(f"Saved comparison visualization to Comparison_YIMM_AIR_VALVE.png")
                
                # Calculate metrics
                calculate_accuracy_metrics(comparison)
            else:
                logger.warning("No actual data found in forecast period")
        else:
            logger.warning(f"Full dataset file not found: {full_data_path}")
    except Exception as e:
        logger.error(f"Error comparing with actual data: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def calculate_accuracy_metrics(comparison):
    """
    Calculate accuracy metrics for the forecast.
    
    Args:
        comparison (pd.DataFrame): Dataframe with forecast and actual data
    """
    # Drop rows with missing values
    comparison_clean = comparison.dropna()
    
    if len(comparison_clean) > 0:
        # Calculate Mean Absolute Error (MAE)
        mae = (comparison_clean['y'] - comparison_clean['yhat']).abs().mean()
        
        # Calculate Root Mean Squared Error (RMSE)
        rmse = ((comparison_clean['y'] - comparison_clean['yhat']) ** 2).mean() ** 0.5
        
        # Calculate Mean Absolute Percentage Error (MAPE)
        mape = ((comparison_clean['y'] - comparison_clean['yhat']).abs() / comparison_clean['y']).mean() * 100
        
        # Calculate percentage of actual values within prediction interval
        within_interval = ((comparison_clean['y'] >= comparison_clean['yhat_lower']) & 
                          (comparison_clean['y'] <= comparison_clean['yhat_upper'])).mean() * 100
        
        logger.info(f"Accuracy Metrics:")
        logger.info(f"  MAE: {mae:.2f}")
        logger.info(f"  RMSE: {rmse:.2f}")
        logger.info(f"  MAPE: {mape:.2f}%")
        logger.info(f"  % within interval: {within_interval:.2f}%")
        
        # Save metrics to file
        metrics = {
            'MAE': mae,
            'RMSE': rmse,
            'MAPE': mape,
            'Within_Interval': within_interval
        }
        
        pd.DataFrame([metrics]).to_csv("Metrics_YIMM_AIR_VALVE.csv", index=False)
        logger.info("Saved metrics to Metrics_YIMM_AIR_VALVE.csv")
    else:
        logger.warning("No overlapping data points for accuracy calculation")

if __name__ == "__main__":
    test_tci_premium_air_valve()
