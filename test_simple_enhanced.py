#!/usr/bin/env python3
"""
Test Simple Enhanced TCI-fix

Quick test of the simplified enhanced TCI-fix model.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_simple_enhanced():
    """Test the simplified enhanced TCI-fix model."""
    print("🧪 Testing Simplified Enhanced TCI-fix")
    print("=" * 50)
    
    try:
        # Import modules
        from tci_fix_simple_enhanced import SimplifiedEnhancedTCIFix
        from tci_fix_integration import load_external_data
        from folder_data_manager import FolderDataManager
        
        # Load data
        print("📊 Loading test data...")
        data_manager = FolderDataManager("dataset")
        success, message = data_manager.load_data_from_folder()
        
        if not success:
            print(f"❌ Failed to load data: {message}")
            return
        
        # Test with ADVIK product
        customer = "ADVIK (IND)"
        product = "MECHANICAL SEAL ( BB-0030451 )"
        key = f"{customer}_{product}"
        
        if key not in data_manager.data:
            print(f"❌ No data found for {customer} - {product}")
            return
        
        data = data_manager.data[key].copy()
        print(f"✅ Loaded data: {len(data)} rows")
        
        # Load external data
        print("🌐 Loading external data...")
        external_data = load_external_data(start_year=2016, end_year=2025)
        print(f"✅ Loaded {len(external_data)} external data sources")
        
        # Test 1: Train simplified enhanced model
        print("\n🔧 Test 1: Training Simplified Enhanced Model")
        enhanced_model = SimplifiedEnhancedTCIFix(
            bias_correction=True,
            uncertainty_quantification=True
        )
        
        enhanced_model.fit(data, external_data)
        print("✅ Enhanced model trained successfully")
        print(f"   Bias correction factor: {enhanced_model.bias_correction_factor:.3f}")
        print(f"   Volatility level: {enhanced_model.volatility_level}")
        print(f"   Ensemble weights: {enhanced_model.ensemble_weights}")
        
        # Test 2: Generate enhanced predictions
        print("\n📈 Test 2: Generating Enhanced Predictions")
        enhanced_predictions = enhanced_model.predict(
            start_date='2025-02-25',
            end_date='2025-07-25',
            data=data,
            external_data=external_data
        )
        
        if enhanced_predictions is not None:
            print(f"✅ Enhanced predictions generated: {len(enhanced_predictions)} periods")
            print(f"   Prediction range: {enhanced_predictions['predicted_quantity'].min():,.0f} to {enhanced_predictions['predicted_quantity'].max():,.0f}")
            
            # Check enhanced features
            if 'lower_bound' in enhanced_predictions.columns:
                print(f"✅ Uncertainty bounds included")
                avg_uncertainty = (enhanced_predictions['upper_bound'] - enhanced_predictions['lower_bound']).mean()
                print(f"   Average uncertainty range: ±{avg_uncertainty:,.0f}")
            
            if 'confidence_level' in enhanced_predictions.columns:
                print(f"✅ Confidence levels included")
                avg_confidence = enhanced_predictions['confidence_level'].mean()
                print(f"   Average confidence: {avg_confidence:.1%}")
            
            # Show sample predictions
            print(f"\n📋 Sample Enhanced Predictions:")
            for i in range(min(5, len(enhanced_predictions))):
                row = enhanced_predictions.iloc[i]
                pred = row['predicted_quantity']
                lower = row.get('lower_bound', pred)
                upper = row.get('upper_bound', pred)
                confidence = row.get('confidence_level', 0.75)
                
                print(f"   Period {i+1}: {pred:6,.0f} [{lower:6,.0f} - {upper:6,.0f}] ({confidence:.0%} confidence)")
        else:
            print("❌ Enhanced predictions failed")
            return
        
        # Test 3: Compare with original TCI-fix
        print("\n🔍 Test 3: Comparing with Original TCI-fix")
        from tci_fix_integration import TCIFixIntegration
        
        original_integration = TCIFixIntegration()
        original_predictions = original_integration.predict_future(customer, product, periods=6)
        
        if original_predictions is not None:
            print(f"✅ Original predictions generated: {len(original_predictions)} periods")
            
            # Compare predictions
            enhanced_mean = enhanced_predictions['predicted_quantity'].mean()
            original_mean = original_predictions['predicted_quantity'].mean()
            difference = enhanced_mean - original_mean
            difference_pct = (difference / original_mean) * 100 if original_mean > 0 else 0
            
            print(f"\n📊 Comparison:")
            print(f"   Enhanced mean: {enhanced_mean:,.0f}")
            print(f"   Original mean: {original_mean:,.0f}")
            print(f"   Difference: {difference:+,.0f} ({difference_pct:+.1f}%)")
            
            # Show side-by-side comparison
            print(f"\n📋 Side-by-Side Comparison:")
            print(f"   {'Period':<8} {'Enhanced':<10} {'Original':<10} {'Diff':<8} {'Diff%':<8}")
            print(f"   {'-'*8} {'-'*10} {'-'*10} {'-'*8} {'-'*8}")
            
            for i in range(min(5, len(enhanced_predictions), len(original_predictions))):
                enhanced_val = enhanced_predictions.iloc[i]['predicted_quantity']
                original_val = original_predictions.iloc[i]['predicted_quantity']
                diff = enhanced_val - original_val
                diff_pct = (diff / original_val) * 100 if original_val > 0 else 0
                
                print(f"   {i+1:<8} {enhanced_val:<10,.0f} {original_val:<10,.0f} {diff:<+8,.0f} {diff_pct:<+8.1f}")
        else:
            print("❌ Original predictions failed")
        
        # Test 4: Validate enhanced features
        print("\n🎯 Test 4: Enhanced Features Validation")
        
        # Check bias correction
        if abs(enhanced_model.bias_correction_factor - 1.0) > 0.1:
            print(f"✅ Bias correction active: {enhanced_model.bias_correction_factor:.3f}")
        else:
            print(f"⚠️ Minimal bias correction: {enhanced_model.bias_correction_factor:.3f}")
        
        # Check ensemble components
        if 'tci_fix_component' in enhanced_predictions.columns:
            print(f"✅ Ensemble components available")
            tci_component = enhanced_predictions['tci_fix_component'].mean()
            trend_component = enhanced_predictions['trend_component'].mean()
            ma_component = enhanced_predictions['ma_component'].mean()
            
            print(f"   TCI-fix component: {tci_component:,.0f}")
            print(f"   Trend component: {trend_component:,.0f}")
            print(f"   MA component: {ma_component:,.0f}")
        
        # Check uncertainty quantification
        if 'uncertainty_factor' in enhanced_predictions.columns:
            avg_uncertainty = enhanced_predictions['uncertainty_factor'].mean()
            print(f"✅ Uncertainty quantification: ±{avg_uncertainty:.1%}")
        
        print(f"\n🎉 Simplified Enhanced TCI-fix Test Complete!")
        print("=" * 50)
        print("Enhanced features working:")
        print("✅ Bias correction")
        print("✅ Uncertainty quantification")
        print("✅ Ensemble approach")
        print("✅ Volatility-aware modeling")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_accuracy_test():
    """Quick test to see if enhanced model improves accuracy."""
    print("\n🎯 Quick Accuracy Test")
    print("-" * 30)
    
    try:
        from tci_fix_simple_enhanced import SimplifiedEnhancedTCIFix
        from tci_fix_integration import load_external_data
        from folder_data_manager import FolderDataManager
        
        # Load data
        data_manager = FolderDataManager("dataset")
        success, message = data_manager.load_data_from_folder()
        
        if not success:
            print(f"❌ Failed to load data: {message}")
            return
        
        # Test with ADVIK product
        customer = "ADVIK (IND)"
        product = "MECHANICAL SEAL ( BB-0030451 )"
        key = f"{customer}_{product}"
        
        if key not in data_manager.data:
            print(f"❌ No data found for {customer} - {product}")
            return
        
        data = data_manager.data[key].copy()
        external_data = load_external_data(start_year=2016, end_year=2025)
        
        # Split data for testing
        split_point = int(len(data) * 0.8)
        train_data = data.iloc[:split_point].copy()
        test_data = data.iloc[split_point:].copy()
        
        if len(test_data) < 3:
            print("⚠️ Insufficient test data")
            return
        
        print(f"📊 Using {len(train_data)} training samples, {len(test_data)} test samples")
        
        # Train enhanced model
        enhanced_model = SimplifiedEnhancedTCIFix()
        enhanced_model.fit(train_data, external_data)
        
        # Generate predictions for test period
        test_start = test_data.iloc[0]['Date']
        test_end = test_data.iloc[-1]['Date']
        
        enhanced_pred = enhanced_model.predict(test_start, test_end, train_data, external_data)
        
        if enhanced_pred is not None and len(enhanced_pred) >= len(test_data):
            # Calculate accuracy
            actual = test_data['Quantity'].values
            predicted = enhanced_pred['predicted_quantity'].iloc[:len(test_data)].values
            
            # Calculate metrics
            mae = np.mean(np.abs(actual - predicted))
            mape = np.mean(np.abs((actual - predicted) / (actual + 1e-6))) * 100
            
            print(f"📈 Enhanced Model Accuracy:")
            print(f"   MAE: {mae:,.0f}")
            print(f"   MAPE: {mape:.1f}%")
            
            # Show individual predictions
            print(f"\n📋 Individual Predictions:")
            for i in range(len(test_data)):
                actual_val = actual[i]
                pred_val = predicted[i]
                error = abs(actual_val - pred_val)
                error_pct = (error / actual_val) * 100 if actual_val > 0 else 0
                
                status = "🟢" if error_pct < 20 else "🟡" if error_pct < 40 else "🔴"
                print(f"   {status} Actual: {actual_val:6,.0f}, Predicted: {pred_val:6,.0f}, Error: {error_pct:5.1f}%")
        
    except Exception as e:
        print(f"❌ Accuracy test failed: {e}")

if __name__ == "__main__":
    try:
        # Test enhanced model
        success = test_simple_enhanced()
        
        if success:
            # Quick accuracy test
            quick_accuracy_test()
        
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")
        import traceback
        traceback.print_exc()
