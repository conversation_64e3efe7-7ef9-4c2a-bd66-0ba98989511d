#!/bin/bash

# Release script for the TCI framework
# This script automates the release process for the TCI framework

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Get the version from tci/__init__.py
VERSION=$(grep -o '"[0-9]\+\.[0-9]\+\.[0-9]\+"' tci/__init__.py | tr -d '"')
echo -e "${YELLOW}Preparing release for TCI framework version ${VERSION}${NC}"

# Check if working directory is clean
if [[ -n $(git status --porcelain) ]]; then
    echo -e "${RED}Error: Working directory is not clean. Please commit or stash changes before releasing.${NC}"
    exit 1
fi

# Run tests
echo -e "\n${YELLOW}Running tests...${NC}"
pytest --cov=tci tests/
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Tests failed. Please fix the tests before releasing.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests passed.${NC}"

# Build documentation
echo -e "\n${YELLOW}Building documentation...${NC}"
cd docs
make html
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Documentation build failed. Please fix the documentation before releasing.${NC}"
    exit 1
fi
cd ..
echo -e "${GREEN}Documentation built successfully.${NC}"

# Clean up previous builds
echo -e "\n${YELLOW}Cleaning up previous builds...${NC}"
rm -rf build/ dist/ *.egg-info/

# Build the package
echo -e "\n${YELLOW}Building the package...${NC}"
python setup.py sdist bdist_wheel
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Package build failed.${NC}"
    exit 1
fi
echo -e "${GREEN}Package built successfully.${NC}"

# Check the package
echo -e "\n${YELLOW}Checking the package...${NC}"
twine check dist/*
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Package check failed.${NC}"
    exit 1
fi
echo -e "${GREEN}Package check passed.${NC}"

# Create a git tag
echo -e "\n${YELLOW}Creating git tag v${VERSION}...${NC}"
git tag -a "v${VERSION}" -m "Release version ${VERSION}"
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to create git tag.${NC}"
    exit 1
fi
echo -e "${GREEN}Git tag created successfully.${NC}"

# Ask for confirmation before pushing
echo -e "\n${YELLOW}Ready to push tag v${VERSION} to remote repository.${NC}"
read -p "Do you want to continue? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Release process aborted.${NC}"
    git tag -d "v${VERSION}"
    exit 0
fi

# Push the tag
echo -e "\n${YELLOW}Pushing tag v${VERSION} to remote repository...${NC}"
git push origin "v${VERSION}"
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to push git tag.${NC}"
    exit 1
fi
echo -e "${GREEN}Git tag pushed successfully.${NC}"

# Ask for confirmation before uploading to PyPI
echo -e "\n${YELLOW}Ready to upload package to PyPI.${NC}"
read -p "Do you want to continue? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Package upload aborted.${NC}"
    exit 0
fi

# Upload to PyPI
echo -e "\n${YELLOW}Uploading package to PyPI...${NC}"
twine upload dist/*
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to upload package to PyPI.${NC}"
    exit 1
fi
echo -e "${GREEN}Package uploaded successfully.${NC}"

# Create GitHub release
echo -e "\n${YELLOW}Creating GitHub release...${NC}"
echo -e "${YELLOW}Please create a GitHub release manually at:${NC}"
echo -e "${YELLOW}https://github.com/yourusername/tci/releases/new?tag=v${VERSION}${NC}"
echo -e "${YELLOW}Use the content from CHANGELOG.md for the release notes.${NC}"

echo -e "\n${GREEN}Release process completed successfully!${NC}"
echo -e "${GREEN}TCI framework version ${VERSION} has been released.${NC}"
