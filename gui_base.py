"""
User Interface Module for Historical Customer Order Prediction System

This module creates a modern, user-friendly interface for our prediction system using PyQt5.
It provides a complete UI with:

1. A tabbed interface for different functions (data management, analysis, prediction)
2. Interactive controls (dropdowns, buttons, checkboxes) for user input
3. Visualization components for displaying data and forecasts
4. Data tables for showing detailed information
5. Status indicators to keep the user informed of progress

The UI is designed to be intuitive and responsive, making it easy for users to
load their data, analyze patterns, and generate forecasts without needing to
understand the underlying technical details.

Author: Harry
Version: 1.0
"""

# Standard library imports
import sys                      # For system-level operations
import os                       # For file and directory operations
from datetime import datetime   # For date and time handling

# Data handling
import pandas as pd             # For data manipulation and display in tables
import numpy as np              # For numerical operations
import matplotlib.pyplot as plt # For plotting
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# Import our custom model integration
from model_integration import ModelIntegration
from folder_data_manager import FolderDataManager

# PyQt5 imports for building the GUI
from PyQt5.QtCore import Qt  # For alignment and other core functionality
from PyQt5.QtWidgets import (
    QApplication,              # The main application object
    QMainWindow,               # The main window container
    QWidget,                   # Base widget class
    QVBoxLayout, QHBoxLayout,  # Layout managers for organizing widgets
    QLabel,                    # For displaying text
    QComboBox,                 # Dropdown selection boxes
    QPushButton,               # Clickable buttons
    QFileDialog, QMessageBox,  # Dialog windows
    QTabWidget,                # For creating tabbed interfaces
    QGroupBox,                 # For grouping related controls
    QFormLayout,               # For label-field layouts
    QLineEdit, QSpinBox,       # Text input and number input
    QCheckBox,                 # Toggle options
    QSplitter, QFrame,         # UI organization elements
    QStatusBar, QProgressBar,  # Status indicators
    QSizePolicy,               # Widget sizing policies
    QTableView, QHeaderView,   # For displaying tabular data
    QGridLayout,               # Grid-based layout manager
    QTableWidget, QTableWidgetItem,  # For creating and populating tables
    QScrollArea                # For scrollable areas
)
from PyQt5.QtCore import Qt, QSize, QAbstractTableModel  # Core Qt functionality
from PyQt5.QtGui import QIcon, QFont, QColor             # UI styling elements

# Matplotlib integration for plotting
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt

# Logging configuration
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PandasTableModel(QAbstractTableModel):
    """Model for displaying pandas DataFrame in QTableView.

    This class bridges the gap between pandas DataFrames and Qt's table views,
    allowing us to display our data in a nicely formatted table in the UI.
    It handles the conversion of data types, formatting of values, and proper
    display of headers.
    """

    def __init__(self, data):
        """Initialize with pandas DataFrame.

        Args:
            data: A pandas DataFrame containing the data to display
        """
        super().__init__()
        self._data = data  # Store the DataFrame for later access

    def rowCount(self, parent=None):
        """Return number of rows."""
        return len(self._data)

    def columnCount(self, parent=None):
        """Return number of columns."""
        return len(self._data.columns)

    def data(self, index, role=Qt.DisplayRole):
        """Return data at index."""
        if not index.isValid():
            return None

        if role == Qt.DisplayRole:
            value = self._data.iloc[index.row(), index.column()]
            # Format dates nicely
            if isinstance(value, pd.Timestamp):
                return value.strftime('%Y-%m-%d')
            # Format numbers with 2 decimal places
            elif isinstance(value, (int, float)):
                return f"{value:.2f}" if isinstance(value, float) else str(value)
            else:
                return str(value)

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        """Return header data."""
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return str(self._data.columns[section])
            else:
                return str(section + 1)  # Row numbers starting from 1

        return None

class MatplotlibCanvas(FigureCanvas):
    """Matplotlib canvas for embedding interactive plots in Qt.

    This class integrates Matplotlib (a powerful plotting library) with PyQt,
    allowing us to embed interactive charts and graphs directly in our application.
    It handles the creation of figures, axes, and provides methods for updating
    and clearing plots.
    """

    def __init__(self, parent=None, width=4, height=3, dpi=100):
        """Initialize the canvas with sensible defaults.

        Args:
            parent: Parent widget (optional)
            width: Width of the figure in inches (default: 5)
            height: Height of the figure in inches (default: 4)
            dpi: Resolution in dots per inch (default: 100)
        """
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)

        # Create canvas with the figure
        super(MatplotlibCanvas, self).__init__(self.fig)
        self.setParent(parent)

        # Set up figure for better integration with Qt
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.updateGeometry()

        # Set minimum size to ensure the plot is visible
        self.setMinimumSize(400, 300)

    def clear(self):
        """Clear the figure to prepare for new plots.

        This is useful when updating the display with new data.
        """
        self.axes.clear()  # Remove all plots from the axes
        self.draw()        # Redraw the empty canvas

    def resizeEvent(self, event):
        """Handle resize events to adjust the figure layout."""
        super().resizeEvent(event)
        # Adjust the layout with more padding and make sure it fills the canvas
        self.fig.subplots_adjust(left=0.1, right=0.95, bottom=0.15, top=0.9)
        self.draw()

class MainWindow(QMainWindow):
    """Main window for the Historical Customer Order Prediction System.

    This class creates the main application window and all its components.
    It defines the overall structure of the UI, including:

    1. The tabbed interface for different functional areas
    2. The menu bar for global actions
    3. The status bar for displaying messages
    4. The progress indicator for long-running operations

    Each tab is created by a separate method, making the code modular and easier
    to maintain as the application grows.
    """

    def __init__(self, data_manager=None):
        """Initialize the main window and set up the UI components.

        This constructor creates the window but doesn't populate it with data.
        Data loading happens separately when the application starts.

        Parameters:
        -----------
        data_manager : FolderDataManager, optional
            The data manager to use. If None, a new one will be created.
        """
        super().__init__()

        # Set window properties
        self.setWindowTitle("Customer Order Prediction System")
        self.setGeometry(100, 100, 1200, 800)

        # Store the data manager
        self.data_manager = data_manager if data_manager else FolderDataManager(dataset_folder="dataset")

        # Initialize model integration
        self.model_integration = ModelIntegration(self.data_manager)

        # Initialize UI components
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Create central widget and main layout
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)

        # Create tab widget for different sections
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # Create tabs
        self.create_data_tab()
        self.create_analysis_tab()
        self.create_prediction_tab()
        # Metrics tab hidden for now
        # self.create_metrics_tab()

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label, 1)

        # Progress bar for long operations
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setVisible(False)
        self.status_bar.addWidget(self.progress_bar)

        # Set central widget
        self.setCentralWidget(central_widget)

    def create_data_tab(self):
        """Create the data management tab with scrolling support."""
        # Create a base widget for the tab
        data_tab = QWidget()

        # Create a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)  # Allow the widget to resize
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold the content
        scroll_content = QWidget()

        # Set the content widget for the scroll area
        scroll_area.setWidget(scroll_content)

        # Create a layout for the tab
        tab_layout = QVBoxLayout(data_tab)
        tab_layout.addWidget(scroll_area)

        # Create a layout for the content
        layout = QVBoxLayout(scroll_content)

        # Data loading section
        data_group = QGroupBox("Data Source")
        data_layout = QVBoxLayout(data_group)

        # Dataset folder info
        folder_layout = QHBoxLayout()
        folder_layout.addWidget(QLabel("Dataset Folder:"))
        self.dataset_path_label = QLabel("dataset/")
        folder_layout.addWidget(self.dataset_path_label)

        # Buttons for data management
        self.refresh_btn = QPushButton("Refresh Data")
        # Connection will be handled in main.py
        folder_layout.addWidget(self.refresh_btn)

        # Process All Data button
        self.process_all_btn = QPushButton("Process Excel Files")
        self.process_all_btn.setToolTip("Process all Excel files in the data folder and organize them by customer and product")
        self.process_all_btn.setStyleSheet("font-weight: bold;")
        self.process_all_btn.setMinimumHeight(30)  # Make the button taller
        # Connection will be handled in main.py
        folder_layout.addWidget(self.process_all_btn)

        data_layout.addLayout(folder_layout)

        # Data info section - organize in a grid
        info_group = QGroupBox("Dataset Statistics")
        info_grid = QGridLayout(info_group)

        # Create labels with better styling
        self.records_label = QLabel("0")
        self.records_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.customers_label = QLabel("0")
        self.customers_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.products_label = QLabel("0")
        self.products_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.date_range_label = QLabel("N/A")
        self.date_range_label.setStyleSheet("font-size: 12px;")

        # Add labels to grid - first row
        info_grid.addWidget(QLabel("Total Records:"), 0, 0)
        info_grid.addWidget(self.records_label, 0, 1)
        info_grid.addWidget(QLabel("Unique Customers:"), 0, 2)
        info_grid.addWidget(self.customers_label, 0, 3)

        # Second row
        info_grid.addWidget(QLabel("Unique Products:"), 1, 0)
        info_grid.addWidget(self.products_label, 1, 1)
        info_grid.addWidget(QLabel("Date Range:"), 1, 2)
        info_grid.addWidget(self.date_range_label, 1, 3)

        data_layout.addWidget(info_group)
        layout.addWidget(data_group)

        # Data preview section
        preview_group = QGroupBox("Data Preview")
        preview_layout = QVBoxLayout(preview_group)

        # Add customer and product selection in a grid layout
        selection_group = QGroupBox("Select Data to Preview")
        selection_grid = QGridLayout(selection_group)

        self.data_customer_combo = QComboBox()
        self.data_customer_combo.currentTextChanged.connect(self.update_data_products)
        self.data_customer_combo.setMinimumWidth(200)  # Set minimum width
        self.data_customer_combo.setMaxVisibleItems(15)  # Limit dropdown height
        self.data_customer_combo.setStyleSheet("combobox-popup: 0;")  # Enable scrollbar

        self.data_product_combo = QComboBox()
        self.data_product_combo.currentTextChanged.connect(self.update_data_preview)
        self.data_product_combo.setMinimumWidth(200)  # Set minimum width
        self.data_product_combo.setMaxVisibleItems(15)  # Limit dropdown height
        self.data_product_combo.setStyleSheet("combobox-popup: 0;")  # Enable scrollbar

        # Add to grid
        selection_grid.addWidget(QLabel("Customer:"), 0, 0)
        selection_grid.addWidget(self.data_customer_combo, 0, 1)
        selection_grid.addWidget(QLabel("Product:"), 0, 2)
        selection_grid.addWidget(self.data_product_combo, 0, 3)

        # Note: Preview updates automatically when selections change

        preview_layout.addWidget(selection_group)

        # Add a matplotlib canvas for the graph
        self.data_canvas = MatplotlibCanvas(width=10, height=4)
        preview_layout.addWidget(self.data_canvas)

        # Add a table view for the data
        self.data_table = QTableView()
        self.data_table.setMinimumHeight(200)
        preview_layout.addWidget(self.data_table)

        layout.addWidget(preview_group, 1)  # Give it a stretch factor of 1

        # Add tab
        self.tabs.addTab(data_tab, "Data Management")

    def create_analysis_tab(self):
        """Create the data analysis tab with scrolling support."""
        # Create a base widget for the tab
        analysis_tab = QWidget()

        # Create a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)  # Allow the widget to resize
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold the content
        scroll_content = QWidget()

        # Set the content widget for the scroll area
        scroll_area.setWidget(scroll_content)

        # Create a layout for the tab
        tab_layout = QVBoxLayout(analysis_tab)
        tab_layout.addWidget(scroll_area)

        # Create a layout for the content
        layout = QVBoxLayout(scroll_content)

        # Selection section
        selection_group = QGroupBox("Selection")
        selection_layout = QGridLayout(selection_group)
        selection_layout.setColumnStretch(1, 1)  # Make the combo boxes stretch
        selection_layout.setColumnStretch(3, 1)  # Make the combo boxes stretch

        # --- Data Selection ---
        data_group = QGroupBox("Data Selection")
        data_layout = QFormLayout(data_group)

        self.analysis_customer_combo = QComboBox()
        self.analysis_customer_combo.currentTextChanged.connect(self.update_analysis_products)
        self.analysis_customer_combo.setMinimumWidth(200)  # Set minimum width
        self.analysis_customer_combo.setMaxVisibleItems(15)  # Limit dropdown height
        self.analysis_customer_combo.setStyleSheet("combobox-popup: 0;")  # Enable scrollbar

        self.analysis_product_combo = QComboBox()
        self.analysis_product_combo.currentTextChanged.connect(self.update_analysis)
        self.analysis_product_combo.setMinimumWidth(200)  # Set minimum width
        self.analysis_product_combo.setMaxVisibleItems(15)  # Limit dropdown height
        self.analysis_product_combo.setStyleSheet("combobox-popup: 0;")  # Enable scrollbar

        data_layout.addRow("Customer:", self.analysis_customer_combo)
        data_layout.addRow("Product:", self.analysis_product_combo)

        # Add the group to the main layout
        selection_layout.addWidget(data_group, 0, 0, 1, 3)

        # Note: Analysis updates automatically when selections change

        layout.addWidget(selection_group)

        # Analysis results section
        results_group = QGroupBox("Analysis Results")
        results_layout = QVBoxLayout(results_group)

        # Matplotlib canvas for plotting
        self.analysis_canvas = MatplotlibCanvas(width=10, height=6)
        results_layout.addWidget(self.analysis_canvas, 1)  # Give it a stretch factor of 1

        # Statistics section
        stats_layout = QFormLayout()
        self.total_orders_label = QLabel("0")
        self.avg_quantity_label = QLabel("0")
        self.order_frequency_label = QLabel("N/A")

        stats_layout.addRow("Total Orders:", self.total_orders_label)
        stats_layout.addRow("Average Quantity:", self.avg_quantity_label)
        stats_layout.addRow("Order Frequency:", self.order_frequency_label)

        results_layout.addLayout(stats_layout)

        # Export button
        export_btn = QPushButton("Export Analysis")
        export_btn.clicked.connect(self.export_analysis)
        results_layout.addWidget(export_btn)

        layout.addWidget(results_group, 1)  # Give it a stretch factor of 1

        # Add tab
        self.tabs.addTab(analysis_tab, "Data Analysis")

    def create_metrics_tab(self):
        """Create the model metrics tab with scrolling support."""
        # Create a base widget for the tab
        metrics_tab = QWidget()

        # Create a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)  # Allow the widget to resize
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold the content
        scroll_content = QWidget()

        # Set the content widget for the scroll area
        scroll_area.setWidget(scroll_content)

        # Create a layout for the tab
        tab_layout = QVBoxLayout(metrics_tab)
        tab_layout.addWidget(scroll_area)

        # Create a layout for the content
        layout = QVBoxLayout(scroll_content)

        # Create selection group
        selection_group = QGroupBox("Select Model and Data")
        selection_layout = QFormLayout()

        # Customer selection
        self.metrics_customer_combo = QComboBox()
        selection_layout.addRow("Customer:", self.metrics_customer_combo)

        # Product selection
        self.metrics_product_combo = QComboBox()
        selection_layout.addRow("Product:", self.metrics_product_combo)

        # Model selection
        self.metrics_model_combo = QComboBox()
        # Get available models from model integration
        try:
            available_models = self.model_integration.get_available_models()
            self.metrics_model_combo.addItems(available_models)
        except:
            # Fallback to default models if model integration fails
            self.metrics_model_combo.addItems(["TCI", "TCI Premium", "TCI-fix"])
        selection_layout.addRow("Model:", self.metrics_model_combo)

        # Calculate button
        self.calculate_metrics_btn = QPushButton("Calculate Metrics")
        selection_layout.addRow("", self.calculate_metrics_btn)

        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)

        # Metrics display group
        metrics_group = QGroupBox("Model Quality Metrics")
        metrics_layout = QVBoxLayout()

        # Create metrics table
        self.metrics_table = QTableWidget(4, 2)
        self.metrics_table.setHorizontalHeaderLabels(["Metric", "Value"])
        self.metrics_table.setVerticalHeaderLabels(["MAPE", "RMSE", "MAE", "R²"])
        self.metrics_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Initialize with metric names
        metric_names = ["MAPE", "RMSE", "MAE", "R²"]
        for row, name in enumerate(metric_names):
            self.metrics_table.setItem(row, 0, QTableWidgetItem(name))
            self.metrics_table.setItem(row, 1, QTableWidgetItem("Not calculated"))

        metrics_layout.addWidget(self.metrics_table)

        # Visualization options
        viz_layout = QHBoxLayout()

        # Plot button
        self.plot_metrics_btn = QPushButton("Plot Metrics")
        self.plot_metrics_btn.setEnabled(False)
        viz_layout.addWidget(self.plot_metrics_btn)

        # Export button
        self.export_metrics_btn = QPushButton("Export Metrics")
        self.export_metrics_btn.setEnabled(False)
        viz_layout.addWidget(self.export_metrics_btn)

        metrics_layout.addLayout(viz_layout)

        # Add canvas for plots
        self.metrics_canvas_frame = QFrame()
        self.metrics_canvas_frame.setFrameShape(QFrame.StyledPanel)
        self.metrics_canvas_frame.setFrameShadow(QFrame.Raised)
        self.metrics_canvas_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        metrics_canvas_layout = QVBoxLayout(self.metrics_canvas_frame)
        self.metrics_canvas = MatplotlibCanvas(width=8, height=6)
        metrics_canvas_layout.addWidget(self.metrics_canvas)

        # Initially hide the canvas
        self.metrics_canvas_frame.setVisible(False)

        metrics_layout.addWidget(self.metrics_canvas_frame)

        metrics_group.setLayout(metrics_layout)
        layout.addWidget(metrics_group)

        # Add the tab
        self.tabs.addTab(metrics_tab, "Metrics")

    def create_prediction_tab(self):
        """Create the prediction tab with scrolling support."""
        # Create a base widget for the tab
        prediction_tab = QWidget()

        # Create a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)  # Allow the widget to resize
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold the content
        scroll_content = QWidget()

        # Set the content widget for the scroll area
        scroll_area.setWidget(scroll_content)

        # Create a layout for the tab
        tab_layout = QVBoxLayout(prediction_tab)
        tab_layout.addWidget(scroll_area)

        # Create a layout for the content
        layout = QVBoxLayout(scroll_content)

        # Selection section
        selection_group = QGroupBox("Selection")
        selection_layout = QGridLayout(selection_group)
        selection_layout.setColumnStretch(1, 1)  # Make the combo boxes stretch
        selection_layout.setColumnStretch(3, 1)  # Make the combo boxes stretch

        # Create a more organized layout with two columns

        # --- First Column: Data Selection ---
        data_group = QGroupBox("Data Selection")
        data_layout = QFormLayout(data_group)

        self.prediction_customer_combo = QComboBox()
        self.prediction_customer_combo.currentTextChanged.connect(self.update_prediction_products)
        self.prediction_customer_combo.setMinimumWidth(200)  # Set minimum width
        self.prediction_customer_combo.setMaxVisibleItems(15)  # Limit dropdown height
        self.prediction_customer_combo.setStyleSheet("combobox-popup: 0;")  # Enable scrollbar

        self.prediction_product_combo = QComboBox()
        self.prediction_product_combo.setMinimumWidth(200)  # Set minimum width
        self.prediction_product_combo.setMaxVisibleItems(15)  # Limit dropdown height
        self.prediction_product_combo.setStyleSheet("combobox-popup: 0;")  # Enable scrollbar

        data_layout.addRow("Customer:", self.prediction_customer_combo)
        data_layout.addRow("Product:", self.prediction_product_combo)

        # --- Second Column: Forecast Settings ---
        forecast_group = QGroupBox("Forecast Settings")
        forecast_layout = QFormLayout(forecast_group)

        # Prediction parameters
        self.prediction_period_combo = QComboBox()
        self.prediction_period_combo.addItems(["1 month", "3 months", "6 months", "1 year", "3 years", "5 years"])
        self.prediction_period_combo.setMaxVisibleItems(10)  # Limit dropdown height

        # Year selection - include 2021 and onwards
        current_year = datetime.now().year
        self.prediction_year_combo = QComboBox()
        self.prediction_year_combo.addItems(["After Historical Data"] + [str(year) for year in range(2021, current_year + 6)])
        self.prediction_year_combo.setMaxVisibleItems(10)  # Limit dropdown height

        # Connect the year selection to update model configuration
        self.prediction_year_combo.currentTextChanged.connect(self.on_start_year_changed)

        # Add model selection
        self.model_selection_combo = QComboBox()

        # Model integration is now initialized in __init__

        # Add available models to dropdown
        self.model_selection_combo.addItems(self.model_integration.get_available_models())
        self.model_selection_combo.setMaxVisibleItems(10)  # Limit dropdown height

        # Add checkbox for using global model
        self.use_global_model_checkbox = QCheckBox("Use Global Model")
        self.use_global_model_checkbox.setToolTip("Use a single model trained on all data instead of customer-product specific model")
        # Only enable for Ensemble model
        self.use_global_model_checkbox.setEnabled(False)

        # Add checkbox for hyperparameter optimization
        self.optimize_hyperparams_checkbox = QCheckBox("Optimize Hyperparameters")
        self.optimize_hyperparams_checkbox.setToolTip("Automatically find the best hyperparameters for the model (increases training time)")
        self.optimize_hyperparams_checkbox.setChecked(True)  # Enabled by default
        # Only enable for TCI Premium model
        self.optimize_hyperparams_checkbox.setEnabled(False)

        self.model_selection_combo.currentTextChanged.connect(self.on_model_selection_changed)

        forecast_layout.addRow("Forecast Period:", self.prediction_period_combo)
        forecast_layout.addRow("Start Year:", self.prediction_year_combo)
        forecast_layout.addRow("Model:", self.model_selection_combo)
        forecast_layout.addRow("", self.use_global_model_checkbox)  # Add the global model checkbox
        forecast_layout.addRow("", self.optimize_hyperparams_checkbox)  # Add the hyperparameter optimization checkbox

        # --- Third Column: Display Options ---
        display_group = QGroupBox("Display Options")
        display_layout = QFormLayout(display_group)

        # Frequency and interval options
        self.prediction_interval_check = QCheckBox("Show prediction intervals")
        self.prediction_interval_check.setChecked(True)

        # Add graph style selector
        self.graph_style_combo = QComboBox()
        self.graph_style_combo.addItems(["Bar Chart", "Line Chart", "Scatter Plot", "Area Chart"])
        self.graph_style_combo.setMaxVisibleItems(10)  # Limit dropdown height

        display_layout.addRow("Graph Style:", self.graph_style_combo)
        display_layout.addRow("", self.prediction_interval_check)

        # Add the groups to the main layout
        selection_layout.addWidget(data_group, 0, 0, 1, 2)
        selection_layout.addWidget(forecast_group, 0, 2, 1, 2)
        selection_layout.addWidget(display_group, 1, 0, 1, 2)

        # Create a button group for training and prediction
        button_group = QGroupBox("Model Operations")
        button_layout = QVBoxLayout(button_group)  # Changed to vertical layout for better organization

        # Top row buttons
        top_button_layout = QHBoxLayout()
        button_layout.addLayout(top_button_layout)

        # Training button
        train_btn = QPushButton("Train Model")
        train_btn.clicked.connect(self.train_model)
        train_btn.setMinimumHeight(40)  # Make the button taller
        train_btn.setStyleSheet("font-weight: bold; font-size: 14px; background-color: #3498db; color: white;")  # Blue button
        top_button_layout.addWidget(train_btn)

        # Train All Products button
        train_all_products_btn = QPushButton("Train All Products")
        train_all_products_btn.clicked.connect(self.train_all_products)
        train_all_products_btn.setToolTip("Train individual models for all products of the selected customer")
        train_all_products_btn.setMinimumHeight(40)  # Make the button taller
        train_all_products_btn.setStyleSheet("font-weight: bold; font-size: 14px; background-color: #9b59b6; color: white;")  # Purple button
        top_button_layout.addWidget(train_all_products_btn)

        # Global Model button
        global_model_btn = QPushButton("Train Global Model")
        global_model_btn.clicked.connect(self.train_global_model)
        global_model_btn.setToolTip("Train a single model on all customer-product data combined")
        global_model_btn.setMinimumHeight(40)  # Make the button taller
        global_model_btn.setStyleSheet("font-weight: bold; font-size: 14px; background-color: #f39c12; color: white;")  # Orange button
        top_button_layout.addWidget(global_model_btn)

        # Bottom row buttons
        bottom_button_layout = QHBoxLayout()
        button_layout.addLayout(bottom_button_layout)

        # Prediction button
        predict_btn = QPushButton("Generate Forecast")
        predict_btn.clicked.connect(self.generate_forecast)
        predict_btn.setMinimumHeight(40)  # Make the button taller
        predict_btn.setStyleSheet("font-weight: bold; font-size: 14px; background-color: #2ecc71; color: white;")  # Green button
        bottom_button_layout.addWidget(predict_btn)

        # Add a label for displaying metrics
        self.metrics_label = QLabel("")
        self.metrics_label.setStyleSheet("font-weight: bold; color: #2980b9; font-size: 14px; padding: 5px; background-color: #f8f9fa; border-radius: 5px;")
        self.metrics_label.setAlignment(Qt.AlignCenter)

        # Add the button group
        selection_layout.addWidget(button_group, 1, 2, 1, 2)

        # Add the metrics label below the buttons
        selection_layout.addWidget(self.metrics_label, 2, 0, 1, 4)

        layout.addWidget(selection_group)

        # Prediction results section
        results_group = QGroupBox("Forecast Results")
        results_layout = QVBoxLayout(results_group)

        # Create a tab widget for different graph views
        self.prediction_graph_tabs = QTabWidget()
        results_layout.addWidget(self.prediction_graph_tabs, 1)  # Give it a stretch factor of 1

        # Tab 1: Combined view (history and prediction together)
        combined_tab = QWidget()
        combined_layout = QVBoxLayout(combined_tab)
        self.prediction_canvas_combined = MatplotlibCanvas(width=8, height=4)
        combined_layout.addWidget(self.prediction_canvas_combined)
        self.prediction_graph_tabs.addTab(combined_tab, "Complete Timeline")

        # Tab 2: Separate view (history and prediction separate)
        separate_tab = QWidget()
        separate_layout = QVBoxLayout(separate_tab)
        self.prediction_canvas_separate = MatplotlibCanvas(width=8, height=4)
        separate_layout.addWidget(self.prediction_canvas_separate)
        self.prediction_graph_tabs.addTab(separate_tab, "History & Forecast (Highlighted)")

        # Tab 3: Table view of forecast data
        table_tab = QWidget()
        table_layout = QVBoxLayout(table_tab)

        # Create a table for displaying forecast results
        self.prediction_result_table = QTableWidget()
        self.prediction_result_table.setColumnCount(4)
        self.prediction_result_table.setHorizontalHeaderLabels(["Date", "Forecast", "Lower Bound", "Upper Bound"])
        self.prediction_result_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table_layout.addWidget(self.prediction_result_table)

        self.prediction_graph_tabs.addTab(table_tab, "Forecast Table")

        # For backward compatibility, keep the original canvas reference
        self.prediction_canvas = self.prediction_canvas_combined

        # Export buttons
        export_layout = QHBoxLayout()

        export_forecast_btn = QPushButton("Export Forecast")
        export_forecast_btn.clicked.connect(self.export_forecast)
        export_layout.addWidget(export_forecast_btn)

        export_components_btn = QPushButton("Export Components")
        export_components_btn.clicked.connect(self.export_components)
        export_layout.addWidget(export_components_btn)

        export_report_btn = QPushButton("Export Full Report")
        export_report_btn.clicked.connect(self.export_report)
        export_layout.addWidget(export_report_btn)

        results_layout.addLayout(export_layout)

        layout.addWidget(results_group, 1)  # Give it a stretch factor of 1

        # Add tab
        self.tabs.addTab(prediction_tab, "Prediction")



    # Metrics tab removed

    # Settings tab removed as it was not functional

    # Data tab methods
    def update_data_customers(self):
        """Update customer list in data tab."""
        # This will be implemented in main.py when connecting to the data manager
        pass

    def process_all_data(self):
        """Process all data from the data folder.

        This method will be implemented in main.py to process all Excel files
        in the data folder and convert them to the structured dataset format.
        """
        # This will be implemented in main.py
        pass

    def update_data_products(self):
        """Update product list based on selected customer in data tab."""
        # This will be implemented in main.py when connecting to the data manager
        pass

    def update_data_preview(self):
        """Update data preview based on selected customer and product."""
        # This will be implemented in main.py when connecting to the data manager
        pass

    # Analysis tab methods
    def update_analysis_products(self):
        """Update product list based on selected customer in analysis tab."""
        # This will be implemented when connecting to the data manager
        # For now, use the data from our model integration
        try:
            # Get customers from the data manager
            customers = self.model_integration.tci_predictor.customers

            # Clear and update the customer combo box
            self.prediction_customer_combo.clear()
            self.prediction_customer_combo.addItems(customers)

            # Update products for the first customer
            if customers:
                self.update_prediction_products()

        except Exception as e:
            self.status_label.setText(f"Error loading customers: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_analysis(self):
        """Update analysis based on selected customer and product."""
        # This will be implemented when connecting to the data manager
        pass

    def perform_analysis(self):
        """Perform data analysis for selected customer and product."""
        # This will be implemented when connecting to the data manager
        self.status_label.setText("Analysis completed")

    def export_analysis(self):
        """Export analysis results."""
        # This will be implemented when connecting to the data manager
        pass

    def update_prediction_products(self):
        """Update product list based on selected customer in prediction tab."""
        # Get selected customer
        customer = self.prediction_customer_combo.currentText()

        # Get products for the selected customer
        try:
            products = self.model_integration.tci_predictor.products_by_customer.get(customer, [])

            # Clear and update the product combo box
            self.prediction_product_combo.clear()
            self.prediction_product_combo.addItems(products)

        except Exception as e:
            self.status_label.setText(f"Error loading products: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_model_selection_changed(self, model_name):
        """Update UI based on selected model."""
        # Enable/disable global model checkbox based on model selection
        self.use_global_model_checkbox.setEnabled(model_name == "Ensemble")

        # Enable/disable hyperparameter optimization checkbox based on model selection
        self.optimize_hyperparams_checkbox.setEnabled(model_name == "TCI Premium")

    def on_start_year_changed(self, start_year_text):
        """Update model configuration based on selected start year."""
        # Get the current model
        model_name = self.model_selection_combo.currentText()

        # Adjust hyperparameter optimization based on start year
        if start_year_text != "After Historical Data":
            start_year = int(start_year_text)

            # For older start years (2021-2022), use more aggressive optimization
            if start_year <= 2022:
                self.optimize_hyperparams_checkbox.setChecked(True)
                self.status_label.setText(f"Using aggressive optimization for start year {start_year}")

            # For recent years (2023+), use standard optimization
            else:
                # Keep current setting
                self.status_label.setText(f"Using standard configuration for start year {start_year}")
        else:
            # For 'After Historical Data', use default settings
            self.status_label.setText("Using default configuration for predictions after historical data")

    def generate_forecast(self):
        """Generate forecast for selected customer and product."""
        # Get selected customer and product
        customer = self.prediction_customer_combo.currentText()
        product = self.prediction_product_combo.currentText()

        # Get selected model
        model_name = self.model_selection_combo.currentText()

        # Get forecast period
        period_text = self.prediction_period_combo.currentText()
        periods = int(period_text.split()[0])  # Extract number from text like "12 months"

        # Get display options
        show_intervals = self.prediction_interval_check.isChecked()
        graph_style = self.graph_style_combo.currentText()

        # Update status
        self.status_label.setText(f"Generating forecast for {customer}, {product} using {model_name}...")

        try:
            # Get start year selection
            start_year_text = self.prediction_year_combo.currentText()

            # Handle start year selection
            start_year = None
            if start_year_text != "After Historical Data":
                start_year = int(start_year_text)
                self.status_label.setText(f"Generating forecast starting from {start_year} for {customer}, {product} using {model_name}...")

            # Get hyperparameter optimization setting
            optimize_hyperparams = self.optimize_hyperparams_checkbox.isChecked()

            # Generate forecast with appropriate configuration based on start year
            forecast_df = self.model_integration.generate_forecast(
                model_name, customer, product, periods=periods, start_year=start_year,
                optimize_hyperparams=optimize_hyperparams
            )

            if forecast_df is None:
                self.status_label.setText(f"Error generating forecast for {customer}, {product}")
                return

            # Plot forecast using the existing canvas
            fig = self.model_integration.plot_forecast(
                model_name, customer, product, forecast_df, show_intervals=show_intervals
            )

            # Update the combined view canvas
            self.prediction_canvas_combined.fig.clear()
            new_fig_canvas = FigureCanvas(fig)

            # Copy the figure to our canvas
            for ax in fig.get_axes():
                new_ax = self.prediction_canvas_combined.fig.add_subplot(111)
                for line in ax.get_lines():
                    x_data = line.get_xdata()
                    y_data = line.get_ydata()
                    new_ax.plot(x_data, y_data,
                               color=line.get_color(),
                               linestyle=line.get_linestyle(),
                               marker=line.get_marker(),
                               label=line.get_label(),
                               alpha=line.get_alpha())

                # Copy title and labels
                new_ax.set_title(ax.get_title())
                new_ax.set_xlabel(ax.get_xlabel())
                new_ax.set_ylabel(ax.get_ylabel())

                # Copy legend
                if ax.get_legend() is not None:
                    new_ax.legend()

                # Copy grid
                new_ax.grid(ax.get_grid())

                # Copy x and y ticks rotation
                plt.setp(new_ax.get_xticklabels(), rotation=45)

            # Refresh the canvas
            self.prediction_canvas_combined.fig.tight_layout()
            self.prediction_canvas_combined.draw()

            # Display forecast in the table
            self.display_forecast_table(forecast_df)

            # Get model components
            components = self.model_integration.get_model_components(model_name, customer, product)

            # Display model components if available
            if components is not None:
                # Display metrics
                if model_name == "TCI (Temporal Causality Infusion)":
                    metrics = components.get("metrics", {})
                    metrics_text = f"MAPE: {metrics.get('mape', 'N/A'):.4f}, "
                    metrics_text += f"R²: {metrics.get('r2', 'N/A'):.4f}, "
                    metrics_text += f"RMSE: {metrics.get('rmse', 'N/A'):.4f}"
                    self.metrics_label.setText(metrics_text)

                elif model_name == "Hybrid (TCI + XGBoost)":
                    tci_metrics = components.get("tci_metrics", {})
                    xgb_metrics = components.get("xgb_metrics", {})
                    metrics_text = f"TCI - MAPE: {tci_metrics.get('mape', 'N/A'):.4f}, "
                    metrics_text += f"R²: {tci_metrics.get('r2', 'N/A'):.4f}; "
                    metrics_text += f"XGBoost - MAPE: {xgb_metrics.get('mape', 'N/A'):.4f}, "
                    metrics_text += f"R²: {xgb_metrics.get('r2', 'N/A'):.4f}"
                    self.metrics_label.setText(metrics_text)

            # Update status
            self.status_label.setText(f"Forecast generated for {customer}, {product} using {model_name}")

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            import traceback
            traceback.print_exc()

    def export_forecast(self):
        """Export forecast results."""
        # Get selected customer and product
        customer = self.prediction_customer_combo.currentText()
        product = self.prediction_product_combo.currentText()

        # Get selected model
        model_name = self.model_selection_combo.currentText()

        # Get forecast period
        period_text = self.prediction_period_combo.currentText()
        periods = int(period_text.split()[0])  # Extract number from text like "12 months"

        # Update status
        self.status_label.setText(f"Exporting forecast for {customer}, {product}...")

        try:
            # Generate forecast if not already generated
            forecast_df = self.model_integration.generate_forecast(
                model_name, customer, product, periods=periods
            )

            if forecast_df is None:
                self.status_label.setText(f"Error generating forecast for {customer}, {product}")
                return

            # Open file dialog to get save location
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Forecast", "", "CSV Files (*.csv);;All Files (*)"
            )

            if not file_path:
                self.status_label.setText("Export cancelled")
                return

            # Export forecast
            success = self.model_integration.export_forecast(
                model_name, customer, product, forecast_df, file_path
            )

            if success:
                self.status_label.setText(f"Forecast exported to {file_path}")
            else:
                self.status_label.setText("Error exporting forecast")

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            import traceback
            traceback.print_exc()

    def export_components(self):
        """Export forecast components."""
        # Get selected customer and product
        customer = self.prediction_customer_combo.currentText()
        product = self.prediction_product_combo.currentText()

        # Get selected model
        model_name = self.model_selection_combo.currentText()

        # Update status
        self.status_label.setText(f"Exporting components for {customer}, {product}...")

        try:
            # Get model components
            components = self.model_integration.get_model_components(model_name, customer, product)

            if components is None:
                self.status_label.setText(f"No components available for {model_name}")
                return

            # Open file dialog to get save location
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Components", "", "PNG Files (*.png);;All Files (*)"
            )

            if not file_path:
                self.status_label.setText("Export cancelled")
                return

            # Export components based on model type
            if model_name == "TCI (Temporal Causality Infusion)":
                # Copy causal graph to the selected location
                import shutil
                causal_graph_path = components.get("causal_graph_path")
                if os.path.exists(causal_graph_path):
                    shutil.copy(causal_graph_path, file_path)
                    self.status_label.setText(f"Causal graph exported to {file_path}")
                else:
                    self.status_label.setText("Causal graph not found")

            elif model_name == "Hybrid (TCI + XGBoost)":
                # Copy future predictions plot to the selected location
                import shutil
                predictions_path = components.get("future_predictions_path")
                if os.path.exists(predictions_path):
                    shutil.copy(predictions_path, file_path)
                    self.status_label.setText(f"Predictions plot exported to {file_path}")
                else:
                    self.status_label.setText("Predictions plot not found")

            else:
                self.status_label.setText(f"Component export not implemented for {model_name}")

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            import traceback
            traceback.print_exc()

    def export_report(self):
        """Export full report with analysis and forecast."""
        # Get selected customer and product
        customer = self.prediction_customer_combo.currentText()
        product = self.prediction_product_combo.currentText()

        # Get selected model
        model_name = self.model_selection_combo.currentText()

        # Get forecast period
        period_text = self.prediction_period_combo.currentText()
        periods = int(period_text.split()[0])  # Extract number from text like "12 months"

        # Update status
        self.status_label.setText(f"Exporting report for {customer}, {product}...")

        try:
            # Generate forecast if not already generated
            forecast_df = self.model_integration.generate_forecast(
                model_name, customer, product, periods=periods
            )

            if forecast_df is None:
                self.status_label.setText(f"Error generating forecast for {customer}, {product}")
                return

            # Open file dialog to get save location
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Report", "", "Markdown Files (*.md);;All Files (*)"
            )

            if not file_path:
                self.status_label.setText("Export cancelled")
                return

            # Export report
            success = self.model_integration.export_report(
                model_name, customer, product, forecast_df, file_path
            )

            if success:
                self.status_label.setText(f"Report exported to {file_path}")
            else:
                self.status_label.setText("Error exporting report")

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            import traceback
            traceback.print_exc()

    # Dark mode toggle removed along with settings tab

    def display_forecast_table(self, forecast_df):
        """Display forecast data in the table."""
        # Get the data table from the prediction tab
        prediction_data_table = self.prediction_data_table

        # Clear the table
        prediction_data_table.setRowCount(0)

        # Set the number of rows
        prediction_data_table.setRowCount(len(forecast_df))

        # Populate the table
        for i, (_, row) in enumerate(forecast_df.iterrows()):
            # Date
            date_item = QTableWidgetItem(row['date'].strftime('%Y-%m-%d'))
            prediction_data_table.setItem(i, 0, date_item)

            # Predicted Quantity
            predicted_item = QTableWidgetItem(f"{row['predicted_quantity']:.2f}")
            prediction_data_table.setItem(i, 1, predicted_item)

            # Confidence Interval
            if 'lower_bound' in forecast_df.columns and 'upper_bound' in forecast_df.columns:
                interval_item = QTableWidgetItem(
                    f"[{row['lower_bound']:.2f}, {row['upper_bound']:.2f}]"
                )
                prediction_data_table.setItem(i, 2, interval_item)
            else:
                interval_item = QTableWidgetItem("N/A")
                prediction_data_table.setItem(i, 2, interval_item)

def main():
    """Main function to run the application."""
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
