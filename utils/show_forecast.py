"""
Simple script to show forecast data
"""

import pandas as pd
import sys

# Specify the forecast file
forecast_file = "etc/Forecast/Forecast_ADVIK (IND)_MECHANICAL SEAL ( BB-0030451 ).csv"

# Load the forecast data
try:
    forecast = pd.read_csv(forecast_file)
    print(f"Loaded forecast data from {forecast_file}")
    print(f"Shape: {forecast.shape}")
    
    # Convert date column to datetime
    if 'ds' in forecast.columns:
        forecast['ds'] = pd.to_datetime(forecast['ds'])
        date_col = 'ds'
    elif 'date' in forecast.columns:
        forecast['date'] = pd.to_datetime(forecast['date'])
        date_col = 'date'
    else:
        print("No date column found in the forecast data.")
        sys.exit(1)
    
    # Identify the prediction column
    if 'yhat' in forecast.columns:
        pred_col = 'yhat'
    elif 'predicted_quantity' in forecast.columns:
        pred_col = 'predicted_quantity'
    else:
        print("No prediction column found in the forecast data.")
        sys.exit(1)
    
    # Display the forecast data
    print("\nForecast Data:")
    print(forecast[[date_col, pred_col]].to_string(index=False))
    
    # Calculate statistics
    print("\nForecast Statistics:")
    print(f"Min: {forecast[pred_col].min():.2f}")
    print(f"Max: {forecast[pred_col].max():.2f}")
    print(f"Mean: {forecast[pred_col].mean():.2f}")
    print(f"Median: {forecast[pred_col].median():.2f}")
    print(f"Std Dev: {forecast[pred_col].std():.2f}")
    
    # Calculate month-to-month growth rates
    forecast['growth_rate'] = forecast[pred_col].pct_change() * 100
    
    print("\nMonth-to-Month Growth Rates (%):")
    for i in range(1, min(13, len(forecast))):
        date = forecast[date_col].iloc[i].strftime('%Y-%m')
        growth = forecast['growth_rate'].iloc[i]
        print(f"{date}: {growth:.2f}%")
    
except Exception as e:
    print(f"Error processing forecast data: {str(e)}")
