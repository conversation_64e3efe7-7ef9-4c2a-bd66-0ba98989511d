"""
Temporal causal reasoning for TCI.

This module provides temporal causal reasoning capabilities for the TCI framework,
enabling causal inference in temporal data.
"""

import numpy as np
import networkx as nx
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional, Union, Set, Any, Callable
import logging
import warnings
import time
from collections import deque

from tci.temporal.models import TemporalCausalGraph

logger = logging.getLogger(__name__)


class TemporalCausalReasoning:
    """
    Temporal causal reasoning.
    
    This class provides temporal causal reasoning capabilities, enabling causal
    inference in temporal data.
    
    Parameters
    ----------
    causal_graph : TemporalCausalGraph
        Temporal causal graph
    """
    
    def __init__(self, causal_graph: TemporalCausalGraph):
        self.causal_graph = causal_graph
    
    def get_causal_effect(
        self,
        source: str,
        target: str,
        source_lag: int,
        target_lag: int
    ) -> float:
        """
        Get the causal effect of one variable on another.
        
        Parameters
        ----------
        source : str
            Source variable name
        target : str
            Target variable name
        source_lag : int
            Source time lag
        target_lag : int
            Target time lag
            
        Returns
        -------
        float
            Causal effect
        """
        # Check if there is a direct edge
        if self.causal_graph.has_edge(source, target, source_lag, target_lag):
            return self.causal_graph.get_edge_weight(source, target, source_lag, target_lag)
        
        # Check if there is a path
        paths = self.get_causal_paths(source, target, source_lag, target_lag)
        
        if not paths:
            return 0.0
        
        # Compute total effect
        total_effect = 0.0
        
        for path in paths:
            # Compute path effect
            path_effect = 1.0
            
            for i in range(len(path) - 1):
                source_node = path[i]
                target_node = path[i + 1]
                
                source_name, source_lag_str = source_node.rsplit('_', 1)
                target_name, target_lag_str = target_node.rsplit('_', 1)
                
                source_lag_path = int(source_lag_str)
                target_lag_path = int(target_lag_str)
                
                edge_weight = self.causal_graph.get_edge_weight(
                    source_name, target_name, source_lag_path, target_lag_path
                )
                
                path_effect *= edge_weight
            
            # Add path effect to total effect
            total_effect += path_effect
        
        return total_effect
    
    def get_causal_paths(
        self,
        source: str,
        target: str,
        source_lag: int,
        target_lag: int
    ) -> List[List[str]]:
        """
        Get all causal paths from one variable to another.
        
        Parameters
        ----------
        source : str
            Source variable name
        target : str
            Target variable name
        source_lag : int
            Source time lag
        target_lag : int
            Target time lag
            
        Returns
        -------
        List[List[str]]
            List of causal paths, where each path is a list of node names
        """
        # Get source and target nodes
        source_node = f"{source}_{source_lag}"
        target_node = f"{target}_{target_lag}"
        
        # Get all paths
        G = self.causal_graph.to_networkx()
        
        try:
            paths = list(nx.all_simple_paths(G, source_node, target_node))
        except nx.NetworkXNoPath:
            paths = []
        
        return paths
    
    def is_d_separated(
        self,
        source: str,
        target: str,
        source_lag: int,
        target_lag: int,
        conditioning_set: Optional[List[Tuple[str, int]]] = None
    ) -> bool:
        """
        Check if two variables are d-separated given a conditioning set.
        
        Parameters
        ----------
        source : str
            Source variable name
        target : str
            Target variable name
        source_lag : int
            Source time lag
        target_lag : int
            Target time lag
        conditioning_set : Optional[List[Tuple[str, int]]], optional
            Conditioning set of (variable_name, time_lag) tuples, by default None
            
        Returns
        -------
        bool
            True if the variables are d-separated, False otherwise
        """
        # Get source and target nodes
        source_node = f"{source}_{source_lag}"
        target_node = f"{target}_{target_lag}"
        
        # Get conditioning set nodes
        if conditioning_set is None:
            conditioning_set_nodes = []
        else:
            conditioning_set_nodes = [f"{var}_{lag}" for var, lag in conditioning_set]
        
        # Check d-separation
        G = self.causal_graph.to_networkx()
        
        return nx.d_separation(G, {source_node}, {target_node}, set(conditioning_set_nodes))
    
    def get_markov_blanket(self, variable: str, time_lag: int) -> List[Tuple[str, int]]:
        """
        Get the Markov blanket of a variable.
        
        Parameters
        ----------
        variable : str
            Variable name
        time_lag : int
            Time lag
            
        Returns
        -------
        List[Tuple[str, int]]
            List of (variable_name, time_lag) tuples in the Markov blanket
        """
        # Get node
        node = f"{variable}_{time_lag}"
        
        # Get graph
        G = self.causal_graph.to_networkx()
        
        # Get parents
        parents = list(G.predecessors(node))
        
        # Get children
        children = list(G.successors(node))
        
        # Get parents of children
        parents_of_children = []
        for child in children:
            parents_of_children.extend(list(G.predecessors(child)))
        
        # Remove the variable itself from parents of children
        parents_of_children = [p for p in parents_of_children if p != node]
        
        # Combine all nodes in the Markov blanket
        markov_blanket_nodes = parents + children + parents_of_children
        
        # Convert to (variable_name, time_lag) tuples
        markov_blanket = []
        for node in markov_blanket_nodes:
            var, lag = node.rsplit('_', 1)
            markov_blanket.append((var, int(lag)))
        
        return markov_blanket
    
    def get_ancestors(self, variable: str, time_lag: int) -> List[Tuple[str, int]]:
        """
        Get the ancestors of a variable.
        
        Parameters
        ----------
        variable : str
            Variable name
        time_lag : int
            Time lag
            
        Returns
        -------
        List[Tuple[str, int]]
            List of (variable_name, time_lag) tuples that are ancestors
        """
        # Get node
        node = f"{variable}_{time_lag}"
        
        # Get graph
        G = self.causal_graph.to_networkx()
        
        # Get ancestors
        ancestor_nodes = nx.ancestors(G, node)
        
        # Convert to (variable_name, time_lag) tuples
        ancestors = []
        for node in ancestor_nodes:
            var, lag = node.rsplit('_', 1)
            ancestors.append((var, int(lag)))
        
        return ancestors
    
    def get_descendants(self, variable: str, time_lag: int) -> List[Tuple[str, int]]:
        """
        Get the descendants of a variable.
        
        Parameters
        ----------
        variable : str
            Variable name
        time_lag : int
            Time lag
            
        Returns
        -------
        List[Tuple[str, int]]
            List of (variable_name, time_lag) tuples that are descendants
        """
        # Get node
        node = f"{variable}_{time_lag}"
        
        # Get graph
        G = self.causal_graph.to_networkx()
        
        # Get descendants
        descendant_nodes = nx.descendants(G, node)
        
        # Convert to (variable_name, time_lag) tuples
        descendants = []
        for node in descendant_nodes:
            var, lag = node.rsplit('_', 1)
            descendants.append((var, int(lag)))
        
        return descendants
class TemporalDSeparation:
    """
    Temporal d-separation.
    
    This class provides temporal d-separation capabilities, enabling causal
    inference in temporal data.
    
    Parameters
    ----------
    causal_graph : TemporalCausalGraph
        Temporal causal graph
    """
    
    def __init__(self, causal_graph: TemporalCausalGraph):
        self.causal_graph = causal_graph
    
    def is_d_separated(
        self,
        X: List[Tuple[str, int]],
        Y: List[Tuple[str, int]],
        Z: Optional[List[Tuple[str, int]]] = None
    ) -> bool:
        """
        Check if two sets of variables are d-separated given a conditioning set.
        
        Parameters
        ----------
        X : List[Tuple[str, int]]
            First set of (variable_name, time_lag) tuples
        Y : List[Tuple[str, int]]
            Second set of (variable_name, time_lag) tuples
        Z : Optional[List[Tuple[str, int]]], optional
            Conditioning set of (variable_name, time_lag) tuples, by default None
            
        Returns
        -------
        bool
            True if the sets are d-separated, False otherwise
        """
        # Convert to node names
        X_nodes = [f"{var}_{lag}" for var, lag in X]
        Y_nodes = [f"{var}_{lag}" for var, lag in Y]
        
        if Z is None:
            Z_nodes = []
        else:
            Z_nodes = [f"{var}_{lag}" for var, lag in Z]
        
        # Check d-separation
        G = self.causal_graph.to_networkx()
        
        return nx.d_separation(G, set(X_nodes), set(Y_nodes), set(Z_nodes))
    
    def find_minimal_separator(
        self,
        X: List[Tuple[str, int]],
        Y: List[Tuple[str, int]]
    ) -> List[Tuple[str, int]]:
        """
        Find a minimal set of variables that d-separates two sets of variables.
        
        Parameters
        ----------
        X : List[Tuple[str, int]]
            First set of (variable_name, time_lag) tuples
        Y : List[Tuple[str, int]]
            Second set of (variable_name, time_lag) tuples
            
        Returns
        -------
        List[Tuple[str, int]]
            Minimal separating set of (variable_name, time_lag) tuples
        """
        # Convert to node names
        X_nodes = [f"{var}_{lag}" for var, lag in X]
        Y_nodes = [f"{var}_{lag}" for var, lag in Y]
        
        # Get graph
        G = self.causal_graph.to_networkx()
        
        # Get all nodes
        all_nodes = list(G.nodes())
        
        # Remove X and Y nodes
        candidate_nodes = [node for node in all_nodes if node not in X_nodes and node not in Y_nodes]
        
        # Find minimal separator
        separator = []
        
        # Try to find a minimal separator
        for node in candidate_nodes:
            # Check if adding this node to the separator makes X and Y d-separated
            if nx.d_separation(G, set(X_nodes), set(Y_nodes), set(separator + [node])):
                separator.append(node)
                
                # Check if we can remove any node from the separator
                for i in range(len(separator) - 1):
                    if nx.d_separation(G, set(X_nodes), set(Y_nodes), set(separator[:i] + separator[i+1:])):
                        separator = separator[:i] + separator[i+1:]
        
        # Convert to (variable_name, time_lag) tuples
        separator_tuples = []
        for node in separator:
            var, lag = node.rsplit('_', 1)
            separator_tuples.append((var, int(lag)))
        
        return separator_tuples
    
    def get_all_d_separations(self) -> List[Tuple[List[Tuple[str, int]], List[Tuple[str, int]], List[Tuple[str, int]]]]:
        """
        Get all d-separation relationships in the graph.
        
        Returns
        -------
        List[Tuple[List[Tuple[str, int]], List[Tuple[str, int]], List[Tuple[str, int]]]]
            List of (X, Y, Z) tuples, where X and Y are d-separated given Z
        """
        # Get graph
        G = self.causal_graph.to_networkx()
        
        # Get all nodes
        all_nodes = list(G.nodes())
        
        # Convert to (variable_name, time_lag) tuples
        all_tuples = []
        for node in all_nodes:
            var, lag = node.rsplit('_', 1)
            all_tuples.append((var, int(lag)))
        
        # Find all d-separations
        d_separations = []
        
        # Check all pairs of variables
        for i, x in enumerate(all_tuples):
            for j, y in enumerate(all_tuples[i+1:], i+1):
                # Find minimal separator
                separator = self.find_minimal_separator([x], [y])
                
                if separator:
                    d_separations.append(([x], [y], separator))
        
        return d_separations
    
    def test_d_separation(
        self,
        X: List[Tuple[str, int]],
        Y: List[Tuple[str, int]],
        Z: Optional[List[Tuple[str, int]]] = None,
        data: Optional[np.ndarray] = None,
        variable_names: Optional[List[str]] = None,
        test: str = 'correlation',
        significance_level: float = 0.05
    ) -> Tuple[bool, float]:
        """
        Test d-separation using data.
        
        Parameters
        ----------
        X : List[Tuple[str, int]]
            First set of (variable_name, time_lag) tuples
        Y : List[Tuple[str, int]]
            Second set of (variable_name, time_lag) tuples
        Z : Optional[List[Tuple[str, int]]], optional
            Conditioning set of (variable_name, time_lag) tuples, by default None
        data : Optional[np.ndarray], optional
            Time-series data of shape (n_samples, n_variables), by default None
        variable_names : Optional[List[str]], optional
            Names of the variables in the data, by default None
        test : str, optional
            Test to use, by default 'correlation'
        significance_level : float, optional
            Significance level, by default 0.05
            
        Returns
        -------
        Tuple[bool, float]
            Tuple of (is_d_separated, p_value)
        """
        if data is None:
            # Use structural d-separation
            is_d_separated = self.is_d_separated(X, Y, Z)
            return is_d_separated, 1.0 if is_d_separated else 0.0
        
        if variable_names is None:
            variable_names = self.causal_graph.variable_names
        
        # Prepare data
        X_data, Y_data, Z_data = self._prepare_data(X, Y, Z, data, variable_names)
        
        # Perform test
        if test == 'correlation':
            is_d_separated, p_value = self._correlation_test(X_data, Y_data, Z_data)
        elif test == 'partial_correlation':
            is_d_separated, p_value = self._partial_correlation_test(X_data, Y_data, Z_data)
        else:
            raise ValueError(f"Unknown test: {test}")
        
        return is_d_separated, p_value
    
    def _prepare_data(
        self,
        X: List[Tuple[str, int]],
        Y: List[Tuple[str, int]],
        Z: Optional[List[Tuple[str, int]]],
        data: np.ndarray,
        variable_names: List[str]
    ) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray]]:
        """
        Prepare data for d-separation test.
        
        Parameters
        ----------
        X : List[Tuple[str, int]]
            First set of (variable_name, time_lag) tuples
        Y : List[Tuple[str, int]]
            Second set of (variable_name, time_lag) tuples
        Z : Optional[List[Tuple[str, int]]]
            Conditioning set of (variable_name, time_lag) tuples
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
        variable_names : List[str]
            Names of the variables in the data
            
        Returns
        -------
        Tuple[np.ndarray, np.ndarray, Optional[np.ndarray]]
            Tuple of (X_data, Y_data, Z_data)
        """
        # Get maximum lag
        max_lag = 0
        for _, lag in X + Y + (Z or []):
            max_lag = max(max_lag, lag)
        
        # Get number of samples
        n_samples = data.shape[0] - max_lag
        
        # Prepare X data
        X_data = np.zeros((n_samples, len(X)))
        for i, (var, lag) in enumerate(X):
            var_idx = variable_names.index(var)
            X_data[:, i] = data[max_lag-lag:n_samples+max_lag-lag, var_idx]
        
        # Prepare Y data
        Y_data = np.zeros((n_samples, len(Y)))
        for i, (var, lag) in enumerate(Y):
            var_idx = variable_names.index(var)
            Y_data[:, i] = data[max_lag-lag:n_samples+max_lag-lag, var_idx]
        
        # Prepare Z data
        if Z:
            Z_data = np.zeros((n_samples, len(Z)))
            for i, (var, lag) in enumerate(Z):
                var_idx = variable_names.index(var)
                Z_data[:, i] = data[max_lag-lag:n_samples+max_lag-lag, var_idx]
        else:
            Z_data = None
        
        return X_data, Y_data, Z_data
    
    def _correlation_test(
        self,
        X: np.ndarray,
        Y: np.ndarray,
        Z: Optional[np.ndarray]
    ) -> Tuple[bool, float]:
        """
        Perform correlation test.
        
        Parameters
        ----------
        X : np.ndarray
            First set of variables
        Y : np.ndarray
            Second set of variables
        Z : Optional[np.ndarray]
            Conditioning set of variables
            
        Returns
        -------
        Tuple[bool, float]
            Tuple of (is_d_separated, p_value)
        """
        from scipy import stats
        
        # Compute correlation
        corr, p_value = stats.pearsonr(X.mean(axis=1), Y.mean(axis=1))
        
        # Check if d-separated
        is_d_separated = p_value > 0.05
        
        return is_d_separated, p_value
    
    def _partial_correlation_test(
        self,
        X: np.ndarray,
        Y: np.ndarray,
        Z: Optional[np.ndarray]
    ) -> Tuple[bool, float]:
        """
        Perform partial correlation test.
        
        Parameters
        ----------
        X : np.ndarray
            First set of variables
        Y : np.ndarray
            Second set of variables
        Z : Optional[np.ndarray]
            Conditioning set of variables
            
        Returns
        -------
        Tuple[bool, float]
            Tuple of (is_d_separated, p_value)
        """
        from scipy import stats
        
        if Z is None:
            # Use regular correlation
            return self._correlation_test(X, Y, Z)
        
        # Compute partial correlation
        X_mean = X.mean(axis=1)
        Y_mean = Y.mean(axis=1)
        Z_mean = Z.mean(axis=1)
        
        # Regress X on Z
        beta_x = np.linalg.lstsq(Z_mean.reshape(-1, 1), X_mean, rcond=None)[0]
        res_x = X_mean - Z_mean * beta_x
        
        # Regress Y on Z
        beta_y = np.linalg.lstsq(Z_mean.reshape(-1, 1), Y_mean, rcond=None)[0]
        res_y = Y_mean - Z_mean * beta_y
        
        # Compute correlation between residuals
        corr, p_value = stats.pearsonr(res_x, res_y)
        
        # Check if d-separated
        is_d_separated = p_value > 0.05
        
        return is_d_separated, p_value
class TemporalDoOperator:
    """
    Temporal do-operator.
    
    This class implements the temporal do-operator, which enables causal
    intervention in temporal data.
    
    Parameters
    ----------
    causal_graph : TemporalCausalGraph
        Temporal causal graph
    """
    
    def __init__(self, causal_graph: TemporalCausalGraph):
        self.causal_graph = causal_graph
    
    def do(
        self,
        variable: str,
        time_lag: int,
        value: Optional[float] = None
    ) -> TemporalCausalGraph:
        """
        Apply the do-operator to a variable.
        
        Parameters
        ----------
        variable : str
            Variable name
        time_lag : int
            Time lag
        value : Optional[float], optional
            Value to set, by default None
            
        Returns
        -------
        TemporalCausalGraph
            Modified causal graph
        """
        # Create a copy of the causal graph
        G = self.causal_graph.to_networkx()
        
        # Get node
        node = f"{variable}_{time_lag}"
        
        # Remove incoming edges
        incoming_edges = list(G.in_edges(node))
        for source, target in incoming_edges:
            G.remove_edge(source, target)
        
        # Create modified causal graph
        modified_graph = TemporalCausalGraph.from_networkx(G)
        
        return modified_graph
    
    def do_multiple(
        self,
        interventions: List[Tuple[str, int, Optional[float]]]
    ) -> TemporalCausalGraph:
        """
        Apply multiple do-operators.
        
        Parameters
        ----------
        interventions : List[Tuple[str, int, Optional[float]]]
            List of (variable_name, time_lag, value) tuples
            
        Returns
        -------
        TemporalCausalGraph
            Modified causal graph
        """
        # Create a copy of the causal graph
        G = self.causal_graph.to_networkx()
        
        # Apply interventions
        for variable, time_lag, _ in interventions:
            # Get node
            node = f"{variable}_{time_lag}"
            
            # Remove incoming edges
            incoming_edges = list(G.in_edges(node))
            for source, target in incoming_edges:
                G.remove_edge(source, target)
        
        # Create modified causal graph
        modified_graph = TemporalCausalGraph.from_networkx(G)
        
        return modified_graph
    
    def predict_intervention_effect(
        self,
        model: Any,
        data: np.ndarray,
        variable: str,
        time_lag: int,
        value: float
    ) -> np.ndarray:
        """
        Predict the effect of an intervention.
        
        Parameters
        ----------
        model : Any
            Model to use for prediction
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
        variable : str
            Variable name
        time_lag : int
            Time lag
        value : float
            Value to set
            
        Returns
        -------
        np.ndarray
            Predicted effect
        """
        # Get variable index
        variable_idx = self.causal_graph.variable_names.index(variable)
        
        # Create a copy of the data
        data_copy = data.copy()
        
        # Apply intervention
        data_copy[-time_lag, variable_idx] = value
        
        # Predict effect
        if hasattr(model, 'predict'):
            prediction = model.predict(data_copy)
        elif hasattr(model, 'forecast'):
            prediction = model.forecast(data_copy, 1)
        else:
            raise ValueError("Model must have predict or forecast method")
        
        return prediction
    
    def predict_multiple_intervention_effects(
        self,
        model: Any,
        data: np.ndarray,
        interventions: List[Tuple[str, int, float]]
    ) -> np.ndarray:
        """
        Predict the effect of multiple interventions.
        
        Parameters
        ----------
        model : Any
            Model to use for prediction
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
        interventions : List[Tuple[str, int, float]]
            List of (variable_name, time_lag, value) tuples
            
        Returns
        -------
        np.ndarray
            Predicted effect
        """
        # Create a copy of the data
        data_copy = data.copy()
        
        # Apply interventions
        for variable, time_lag, value in interventions:
            # Get variable index
            variable_idx = self.causal_graph.variable_names.index(variable)
            
            # Apply intervention
            data_copy[-time_lag, variable_idx] = value
        
        # Predict effect
        if hasattr(model, 'predict'):
            prediction = model.predict(data_copy)
        elif hasattr(model, 'forecast'):
            prediction = model.forecast(data_copy, 1)
        else:
            raise ValueError("Model must have predict or forecast method")
        
        return prediction
    
    def get_backdoor_adjustment_set(
        self,
        source: str,
        target: str,
        source_lag: int,
        target_lag: int
    ) -> List[Tuple[str, int]]:
        """
        Get the backdoor adjustment set for causal effect estimation.
        
        Parameters
        ----------
        source : str
            Source variable name
        target : str
            Target variable name
        source_lag : int
            Source time lag
        target_lag : int
            Target time lag
            
        Returns
        -------
        List[Tuple[str, int]]
            Backdoor adjustment set of (variable_name, time_lag) tuples
        """
        # Get source and target nodes
        source_node = f"{source}_{source_lag}"
        target_node = f"{target}_{target_lag}"
        
        # Get graph
        G = self.causal_graph.to_networkx()
        
        # Get parents of source
        parents = list(G.predecessors(source_node))
        
        # Convert to (variable_name, time_lag) tuples
        adjustment_set = []
        for node in parents:
            var, lag = node.rsplit('_', 1)
            adjustment_set.append((var, int(lag)))
        
        return adjustment_set
    
    def get_frontdoor_adjustment_set(
        self,
        source: str,
        target: str,
        source_lag: int,
        target_lag: int
    ) -> List[Tuple[str, int]]:
        """
        Get the frontdoor adjustment set for causal effect estimation.
        
        Parameters
        ----------
        source : str
            Source variable name
        target : str
            Target variable name
        source_lag : int
            Source time lag
        target_lag : int
            Target time lag
            
        Returns
        -------
        List[Tuple[str, int]]
            Frontdoor adjustment set of (variable_name, time_lag) tuples
        """
        # Get source and target nodes
        source_node = f"{source}_{source_lag}"
        target_node = f"{target}_{target_lag}"
        
        # Get graph
        G = self.causal_graph.to_networkx()
        
        # Get all paths from source to target
        try:
            paths = list(nx.all_simple_paths(G, source_node, target_node))
        except nx.NetworkXNoPath:
            return []
        
        # Get all nodes in all paths
        path_nodes = set()
        for path in paths:
            path_nodes.update(path[1:-1])  # Exclude source and target
        
        # Convert to (variable_name, time_lag) tuples
        adjustment_set = []
        for node in path_nodes:
            var, lag = node.rsplit('_', 1)
            adjustment_set.append((var, int(lag)))
        
        return adjustment_set
class TemporalCounterfactual:
    """
    Temporal counterfactual reasoning.
    
    This class implements temporal counterfactual reasoning, which enables
    counterfactual inference in temporal data.
    
    Parameters
    ----------
    causal_graph : TemporalCausalGraph
        Temporal causal graph
    model : Any
        Model to use for prediction
    """
    
    def __init__(self, causal_graph: TemporalCausalGraph, model: Any):
        self.causal_graph = causal_graph
        self.model = model
        self.do_operator = TemporalDoOperator(causal_graph)
    
    def counterfactual(
        self,
        data: np.ndarray,
        variable: str,
        time_lag: int,
        value: float
    ) -> np.ndarray:
        """
        Compute counterfactual prediction.
        
        Parameters
        ----------
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
        variable : str
            Variable name
        time_lag : int
            Time lag
        value : float
            Counterfactual value
            
        Returns
        -------
        np.ndarray
            Counterfactual prediction
        """
        # Step 1: Abduction - compute exogenous variables
        exogenous_variables = self._compute_exogenous_variables(data)
        
        # Step 2: Action - apply intervention
        modified_graph = self.do_operator.do(variable, time_lag, value)
        
        # Step 3: Prediction - compute counterfactual
        counterfactual_prediction = self._predict_with_exogenous_variables(
            exogenous_variables, modified_graph, variable, time_lag, value
        )
        
        return counterfactual_prediction
    
    def multiple_counterfactuals(
        self,
        data: np.ndarray,
        interventions: List[Tuple[str, int, float]]
    ) -> np.ndarray:
        """
        Compute multiple counterfactual predictions.
        
        Parameters
        ----------
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
        interventions : List[Tuple[str, int, float]]
            List of (variable_name, time_lag, value) tuples
            
        Returns
        -------
        np.ndarray
            Counterfactual predictions
        """
        # Step 1: Abduction - compute exogenous variables
        exogenous_variables = self._compute_exogenous_variables(data)
        
        # Step 2: Action - apply interventions
        modified_graph = self.do_operator.do_multiple(interventions)
        
        # Step 3: Prediction - compute counterfactuals
        counterfactual_predictions = self._predict_with_exogenous_variables_multiple(
            exogenous_variables, modified_graph, interventions
        )
        
        return counterfactual_predictions
    
    def _compute_exogenous_variables(self, data: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Compute exogenous variables.
        
        Parameters
        ----------
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
            
        Returns
        -------
        Dict[str, np.ndarray]
            Dictionary mapping variable names to exogenous variables
        """
        # Get variable names
        variable_names = self.causal_graph.variable_names
        
        # Initialize exogenous variables
        exogenous_variables = {}
        
        # Compute exogenous variables for each variable
        for i, variable in enumerate(variable_names):
            # Get parents
            parents = []
            for lag in range(1, self.causal_graph.max_lag + 1):
                parents.extend(self.causal_graph.get_parents(variable, lag))
            
            # If no parents, use the variable itself as exogenous
            if not parents:
                exogenous_variables[variable] = data[:, i]
                continue
            
            # Prepare data for regression
            X = np.zeros((data.shape[0] - self.causal_graph.max_lag, len(parents)))
            y = data[self.causal_graph.max_lag:, i]
            
            for j, (parent_name, parent_lag) in enumerate(parents):
                parent_idx = variable_names.index(parent_name)
                X[:, j] = data[self.causal_graph.max_lag - parent_lag:-parent_lag, parent_idx]
            
            # Perform regression
            beta = np.linalg.lstsq(X, y, rcond=None)[0]
            
            # Compute residuals
            residuals = y - X @ beta
            
            # Store exogenous variables
            exogenous_variables[variable] = residuals
        
        return exogenous_variables
    
    def _predict_with_exogenous_variables(
        self,
        exogenous_variables: Dict[str, np.ndarray],
        modified_graph: TemporalCausalGraph,
        variable: str,
        time_lag: int,
        value: float
    ) -> np.ndarray:
        """
        Predict with exogenous variables.
        
        Parameters
        ----------
        exogenous_variables : Dict[str, np.ndarray]
            Dictionary mapping variable names to exogenous variables
        modified_graph : TemporalCausalGraph
            Modified causal graph
        variable : str
            Variable name
        time_lag : int
            Time lag
        value : float
            Counterfactual value
            
        Returns
        -------
        np.ndarray
            Counterfactual prediction
        """
        # Get variable names
        variable_names = self.causal_graph.variable_names
        
        # Initialize counterfactual data
        n_samples = next(iter(exogenous_variables.values())).shape[0] + self.causal_graph.max_lag
        counterfactual_data = np.zeros((n_samples, len(variable_names)))
        
        # Set initial values
        counterfactual_data[:self.causal_graph.max_lag] = np.random.randn(self.causal_graph.max_lag, len(variable_names))
        
        # Apply intervention
        variable_idx = variable_names.index(variable)
        counterfactual_data[self.causal_graph.max_lag - time_lag, variable_idx] = value
        
        # Compute counterfactual for each time step
        for t in range(self.causal_graph.max_lag, n_samples):
            for i, var in enumerate(variable_names):
                # Skip intervention variable at intervention time
                if var == variable and t == self.causal_graph.max_lag - time_lag:
                    continue
                
                # Get parents
                parents = []
                for lag in range(1, self.causal_graph.max_lag + 1):
                    parents.extend(modified_graph.get_parents(var, lag))
                
                # If no parents, use exogenous variable
                if not parents:
                    counterfactual_data[t, i] = exogenous_variables[var][t - self.causal_graph.max_lag]
                    continue
                
                # Prepare data for prediction
                X = np.zeros(len(parents))
                
                for j, (parent_name, parent_lag) in enumerate(parents):
                    parent_idx = variable_names.index(parent_name)
                    X[j] = counterfactual_data[t - parent_lag, parent_idx]
                
                # Perform prediction
                beta = np.linalg.lstsq(
                    np.ones((1, len(parents))), np.ones(1), rcond=None
                )[0]  # Placeholder
                
                # Add exogenous variable
                counterfactual_data[t, i] = X @ beta + exogenous_variables[var][t - self.causal_graph.max_lag]
        
        return counterfactual_data
    
    def _predict_with_exogenous_variables_multiple(
        self,
        exogenous_variables: Dict[str, np.ndarray],
        modified_graph: TemporalCausalGraph,
        interventions: List[Tuple[str, int, float]]
    ) -> np.ndarray:
        """
        Predict with exogenous variables for multiple interventions.
        
        Parameters
        ----------
        exogenous_variables : Dict[str, np.ndarray]
            Dictionary mapping variable names to exogenous variables
        modified_graph : TemporalCausalGraph
            Modified causal graph
        interventions : List[Tuple[str, int, float]]
            List of (variable_name, time_lag, value) tuples
            
        Returns
        -------
        np.ndarray
            Counterfactual predictions
        """
        # Get variable names
        variable_names = self.causal_graph.variable_names
        
        # Initialize counterfactual data
        n_samples = next(iter(exogenous_variables.values())).shape[0] + self.causal_graph.max_lag
        counterfactual_data = np.zeros((n_samples, len(variable_names)))
        
        # Set initial values
        counterfactual_data[:self.causal_graph.max_lag] = np.random.randn(self.causal_graph.max_lag, len(variable_names))
        
        # Apply interventions
        for variable, time_lag, value in interventions:
            variable_idx = variable_names.index(variable)
            counterfactual_data[self.causal_graph.max_lag - time_lag, variable_idx] = value
        
        # Create set of intervention variables and times
        intervention_set = {(var, lag) for var, lag, _ in interventions}
        
        # Compute counterfactual for each time step
        for t in range(self.causal_graph.max_lag, n_samples):
            for i, var in enumerate(variable_names):
                # Skip intervention variables at intervention times
                if any((var, t - self.causal_graph.max_lag + lag) in intervention_set for lag in range(1, self.causal_graph.max_lag + 1)):
                    continue
                
                # Get parents
                parents = []
                for lag in range(1, self.causal_graph.max_lag + 1):
                    parents.extend(modified_graph.get_parents(var, lag))
                
                # If no parents, use exogenous variable
                if not parents:
                    counterfactual_data[t, i] = exogenous_variables[var][t - self.causal_graph.max_lag]
                    continue
                
                # Prepare data for prediction
                X = np.zeros(len(parents))
                
                for j, (parent_name, parent_lag) in enumerate(parents):
                    parent_idx = variable_names.index(parent_name)
                    X[j] = counterfactual_data[t - parent_lag, parent_idx]
                
                # Perform prediction
                beta = np.linalg.lstsq(
                    np.ones((1, len(parents))), np.ones(1), rcond=None
                )[0]  # Placeholder
                
                # Add exogenous variable
                counterfactual_data[t, i] = X @ beta + exogenous_variables[var][t - self.causal_graph.max_lag]
        
        return counterfactual_data
    
    def probability_of_necessity(
        self,
        data: np.ndarray,
        cause_variable: str,
        cause_time_lag: int,
        cause_value: float,
        effect_variable: str,
        effect_time_lag: int,
        effect_value: float
    ) -> float:
        """
        Compute the probability of necessity.
        
        Parameters
        ----------
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
        cause_variable : str
            Cause variable name
        cause_time_lag : int
            Cause time lag
        cause_value : float
            Cause value
        effect_variable : str
            Effect variable name
        effect_time_lag : int
            Effect time lag
        effect_value : float
            Effect value
            
        Returns
        -------
        float
            Probability of necessity
        """
        # Get variable indices
        cause_idx = self.causal_graph.variable_names.index(cause_variable)
        effect_idx = self.causal_graph.variable_names.index(effect_variable)
        
        # Check if the effect occurred
        effect_occurred = data[-effect_time_lag, effect_idx] == effect_value
        
        if not effect_occurred:
            return 0.0
        
        # Check if the cause occurred
        cause_occurred = data[-cause_time_lag, cause_idx] == cause_value
        
        if not cause_occurred:
            return 0.0
        
        # Compute counterfactual
        counterfactual_data = self.counterfactual(data, cause_variable, cause_time_lag, 1 - cause_value)
        
        # Check if the effect would have occurred in the counterfactual
        counterfactual_effect = counterfactual_data[-effect_time_lag, effect_idx] == effect_value
        
        # Compute probability of necessity
        if counterfactual_effect:
            return 0.0  # The effect would have occurred anyway
        else:
            return 1.0  # The effect would not have occurred without the cause
    
    def probability_of_sufficiency(
        self,
        data: np.ndarray,
        cause_variable: str,
        cause_time_lag: int,
        cause_value: float,
        effect_variable: str,
        effect_time_lag: int,
        effect_value: float
    ) -> float:
        """
        Compute the probability of sufficiency.
        
        Parameters
        ----------
        data : np.ndarray
            Time-series data of shape (n_samples, n_variables)
        cause_variable : str
            Cause variable name
        cause_time_lag : int
            Cause time lag
        cause_value : float
            Cause value
        effect_variable : str
            Effect variable name
        effect_time_lag : int
            Effect time lag
        effect_value : float
            Effect value
            
        Returns
        -------
        float
            Probability of sufficiency
        """
        # Get variable indices
        cause_idx = self.causal_graph.variable_names.index(cause_variable)
        effect_idx = self.causal_graph.variable_names.index(effect_variable)
        
        # Check if the cause occurred
        cause_occurred = data[-cause_time_lag, cause_idx] == cause_value
        
        if not cause_occurred:
            return 0.0
        
        # Check if the effect occurred
        effect_occurred = data[-effect_time_lag, effect_idx] == effect_value
        
        if effect_occurred:
            return 1.0
        
        # Compute counterfactual
        counterfactual_data = self.counterfactual(data, cause_variable, cause_time_lag, cause_value)
        
        # Check if the effect would have occurred in the counterfactual
        counterfactual_effect = counterfactual_data[-effect_time_lag, effect_idx] == effect_value
        
        # Compute probability of sufficiency
        if counterfactual_effect:
            return 1.0  # The cause would have been sufficient
        else:
            return 0.0  # The cause would not have been sufficient
