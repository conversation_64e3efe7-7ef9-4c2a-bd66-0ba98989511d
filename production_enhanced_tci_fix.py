#!/usr/bin/env python3
"""
Production Enhanced TCI-fix Integration

Production-ready integration for enhanced TCI-fix with bias correction,
uncertainty quantification, and ensemble approach.
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import pickle

from tci_fix_integration import TCIFixIntegration, load_external_data
from folder_data_manager import FolderDataManager

class ProductionEnhancedTCIFix:
    """
    Production-ready enhanced TCI-fix integration.
    
    Features:
    - Bias correction based on recent performance
    - Uncertainty quantification with confidence bounds
    - Ensemble approach combining multiple methods
    - Volatility-aware modeling
    - Production-ready error handling
    """
    
    def __init__(self):
        """Initialize the production enhanced TCI-fix."""
        self.logger = logging.getLogger(__name__)
        self.base_integration = TCIFixIntegration()
        self.data_manager = FolderDataManager("dataset")
        
        # Enhanced model parameters
        self.bias_correction_enabled = True
        self.uncertainty_quantification_enabled = True
        self.ensemble_enabled = True
        
        # Performance tracking
        self.performance_log = []
    
    def train_enhanced_model(self, customer, product, force_retrain=False):
        """
        Train an enhanced model for a customer-product combination.
        
        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name
        force_retrain : bool
            Whether to force retraining
            
        Returns:
        --------
        tuple
            (success, message, model_info)
        """
        try:
            self.logger.info(f"Training enhanced model for {customer} - {product}")
            
            # First train the base TCI-fix model
            success, message = self.base_integration.train_model(customer, product, force_retrain)
            
            if not success:
                return False, f"Base model training failed: {message}", None
            
            # Load data for enhanced analysis
            success, load_message = self.data_manager.load_data_from_folder()
            if not success:
                return False, f"Data loading failed: {load_message}", None
            
            key = f"{customer}_{product}"
            if key not in self.data_manager.data:
                return False, f"No data found for {customer} - {product}", None
            
            data = self.data_manager.data[key].copy()
            
            # Calculate enhanced model parameters
            enhanced_params = self._calculate_enhanced_parameters(data)
            
            # Save enhanced parameters
            enhanced_model_path = self._get_enhanced_model_path(customer, product)
            os.makedirs(os.path.dirname(enhanced_model_path), exist_ok=True)
            
            with open(enhanced_model_path, 'wb') as f:
                pickle.dump(enhanced_params, f)
            
            self.logger.info(f"Enhanced model parameters saved: {enhanced_model_path}")
            
            return True, f"Enhanced model trained successfully", enhanced_params
            
        except Exception as e:
            error_msg = f"Error training enhanced model: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
    
    def predict_enhanced(self, customer, product, periods=12, start_date=None):
        """
        Generate enhanced predictions.

        Parameters:
        -----------
        customer : str
            Customer name
        product : str
            Product name
        periods : int
            Number of periods to predict
        start_date : str or datetime
            Start date for predictions

        Returns:
        --------
        pandas.DataFrame or None
            Enhanced predictions with uncertainty bounds
        """
        try:
            self.logger.info(f"Generating enhanced predictions for {customer} - {product}")

            # Try to get base predictions, but handle failures gracefully
            base_predictions = None
            try:
                base_predictions = self.base_integration.predict_future(customer, product, periods, start_date)
            except Exception as e:
                self.logger.warning(f"Base TCI-fix prediction failed: {e}")

            # If base predictions failed, create fallback predictions
            if base_predictions is None:
                self.logger.info("Creating fallback enhanced predictions")
                base_predictions = self._create_fallback_predictions(customer, product, periods, start_date)

                if base_predictions is None:
                    self.logger.error("Both base and fallback predictions failed")
                    return None

            # Load enhanced parameters
            enhanced_params = self._load_enhanced_parameters(customer, product)

            if enhanced_params is None:
                self.logger.warning("No enhanced parameters found, using basic enhancements")
                return self._add_basic_enhancements(base_predictions)

            # Apply enhancements
            enhanced_predictions = self._apply_enhancements(base_predictions, enhanced_params)

            # Add metadata
            enhanced_predictions['model_type'] = 'enhanced_tci_fix'
            enhanced_predictions['enhancement_version'] = '1.0'
            enhanced_predictions['prediction_timestamp'] = datetime.now()

            self.logger.info(f"Enhanced predictions generated: {len(enhanced_predictions)} periods")

            return enhanced_predictions

        except Exception as e:
            self.logger.error(f"Error generating enhanced predictions: {str(e)}")
            return None
    
    def _calculate_enhanced_parameters(self, data):
        """Calculate enhanced model parameters from historical data."""
        
        target_col = 'Quantity'
        
        # Calculate volatility level
        cv = data[target_col].std() / (data[target_col].mean() + 1e-6)
        
        if cv > 1.0:
            volatility_level = 'high'
            uncertainty_factor = 0.40
            ensemble_weights = [0.5, 0.3, 0.2]  # Conservative
        elif cv > 0.5:
            volatility_level = 'medium'
            uncertainty_factor = 0.25
            ensemble_weights = [0.6, 0.25, 0.15]  # Balanced
        else:
            volatility_level = 'low'
            uncertainty_factor = 0.15
            ensemble_weights = [0.7, 0.2, 0.1]  # Trust TCI-fix more
        
        # Calculate bias correction factor
        bias_correction_factor = self._calculate_bias_correction(data, target_col)
        
        # Calculate trend parameters
        recent_trend = self._calculate_recent_trend(data, target_col)
        
        enhanced_params = {
            'volatility_level': volatility_level,
            'uncertainty_factor': uncertainty_factor,
            'ensemble_weights': ensemble_weights,
            'bias_correction_factor': bias_correction_factor,
            'recent_trend': recent_trend,
            'data_mean': data[target_col].mean(),
            'data_std': data[target_col].std(),
            'zero_order_threshold': 100,
            'training_date': datetime.now(),
            'data_points': len(data)
        }
        
        return enhanced_params
    
    def _calculate_bias_correction(self, data, target_col):
        """Calculate bias correction factor."""
        try:
            # Simple approach: use recent data vs trend
            target_col = 'Quantity'
            recent_data = data.tail(12)  # Last 12 months
            
            if len(recent_data) < 6:
                return 1.0
            
            # Calculate trend-based expectation vs actual
            x = np.arange(len(recent_data))
            trend_coef = np.polyfit(x, recent_data[target_col], 1)[0]
            
            # If trend is positive but recent values are lower, apply upward correction
            recent_mean = recent_data[target_col].mean()
            overall_mean = data[target_col].mean()
            
            if recent_mean > 0 and overall_mean > 0:
                bias_factor = recent_mean / overall_mean
                # Limit to reasonable range
                bias_factor = np.clip(bias_factor, 0.7, 1.5)
                return bias_factor
            
            return 1.0
            
        except Exception:
            return 1.0
    
    def _calculate_recent_trend(self, data, target_col):
        """Calculate recent trend for ensemble component."""
        try:
            target_col = 'Quantity'
            recent_data = data.tail(6)  # Last 6 months
            
            if len(recent_data) < 3:
                return 0
            
            x = np.arange(len(recent_data))
            trend_coef = np.polyfit(x, recent_data[target_col], 1)[0]
            
            return trend_coef
            
        except Exception:
            return 0
    
    def _load_enhanced_parameters(self, customer, product):
        """Load enhanced parameters for a model."""
        try:
            enhanced_model_path = self._get_enhanced_model_path(customer, product)
            
            if not os.path.exists(enhanced_model_path):
                return None
            
            with open(enhanced_model_path, 'rb') as f:
                enhanced_params = pickle.load(f)
            
            return enhanced_params
            
        except Exception as e:
            self.logger.warning(f"Could not load enhanced parameters: {e}")
            return None
    
    def _apply_enhancements(self, base_predictions, enhanced_params):
        """Apply enhancements to base predictions."""
        
        enhanced_predictions = base_predictions.copy()
        
        # Find prediction column
        prediction_col = None
        for col in enhanced_predictions.columns:
            if 'predict' in col.lower() or 'forecast' in col.lower() or 'quantity' in col.lower():
                prediction_col = col
                break
        
        if prediction_col is None:
            return enhanced_predictions
        
        # Standardize column name
        if prediction_col != 'predicted_quantity':
            enhanced_predictions['predicted_quantity'] = enhanced_predictions[prediction_col]
        
        # Apply bias correction
        if self.bias_correction_enabled:
            enhanced_predictions['predicted_quantity'] *= enhanced_params['bias_correction_factor']
        
        # Apply ensemble approach
        if self.ensemble_enabled:
            enhanced_predictions = self._apply_ensemble(enhanced_predictions, enhanced_params)
        
        # Add uncertainty quantification
        if self.uncertainty_quantification_enabled:
            enhanced_predictions = self._add_uncertainty_bounds(enhanced_predictions, enhanced_params)
        
        return enhanced_predictions
    
    def _apply_ensemble(self, predictions, enhanced_params):
        """Apply ensemble approach."""
        
        tci_fix_pred = predictions['predicted_quantity'].copy()
        
        # Trend component
        trend_coef = enhanced_params['recent_trend']
        trend_predictions = []
        last_value = enhanced_params['data_mean']
        
        for i in range(len(predictions)):
            trend_pred = max(0, last_value + trend_coef * (i + 1))
            trend_predictions.append(trend_pred)
        
        trend_pred = np.array(trend_predictions)
        
        # Moving average component
        ma_pred = np.full(len(predictions), enhanced_params['data_mean'])
        
        # Apply ensemble weights
        weights = enhanced_params['ensemble_weights']
        
        ensemble_prediction = (
            weights[0] * tci_fix_pred +
            weights[1] * trend_pred +
            weights[2] * ma_pred
        )
        
        predictions['predicted_quantity'] = ensemble_prediction
        predictions['tci_fix_component'] = tci_fix_pred
        predictions['trend_component'] = trend_pred
        predictions['ma_component'] = ma_pred
        
        return predictions
    
    def _add_uncertainty_bounds(self, predictions, enhanced_params):
        """Add uncertainty bounds to predictions."""
        
        uncertainty_factor = enhanced_params['uncertainty_factor']
        
        predictions['lower_bound'] = predictions['predicted_quantity'] * (1 - uncertainty_factor)
        predictions['upper_bound'] = predictions['predicted_quantity'] * (1 + uncertainty_factor)
        predictions['confidence_level'] = 1 - uncertainty_factor
        predictions['uncertainty_factor'] = uncertainty_factor
        predictions['volatility_level'] = enhanced_params['volatility_level']
        
        # Ensure non-negative bounds
        predictions['lower_bound'] = np.maximum(predictions['lower_bound'], 0)
        
        return predictions
    
    def _add_basic_enhancements(self, base_predictions):
        """Add basic enhancements when no enhanced parameters are available."""
        
        enhanced_predictions = base_predictions.copy()
        
        # Find prediction column
        prediction_col = None
        for col in enhanced_predictions.columns:
            if 'predict' in col.lower() or 'forecast' in col.lower() or 'quantity' in col.lower():
                prediction_col = col
                break
        
        if prediction_col is None:
            return enhanced_predictions
        
        # Standardize column name
        if prediction_col != 'predicted_quantity':
            enhanced_predictions['predicted_quantity'] = enhanced_predictions[prediction_col]
        
        # Add basic uncertainty bounds (25%)
        uncertainty_factor = 0.25
        enhanced_predictions['lower_bound'] = enhanced_predictions['predicted_quantity'] * 0.75
        enhanced_predictions['upper_bound'] = enhanced_predictions['predicted_quantity'] * 1.25
        enhanced_predictions['confidence_level'] = 0.75
        enhanced_predictions['uncertainty_factor'] = uncertainty_factor
        enhanced_predictions['volatility_level'] = 'medium'
        enhanced_predictions['model_type'] = 'basic_enhanced_tci_fix'
        
        return enhanced_predictions

    def _create_fallback_predictions(self, customer, product, periods, start_date):
        """Create fallback predictions when base TCI-fix fails."""
        try:
            self.logger.info("Creating fallback predictions using historical data")

            # Load historical data
            success, message = self.data_manager.load_data_from_folder()
            if not success:
                return None

            key = f"{customer}_{product}"
            if key not in self.data_manager.data:
                return None

            data = self.data_manager.data[key].copy()
            target_col = 'Quantity'

            if len(data) < 6:
                return None

            # Create date range for predictions
            if start_date is None:
                last_date = pd.to_datetime(data['Date']).max()
                start_date = last_date + pd.DateOffset(months=1)
                start_date = start_date.replace(day=25)
            else:
                start_date = pd.to_datetime(start_date)

            # Generate prediction dates
            prediction_dates = []
            current_date = start_date
            for i in range(periods):
                prediction_dates.append(current_date)
                current_date = current_date + pd.DateOffset(months=1)

            # Simple fallback prediction logic
            recent_data = data[target_col].tail(12)  # Last 12 months

            # Calculate trend
            if len(recent_data) > 3:
                x = np.arange(len(recent_data))
                trend_coef = np.polyfit(x, recent_data, 1)[0]
            else:
                trend_coef = 0

            # Calculate seasonal pattern (simple monthly averages)
            data['month'] = pd.to_datetime(data['Date']).dt.month
            monthly_factors = data.groupby('month')[target_col].mean() / data[target_col].mean()

            # Generate predictions
            base_value = recent_data.mean()
            predictions = []

            for i, pred_date in enumerate(prediction_dates):
                month = pred_date.month
                seasonal_factor = monthly_factors.get(month, 1.0)

                # Apply trend and seasonality
                trend_adjustment = trend_coef * (i + 1)
                prediction = max(0, (base_value + trend_adjustment) * seasonal_factor)
                predictions.append(prediction)

            # Create DataFrame
            fallback_df = pd.DataFrame({
                'date': prediction_dates,
                'predicted_quantity': predictions
            })

            self.logger.info(f"Fallback predictions created: {len(fallback_df)} periods")
            return fallback_df

        except Exception as e:
            self.logger.error(f"Fallback prediction creation failed: {e}")
            return None

    def _get_enhanced_model_path(self, customer, product):
        """Get the file path for enhanced model parameters."""
        safe_customer = customer.replace("/", "_").replace("\\", "_")
        safe_product = product.replace("/", "_").replace("\\", "_")
        return f"models/enhanced_params_{safe_customer}_{safe_product}.pkl"
