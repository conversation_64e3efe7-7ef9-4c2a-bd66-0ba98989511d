#!/bin/bash

# Run TEP Causal Discovery Report Generator
# This script evaluates causal discovery methods on the TEP dataset and generates
# a comprehensive report on their performance.

# Set the TEP dataset path
TEP_DATASET_PATH="/home/<USER>/Ai/S3/tci/TEP_dataset/TEP_FaultFree_Training.RData"

# Set the output directory
OUTPUT_DIR="results/tep_causal_discovery"

# Create the output directory if it doesn't exist
mkdir -p $OUTPUT_DIR

# Run the evaluation with auto-detected methods
python examples/benchmarks/tep_causal_discovery_report.py \
    --dataset-path $TEP_DATASET_PATH \
    --sample-sizes 1000,5000,10000,20000 \
    --dimensions 10,20,54 \
    --alpha 0.1 \
    --sparsity-factor 0.1 \
    --output-dir $OUTPUT_DIR \
    --seed 42

echo "Evaluation completed. Results saved to $OUTPUT_DIR"
echo "Open $OUTPUT_DIR/tep_causal_discovery_report.html to view the report"
