"""
Demonstration of correlation-based causal discovery in TCI.

This script demonstrates how to use correlation-based causal discovery in the TCI framework
and how different correlation thresholds affect the resulting causal graphs.
"""

import os
import argparse
import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from tabulate import tabulate

from tci.core.agent import TCIAgent
from tci.environments.simple_env import SimpleEnvironment


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Correlation Causal Discovery Demo for TCI")
    
    parser.add_argument("--samples", type=int, default=500,
                        help="Number of samples to generate")
    
    parser.add_argument("--state-dim", type=int, default=8,
                        help="State dimension")
    
    parser.add_argument("--thresholds", type=float, nargs="+",
                        default=[0.1, 0.3, 0.5, 0.7, 0.9],
                        help="Correlation thresholds to compare")
    
    parser.add_argument("--output-dir", type=str, default="visualizations",
                        help="Directory to save visualizations")
    
    parser.add_argument("--verbose", action="store_true",
                        help="Print verbose output")
    
    return parser.parse_args()


def generate_synthetic_data(samples, state_dim, edge_probability=0.2):
    """Generate synthetic data with known causal structure."""
    # Create a random DAG with specified edge probability
    true_edges = []
    for i in range(state_dim):
        for j in range(state_dim):
            if i != j and np.random.random() < edge_probability:
                true_edges.append((i, j))
    
    # Create adjacency matrix
    adj_matrix = np.zeros((state_dim, state_dim))
    for i, j in true_edges:
        adj_matrix[i, j] = np.random.uniform(0.5, 1.0)  # Random edge weights
    
    # Generate data
    states = []
    next_states = []
    
    for _ in range(samples):
        # Generate state with some noise
        state = np.random.normal(0, 0.1, size=state_dim)
        
        # Generate next state based on causal structure
        next_state = np.zeros(state_dim)
        for j in range(state_dim):
            # Causal parents influence the variable
            for i in range(state_dim):
                next_state[j] += adj_matrix[i, j] * state[i]
            # Add noise
            next_state[j] += np.random.normal(0, 0.1)
        
        states.append(state)
        next_states.append(next_state)
    
    return states, next_states, true_edges


def correlation_causal_discovery(states, next_states, state_dim, threshold=0.3):
    """Perform correlation-based causal discovery."""
    # Calculate correlation matrix
    state_array = np.array(states)
    next_state_array = np.array(next_states)
    
    # Calculate correlation between each state variable and each next state variable
    corr_matrix = np.zeros((state_dim, state_dim))
    for i in range(state_dim):
        for j in range(state_dim):
            corr = np.corrcoef(state_array[:, i], next_state_array[:, j])[0, 1]
            corr_matrix[i, j] = corr
    
    # Apply threshold to get causal edges
    edges = []
    for i in range(state_dim):
        for j in range(state_dim):
            if abs(corr_matrix[i, j]) > threshold:
                edges.append((i, j))
    
    return corr_matrix, edges


def evaluate_causal_discovery(discovered_edges, true_edges):
    """Evaluate causal discovery performance."""
    # Convert to sets for easier comparison
    discovered_set = set(discovered_edges)
    true_set = set(true_edges)
    
    # Calculate metrics
    true_positives = len(discovered_set.intersection(true_set))
    false_positives = len(discovered_set - true_set)
    false_negatives = len(true_set - discovered_set)
    
    # Calculate precision, recall, and F1 score
    precision = true_positives / max(1, len(discovered_set))
    recall = true_positives / max(1, len(true_set))
    f1_score = 2 * precision * recall / max(1e-10, precision + recall)
    
    return {
        "precision": precision,
        "recall": recall,
        "f1_score": f1_score,
        "true_positives": true_positives,
        "false_positives": false_positives,
        "false_negatives": false_negatives
    }


def visualize_causal_graph(edges, title, output_path=None, state_dim=None):
    """Visualize a causal graph."""
    plt.figure(figsize=(10, 8))
    
    # Create a NetworkX graph
    G = nx.DiGraph()
    
    # Add nodes
    if state_dim is None:
        state_dim = max(max(i, j) for i, j in edges) + 1 if edges else 0
    
    for i in range(state_dim):
        G.add_node(i, label=f"X{i}")
    
    # Add edges
    for i, j in edges:
        G.add_edge(i, j)
    
    # Draw the graph
    pos = nx.spring_layout(G, seed=42)
    nx.draw_networkx_nodes(G, pos, node_size=500, node_color='lightblue')
    nx.draw_networkx_edges(G, pos, width=1.5, alpha=0.7, edge_color='gray',
                          arrowsize=20)
    nx.draw_networkx_labels(G, pos, labels={i: f"X{i}" for i in range(state_dim)})
    
    plt.title(title)
    plt.axis('off')
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
    
    return plt.gcf()


def visualize_correlation_matrix(corr_matrix, title, output_path=None):
    """Visualize a correlation matrix."""
    plt.figure(figsize=(10, 8))
    
    # Create heatmap
    plt.imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    
    # Add colorbar
    plt.colorbar(label='Correlation')
    
    # Add labels
    plt.title(title)
    plt.xlabel('Next State Variables')
    plt.ylabel('State Variables')
    
    # Add ticks
    state_dim = corr_matrix.shape[0]
    plt.xticks(range(state_dim), [f"X{i}" for i in range(state_dim)])
    plt.yticks(range(state_dim), [f"X{i}" for i in range(state_dim)])
    
    # Add grid
    plt.grid(False)
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
    
    return plt.gcf()


def run_correlation_causal_discovery_demo(args):
    """Run correlation-based causal discovery demonstration."""
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Generate synthetic data with known causal structure
    print(f"Generating {args.samples} synthetic data samples with state dimension {args.state_dim}...")
    states, next_states, true_edges = generate_synthetic_data(args.samples, args.state_dim)
    
    # Visualize true causal graph
    true_graph_path = os.path.join(args.output_dir, "true_causal_graph_corr.png")
    visualize_causal_graph(true_edges, "True Causal Graph", true_graph_path, args.state_dim)
    print(f"True causal graph saved to {true_graph_path}")
    print(f"True causal edges: {len(true_edges)}")
    
    # Calculate correlation matrix
    corr_matrix, _ = correlation_causal_discovery(states, next_states, args.state_dim, threshold=0)
    
    # Visualize correlation matrix
    corr_matrix_path = os.path.join(args.output_dir, "correlation_matrix.png")
    visualize_correlation_matrix(corr_matrix, "Correlation Matrix", corr_matrix_path)
    print(f"Correlation matrix saved to {corr_matrix_path}")
    
    # Run correlation-based causal discovery with different thresholds
    results = {}
    for threshold in args.thresholds:
        print(f"\nRunning correlation-based causal discovery with threshold: {threshold}")
        
        # Run causal discovery
        _, edges = correlation_causal_discovery(
            states=states,
            next_states=next_states,
            state_dim=args.state_dim,
            threshold=threshold
        )
        
        # Evaluate performance
        metrics = evaluate_causal_discovery(edges, true_edges)
        
        # Store results
        results[threshold] = {
            "edges": edges,
            "metrics": metrics
        }
        
        # Print results
        print(f"Found {len(edges)} causal edges with threshold {threshold}")
        print(f"Precision: {metrics['precision']:.4f}, Recall: {metrics['recall']:.4f}, F1: {metrics['f1_score']:.4f}")
        
        # Visualize causal graph
        graph_path = os.path.join(args.output_dir, f"corr_causal_graph_{threshold}.png")
        visualize_causal_graph(edges, f"Correlation Causal Graph (threshold={threshold})", graph_path, args.state_dim)
        print(f"Causal graph saved to {graph_path}")
    
    # Compare results
    print("\n=== Correlation Threshold Comparison ===")
    
    # Create comparison table
    table = []
    for threshold in args.thresholds:
        metrics = results[threshold]["metrics"]
        table.append([
            threshold,
            len(results[threshold]["edges"]),
            f"{metrics['precision']:.4f}",
            f"{metrics['recall']:.4f}",
            f"{metrics['f1_score']:.4f}"
        ])
    
    print(tabulate(table, headers=["Threshold", "Edges", "Precision", "Recall", "F1 Score"], tablefmt="grid"))
    
    # Create comparison plot
    plt.figure(figsize=(12, 8))
    
    # Plot precision, recall, and F1 score for each threshold
    thresholds = args.thresholds
    precision = [results[threshold]["metrics"]["precision"] for threshold in thresholds]
    recall = [results[threshold]["metrics"]["recall"] for threshold in thresholds]
    f1_score = [results[threshold]["metrics"]["f1_score"] for threshold in thresholds]
    num_edges = [len(results[threshold]["edges"]) for threshold in thresholds]
    
    # Create subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot metrics
    ax1.plot(thresholds, precision, 'o-', label='Precision')
    ax1.plot(thresholds, recall, 's-', label='Recall')
    ax1.plot(thresholds, f1_score, '^-', label='F1 Score')
    ax1.set_xlabel('Correlation Threshold')
    ax1.set_ylabel('Score')
    ax1.set_title('Metrics vs. Correlation Threshold')
    ax1.set_ylim(0, 1)
    ax1.grid(True)
    ax1.legend()
    
    # Plot number of edges
    ax2.plot(thresholds, num_edges, 'o-', color='green')
    ax2.axhline(y=len(true_edges), color='r', linestyle='--', label='True Edges')
    ax2.set_xlabel('Correlation Threshold')
    ax2.set_ylabel('Number of Edges')
    ax2.set_title('Number of Edges vs. Correlation Threshold')
    ax2.grid(True)
    ax2.legend()
    
    plt.tight_layout()
    
    # Save comparison plot
    comparison_path = os.path.join(args.output_dir, "correlation_causal_discovery_comparison.png")
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Comparison plot saved to {comparison_path}")
    
    return results


if __name__ == "__main__":
    args = parse_args()
    results = run_correlation_causal_discovery_demo(args)
