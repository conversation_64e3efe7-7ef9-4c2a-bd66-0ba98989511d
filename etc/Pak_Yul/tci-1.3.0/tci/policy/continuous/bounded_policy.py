"""
Bounded policy network for continuous action spaces.

This module provides a policy network for continuous action spaces with bounded outputs.
The policy uses tanh or sigmoid activation to bound the action values.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Dict, Any, Union, List

from tci.policy.continuous.gaussian_policy import GaussianPolicyNetwork


class BoundedPolicyNetwork(nn.Module):
    """
    Bounded policy network for continuous action spaces.
    
    This network outputs actions bounded to a specified range using tanh or sigmoid activation.
    It can be used for continuous control tasks with bounded action spaces in the TCI framework.
    
    Parameters
    ----------
    input_dim : int
        Dimension of the input state
    action_dim : int
        Dimension of the action space
    action_bounds : Tuple[Union[float, List[float]], Union[float, List[float]]], optional
        Lower and upper bounds for actions, by default (-1.0, 1.0)
    hidden_dim : int, optional
        Dimension of the hidden layers, by default 64
    n_hidden_layers : int, optional
        Number of hidden layers, by default 2
    activation : callable, optional
        Activation function, by default nn.ReLU()
    log_std_min : float, optional
        Minimum value for log standard deviation, by default -20
    log_std_max : float, optional
        Maximum value for log standard deviation, by default 2
    init_w : float, optional
        Initial weights scale, by default 3e-3
    """
    
    def __init__(
        self,
        input_dim: int,
        action_dim: int,
        action_bounds: Tuple[Union[float, List[float]], Union[float, List[float]]] = (-1.0, 1.0),
        hidden_dim: int = 64,
        n_hidden_layers: int = 2,
        activation: nn.Module = nn.ReLU(),
        log_std_min: float = -20,
        log_std_max: float = 2,
        init_w: float = 3e-3,
    ):
        super().__init__()
        
        # Validate parameters
        if input_dim <= 0:
            raise ValueError(f"input_dim must be positive, got {input_dim}")
        if action_dim <= 0:
            raise ValueError(f"action_dim must be positive, got {action_dim}")
        if hidden_dim <= 0:
            raise ValueError(f"hidden_dim must be positive, got {hidden_dim}")
        if n_hidden_layers <= 0:
            raise ValueError(f"n_hidden_layers must be positive, got {n_hidden_layers}")
        
        # Process action bounds
        self.action_dim = action_dim
        self.process_action_bounds(action_bounds)
        
        # Create the underlying Gaussian policy
        self.gaussian_policy = GaussianPolicyNetwork(
            input_dim=input_dim,
            action_dim=action_dim,
            hidden_dim=hidden_dim,
            n_hidden_layers=n_hidden_layers,
            activation=activation,
            log_std_min=log_std_min,
            log_std_max=log_std_max,
            init_w=init_w,
        )
    
    def process_action_bounds(self, action_bounds: Tuple[Union[float, List[float]], Union[float, List[float]]]):
        """
        Process action bounds into tensors.
        
        Parameters
        ----------
        action_bounds : Tuple[Union[float, List[float]], Union[float, List[float]]]
            Lower and upper bounds for actions
        """
        lower_bound, upper_bound = action_bounds
        
        # Convert scalar bounds to lists if needed
        if isinstance(lower_bound, (int, float)):
            lower_bound = [lower_bound] * self.action_dim
        if isinstance(upper_bound, (int, float)):
            upper_bound = [upper_bound] * self.action_dim
        
        # Validate bounds
        if len(lower_bound) != self.action_dim or len(upper_bound) != self.action_dim:
            raise ValueError(f"Action bounds must have length {self.action_dim}, got {len(lower_bound)} and {len(upper_bound)}")
        
        for i in range(self.action_dim):
            if lower_bound[i] >= upper_bound[i]:
                raise ValueError(f"Lower bound must be less than upper bound, got {lower_bound[i]} >= {upper_bound[i]} for dimension {i}")
        
        # Convert to tensors
        self.register_buffer('lower_bound', torch.tensor(lower_bound, dtype=torch.float32))
        self.register_buffer('upper_bound', torch.tensor(upper_bound, dtype=torch.float32))
        
        # Compute scaling factors
        self.register_buffer('scale', (self.upper_bound - self.lower_bound) / 2.0)
        self.register_buffer('center', (self.upper_bound + self.lower_bound) / 2.0)
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the network.
        
        Parameters
        ----------
        state : torch.Tensor
            Input state tensor of shape (batch_size, input_dim)
            
        Returns
        -------
        Tuple[torch.Tensor, torch.Tensor]
            Tuple of (mean, std) tensors, each of shape (batch_size, action_dim)
        """
        # Get raw mean and std from Gaussian policy
        raw_mean, raw_std = self.gaussian_policy(state)
        
        # Apply tanh to bound mean to [-1, 1]
        tanh_mean = torch.tanh(raw_mean)
        
        # Scale and shift to desired bounds
        scaled_mean = self.scale * tanh_mean + self.center
        
        return scaled_mean, raw_std
    
    def sample(self, state: torch.Tensor, deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Sample actions from the policy.
        
        Parameters
        ----------
        state : torch.Tensor
            Input state tensor of shape (batch_size, input_dim)
        deterministic : bool, optional
            Whether to sample deterministically (use mean), by default False
            
        Returns
        -------
        Tuple[torch.Tensor, torch.Tensor, torch.Tensor]
            Tuple of (action, log_prob, mean) tensors
        """
        # Get raw mean and std from Gaussian policy
        raw_mean, raw_std = self.gaussian_policy(state)
        
        if deterministic:
            # Use mean for deterministic action
            raw_action = raw_mean
            log_prob = torch.zeros_like(raw_mean[:, 0:1])
        else:
            # Sample from Gaussian distribution
            normal = torch.distributions.Normal(raw_mean, raw_std)
            raw_action = normal.rsample()  # rsample uses reparameterization trick
            
            # Compute log probability with change of variables formula
            # log_prob = log_prob_normal - log(1 - tanh(action)^2)
            log_prob_normal = normal.log_prob(raw_action).sum(dim=-1, keepdim=True)
            log_det_jacobian = 2 * (np.log(2) - raw_action - F.softplus(-2 * raw_action)).sum(dim=-1, keepdim=True)
            log_prob = log_prob_normal - log_det_jacobian
        
        # Apply tanh to bound action to [-1, 1]
        tanh_action = torch.tanh(raw_action)
        
        # Scale and shift to desired bounds
        scaled_action = self.scale * tanh_action + self.center
        scaled_mean = self.scale * torch.tanh(raw_mean) + self.center
        
        return scaled_action, log_prob, scaled_mean
    
    def log_prob(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        Compute log probability of action under the policy.
        
        Parameters
        ----------
        state : torch.Tensor
            Input state tensor of shape (batch_size, input_dim)
        action : torch.Tensor
            Action tensor of shape (batch_size, action_dim)
            
        Returns
        -------
        torch.Tensor
            Log probability tensor of shape (batch_size, 1)
        """
        # Rescale action from [lower_bound, upper_bound] to [-1, 1]
        normalized_action = (action - self.center) / self.scale
        normalized_action = torch.clamp(normalized_action, -0.999, 0.999)  # Avoid numerical issues
        
        # Apply inverse of tanh (arctanh)
        raw_action = torch.atanh(normalized_action)
        
        # Get raw mean and std from Gaussian policy
        raw_mean, raw_std = self.gaussian_policy(state)
        
        # Compute log probability with change of variables formula
        normal = torch.distributions.Normal(raw_mean, raw_std)
        log_prob_normal = normal.log_prob(raw_action).sum(dim=-1, keepdim=True)
        log_det_jacobian = 2 * (np.log(2) - raw_action - F.softplus(-2 * raw_action)).sum(dim=-1, keepdim=True)
        log_prob = log_prob_normal - log_det_jacobian
        
        return log_prob
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the policy to a dictionary for serialization.
        
        Returns
        -------
        Dict[str, Any]
            Dictionary representation of the policy
        """
        return {
            'type': 'BoundedPolicyNetwork',
            'action_dim': self.action_dim,
            'lower_bound': self.lower_bound.cpu().numpy().tolist(),
            'upper_bound': self.upper_bound.cpu().numpy().tolist(),
            'gaussian_policy': self.gaussian_policy.to_dict(),
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BoundedPolicyNetwork':
        """
        Create a policy from a dictionary.
        
        Parameters
        ----------
        data : Dict[str, Any]
            Dictionary representation of the policy
            
        Returns
        -------
        BoundedPolicyNetwork
            Reconstructed policy
        """
        # Create the Gaussian policy first
        gaussian_policy_data = data['gaussian_policy']
        
        # Create the bounded policy
        policy = cls(
            input_dim=gaussian_policy_data['input_dim'],
            action_dim=data['action_dim'],
            action_bounds=(data['lower_bound'], data['upper_bound']),
            hidden_dim=gaussian_policy_data.get('hidden_dim', 64),
            n_hidden_layers=gaussian_policy_data.get('n_hidden_layers', 2),
            log_std_min=gaussian_policy_data.get('log_std_min', -20),
            log_std_max=gaussian_policy_data.get('log_std_max', 2),
        )
        
        # Load the Gaussian policy state dict
        policy.gaussian_policy.load_state_dict(gaussian_policy_data['state_dict'])
        
        return policy
