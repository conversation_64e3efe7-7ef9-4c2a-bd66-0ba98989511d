"""
Example of using the Cyclic FCI algorithm for causal discovery.

This example demonstrates how to use the Cyclic FCI algorithm
to discover causal graphs that may contain cycles (feedback loops)
and latent confounders.
"""

import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
import sys
import os
import logging

# Add the parent directory to the path so we can import the tci package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tci.causal_discovery import CyclicFCI
from tci.visualization.cyclic_graph_viz import visualize_causal_graph_comparison

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_synthetic_cyclic_data(n_samples=1000, n_vars=5, latent_confounders=True, seed=42):
    """
    Generate synthetic data from a cyclic structural causal model with latent confounders.
    
    Args:
        n_samples: Number of samples
        n_vars: Number of variables
        latent_confounders: Whether to include latent confounders
        seed: Random seed
        
    Returns:
        data: Synthetic data
        true_graph: True causal graph
        true_latent_graph: True causal graph including latent confounders
    """
    np.random.seed(seed)
    
    # Create a specific adjacency matrix with a cycle
    W = np.zeros((n_vars, n_vars))
    # X0 -> X1
    W[0, 1] = 0.5
    # X1 -> X2
    W[1, 2] = 0.5
    # X2 -> X3
    W[2, 3] = 0.5
    # X3 -> X4
    W[3, 4] = 0.5
    # X4 -> X1 (creates a cycle)
    W[4, 1] = 0.3
    
    # Create true graph
    true_graph = nx.DiGraph()
    for i in range(n_vars):
        true_graph.add_node(i, name=f"X{i}")
    
    for i in range(n_vars):
        for j in range(n_vars):
            if W[i, j] != 0:
                true_graph.add_edge(i, j, weight=W[i, j])
    
    # Create true latent graph (including latent confounders)
    true_latent_graph = true_graph.copy()
    
    # Add latent confounders if requested
    latent_effects = {}
    if latent_confounders:
        # Add a latent confounder affecting X0 and X3
        latent_effects[(0, 3)] = 0.4
        
        # Add a latent confounder affecting X1 and X4
        latent_effects[(1, 4)] = 0.4
        
        # Add these to the true latent graph
        for (i, j), strength in latent_effects.items():
            # Add a latent node
            latent_idx = n_vars + len(latent_effects)
            true_latent_graph.add_node(latent_idx, name=f"L{latent_idx-n_vars}")
            
            # Add edges from latent to observed
            true_latent_graph.add_edge(latent_idx, i, weight=strength)
            true_latent_graph.add_edge(latent_idx, j, weight=strength)
    
    # Generate data
    X = np.zeros((n_samples, n_vars))
    
    # Generate exogenous noise
    U = np.random.normal(0, 1, size=(n_samples, n_vars))
    
    # Add effects of latent confounders
    for (i, j), strength in latent_effects.items():
        # Generate latent variable
        L = np.random.normal(0, 1, size=(n_samples, 1))
        
        # Add effect to both variables
        U[:, i] += strength * L.flatten()
        U[:, j] += strength * L.flatten()
    
    # Initialize with noise
    X = U.copy()
    
    # Iterate to approximate equilibrium
    for _ in range(10):
        X_new = U + X @ W
        X = X_new
    
    return X, true_graph, true_latent_graph

def evaluate_graph(true_graph, estimated_graph, latent_effects=None):
    """
    Evaluate the estimated graph against the true graph.
    
    Args:
        true_graph: True causal graph
        estimated_graph: Estimated causal graph
        latent_effects: Dictionary of latent effects (i, j) -> strength
        
    Returns:
        metrics: Dictionary of evaluation metrics
    """
    # Get adjacency matrices
    true_adj = nx.to_numpy_array(true_graph)
    est_adj = nx.to_numpy_array(estimated_graph)
    
    # Compute true positives, false positives, true negatives, false negatives
    tp = np.sum((true_adj != 0) & (est_adj != 0))
    fp = np.sum((true_adj == 0) & (est_adj != 0))
    tn = np.sum((true_adj == 0) & (est_adj == 0))
    fn = np.sum((true_adj != 0) & (est_adj == 0))
    
    # Compute metrics
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # Compute structural hamming distance (SHD)
    shd = np.sum(np.abs(true_adj - est_adj))
    
    # Check if cycles are correctly identified
    true_cycles = list(nx.simple_cycles(true_graph))
    est_cycles = list(nx.simple_cycles(estimated_graph))
    
    cycle_precision = len(est_cycles) / max(1, len(true_cycles))
    
    # Check if latent confounders are correctly identified
    latent_precision = 0
    if latent_effects:
        # Count correctly identified latent confounders
        correct_latent = 0
        for (i, j) in latent_effects:
            # In FCI, a latent confounder between i and j is represented by i <-> j
            # Check if there is a bidirected edge between i and j
            if (estimated_graph.has_edge(i, j) and estimated_graph.has_edge(j, i)):
                correct_latent += 1
        
        latent_precision = correct_latent / len(latent_effects) if len(latent_effects) > 0 else 0
    
    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'shd': shd,
        'true_cycles': len(true_cycles),
        'estimated_cycles': len(est_cycles),
        'cycle_precision': cycle_precision,
        'latent_precision': latent_precision
    }

def main():
    """Main function to run the example."""
    # Generate synthetic data
    logger.info("Generating synthetic data with cyclic causal structure and latent confounders")
    data, true_graph, true_latent_graph = generate_synthetic_cyclic_data(
        n_samples=1000, 
        n_vars=5, 
        latent_confounders=True
    )
    
    # Create variable names
    variable_names = [f"X{i}" for i in range(data.shape[1])]
    
    # Run Cyclic FCI algorithm
    logger.info("Running Cyclic FCI algorithm")
    cyclic_fci = CyclicFCI(
        allow_cycles=True,
        independence_test='fisher_z',
        significance_level=0.05,
        max_path_length=3,
        max_cond_set_size=3
    )
    
    # Discover causal graph
    estimated_graph = cyclic_fci.discover(data, variable_names=variable_names)
    
    # Evaluate the estimated graph
    latent_effects = {(0, 3): 0.4, (1, 4): 0.4}
    metrics = evaluate_graph(true_graph, estimated_graph, latent_effects)
    logger.info(f"Evaluation metrics: {metrics}")
    
    # Visualize the graphs
    visualize_causal_graph_comparison(
        true_graph,
        estimated_graph,
        true_feedback_loops=list(nx.simple_cycles(true_graph)),
        estimated_feedback_loops=cyclic_fci.feedback_loops,
        variable_names=variable_names,
        title="True vs. Estimated Causal Graph (Cyclic FCI)",
        output_file="cyclic_fci_comparison.png"
    )
    
    # Visualize the estimated graph with feedback loops highlighted
    cyclic_fci.visualize(output_file="cyclic_fci_graph.png")
    
    # Print discovered feedback loops
    logger.info(f"Discovered feedback loops: {cyclic_fci.feedback_loops}")
    
    # Convert to dictionary representation
    graph_dict = cyclic_fci.to_dict()
    logger.info(f"Graph dictionary: {graph_dict}")

if __name__ == "__main__":
    main()
